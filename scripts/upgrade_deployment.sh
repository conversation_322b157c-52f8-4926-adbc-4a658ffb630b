#!/bin/bash

# 🚀 装运卫士升级部署脚本
# 自动化完成所有升级任务，确保升级过程可控和可回滚

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Flutter
    if ! command -v flutter &> /dev/null; then
        log_error "Flutter未安装，请先安装Flutter"
        exit 1
    fi
    
    # 检查Dart
    if ! command -v dart &> /dev/null; then
        log_error "Dart未安装，请先安装Dart"
        exit 1
    fi
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        log_error "Git未安装，请先安装Git"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建备份
create_backup() {
    log_info "创建项目备份..."
    
    BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份关键文件
    cp -r lib/ "$BACKUP_DIR/"
    cp -r android/ "$BACKUP_DIR/"
    cp -r ios/ "$BACKUP_DIR/"
    cp pubspec.yaml "$BACKUP_DIR/"
    cp pubspec.lock "$BACKUP_DIR/"
    
    log_success "备份已创建: $BACKUP_DIR"
}

# 清理项目
clean_project() {
    log_info "清理项目..."
    
    flutter clean
    flutter pub get
    
    log_success "项目清理完成"
}

# 运行代码分析
run_analysis() {
    log_info "运行代码分析..."
    
    flutter analyze --no-fatal-infos
    
    log_success "代码分析完成"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    flutter test
    
    log_success "测试完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 构建Android版本
    flutter build apk --release
    
    # 构建iOS版本（如果在macOS上）
    if [[ "$OSTYPE" == "darwin"* ]]; then
        flutter build ios --release --no-codesign
    fi
    
    log_success "项目构建完成"
}

# 部署升级
deploy_upgrade() {
    log_info "开始部署升级..."
    
    # 1. 检查依赖
    check_dependencies
    
    # 2. 创建备份
    create_backup
    
    # 3. 清理项目
    clean_project
    
    # 4. 运行代码分析
    run_analysis
    
    # 5. 运行测试
    run_tests
    
    # 6. 构建项目
    build_project
    
    log_success "升级部署完成！"
}

# 回滚升级
rollback_upgrade() {
    log_info "开始回滚升级..."
    
    if [ -z "$1" ]; then
        log_error "请指定备份目录"
        exit 1
    fi
    
    BACKUP_DIR="$1"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_error "备份目录不存在: $BACKUP_DIR"
        exit 1
    fi
    
    # 恢复备份
    cp -r "$BACKUP_DIR/lib/" ./
    cp -r "$BACKUP_DIR/android/" ./
    cp -r "$BACKUP_DIR/ios/" ./
    cp "$BACKUP_DIR/pubspec.yaml" ./
    cp "$BACKUP_DIR/pubspec.lock" ./
    
    # 清理并重新构建
    clean_project
    run_analysis
    run_tests
    
    log_success "升级回滚完成！"
}

# 显示帮助信息
show_help() {
    echo "装运卫士升级部署脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  deploy    部署升级"
    echo "  rollback  回滚升级"
    echo "  test      运行测试"
    echo "  analyze   运行代码分析"
    echo "  clean     清理项目"
    echo "  help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 deploy"
    echo "  $0 rollback backup_20240101_120000"
    echo "  $0 test"
}

# 主函数
main() {
    case "$1" in
        "deploy")
            deploy_upgrade
            ;;
        "rollback")
            rollback_upgrade "$2"
            ;;
        "test")
            run_tests
            ;;
        "analyze")
            run_analysis
            ;;
        "clean")
            clean_project
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@" 