#!/bin/bash

# Script to download MediaPipe text recognition model
# This script should be run from the project root directory

# Create models directory if it doesn't exist
mkdir -p assets/models

# Download MediaPipe text recognition model
echo "Downloading MediaPipe text recognition model..."
curl -L "https://storage.googleapis.com/mediapipe-models/text_recognition_chinese_and_latin/v1/text_recognition.tflite" \
     -o "assets/models/text_recognition_model.tflite"

# Verify download
if [ -f "assets/models/text_recognition_model.tflite" ]; then
    echo "✅ Model downloaded successfully"
    
    # Calculate and verify checksum
    EXPECTED_CHECKSUM="e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
    if command -v sha256sum > /dev/null; then
        ACTUAL_CHECKSUM=$(sha256sum "assets/models/text_recognition_model.tflite" | cut -d' ' -f1)
    else
        ACTUAL_CHECKSUM=$(shasum -a 256 "assets/models/text_recognition_model.tflite" | cut -d' ' -f1)
    fi
    
    if [ "$ACTUAL_CHECKSUM" = "$EXPECTED_CHECKSUM" ]; then
        echo "✅ Checksum verification passed"
    else
        echo "❌ Checksum verification failed"
        echo "Expected: $EXPECTED_CHECKSUM"
        echo "Actual: $ACTUAL_CHECKSUM"
        exit 1
    fi
else
    echo "❌ Failed to download model"
    exit 1
fi

# Make the model file executable
chmod +x "assets/models/text_recognition_model.tflite"

echo "✅ Model setup complete" 