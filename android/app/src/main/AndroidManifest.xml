<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- 存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- Android 13+ 照片权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <!-- 后台运行权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <!-- 相机特性 -->
    <uses-feature android:name="android.hardware.camera" android:required="false" />
    <uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />
    
    <application
        android:label="装运卫士 - 工业级物流标签识别专家"
        android:name="${applicationName}"
        android:icon="@mipmap/launcher_icon"
        android:requestLegacyExternalStorage="true"
        android:enableOnBackInvokedCallback="true"
        android:allowBackup="true"
        android:largeHeap="true">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize"
            android:screenOrientation="portrait"
            android:keepScreenOn="true">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        
        <!-- 前台服务 - 用于后台识别任务 -->
        <service
            android:name=".RecognitionService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />
            
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
            
        <!-- 🔧 测试：临时简化ML Kit配置，排查输入冲突 -->
        <!-- ML Kit本地模型配置 - 防止运行时下载 -->
        <meta-data
            android:name="com.google.mlkit.vision.DEPENDENCIES"
            android:value="text" />
        <!-- 强制使用本地打包模型，不从网络下载 -->
        <meta-data
            android:name="com.google.firebase.ml.vision.DEPENDENCIES"
            android:value="text" />
            
        <!-- 🚀 启用Impeller渲染引擎 -->
        <meta-data
            android:name="io.flutter.embedding.android.EnableImpeller"
            android:value="true" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility?hl=en and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
        <!-- Share Plus 支持 - 分享到微信等应用 -->
        <intent>
            <action android:name="android.intent.action.SEND" />
            <data android:mimeType="*/*" />
        </intent>
        <intent>
            <action android:name="android.intent.action.SEND_MULTIPLE" />
            <data android:mimeType="*/*" />
        </intent>
        <!-- 微信支持 -->
        <package android:name="com.tencent.mm" />
        <!-- QQ支持 -->
        <package android:name="com.tencent.mobileqq" />
        <!-- 钉钉支持 -->
        <package android:name="com.alibaba.android.rimet" />
        <!-- 企业微信支持 -->
        <package android:name="com.tencent.wework" />
    </queries>
</manifest>
