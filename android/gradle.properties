# Gradle JVMéç½®
org.gradle.jvmargs=-Xmx2G
# org.gradle.java.home=D:\\Program Files\\Java\\jdk-21  # æ³¨éæç¡¬ç¼ç è·¯å¾ï¼ä½¿ç¨ç³»ç»JAVA_HOME

# Androidéç½®
android.useAndroidX=true
android.enableJetifier=true

# Gradleæ§è½ä¼å
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.daemon=true

# ç½ç»éç½®ä¼å
systemProp.http.keepAlive=true
systemProp.http.maxConnections=10
systemProp.http.connectionTimeout=300000
systemProp.http.socketTimeout=300000
systemProp.https.protocols=TLSv1.2,TLSv1.3

# ä»£çéç½®ï¼å¦æéè¦ï¼å»ææ³¨éå¹¶å¡«å¥ä»£çä¿¡æ¯ï¼
# systemProp.http.proxyHost=127.0.0.1
# systemProp.http.proxyPort=7890
# systemProp.https.proxyHost=127.0.0.1
# systemProp.https.proxyPort=7890

# Kotlinç¼è¯ä¼å - ç¦ç¨å¢éç¼è¯é¿åè·¯å¾å²çª
kotlin.code.style=official
kotlin.incremental=false
kotlin.incremental.android=false
