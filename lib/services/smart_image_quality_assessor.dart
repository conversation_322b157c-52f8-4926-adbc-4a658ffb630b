import 'dart:io';
import 'dart:math' as math;
import 'package:image/image.dart' as img;
import 'package:loadguard/services/logging_service.dart';

/// 智能图像质量评估器
/// 快速评估图像质量，为预处理策略选择提供依据
class SmartImageQualityAssessor {
  static final SmartImageQualityAssessor _instance =
      SmartImageQualityAssessor._internal();
  factory SmartImageQualityAssessor() => _instance;
  SmartImageQualityAssessor._internal();

  /// 快速质量评估（用于预处理策略选择）
  static Future<ImageQualityProfile> assessImageQuality(
      String imagePath) async {
    try {
      final startTime = DateTime.now();

      final bytes = await File(imagePath).readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        throw Exception('无法解码图像');
      }

      // 为了提高评估速度，将图像缩放到合适尺寸
      final resizedImage = _resizeForAnalysis(image);

      // 并行计算各项质量指标
      final futures = await Future.wait([
        _computeSharpnessScore(resizedImage),
        _computeExposureScore(resizedImage),
        _computeContrastScore(resizedImage),
        _computeNoiseScore(resizedImage),
        _computeReflectionScore(resizedImage),
      ]);

      final sharpness = futures[0];
      final exposure = futures[1];
      final contrast = futures[2];
      final noise = futures[3];
      final reflection = futures[4];

      // 计算综合评分
      final overallScore = _calculateOverallScore(
          sharpness, exposure, contrast, noise, reflection);

      // 分析图像特征
      final features = _analyzeImageFeatures(resizedImage);

      // 确定处理策略
      final strategy = _determineProcessingStrategy(overallScore, features);

      final assessmentTime =
          DateTime.now().difference(startTime).inMilliseconds;

      Log.i(
          '图像质量评估完成: 综合评分=${overallScore.toStringAsFixed(1)}, 策略=$strategy, 耗时=${assessmentTime}ms',
          tag: 'QualityAssessor');

      return ImageQualityProfile(
        overallScore: overallScore,
        sharpnessScore: sharpness,
        exposureScore: exposure,
        contrastScore: contrast,
        noiseScore: noise,
        reflectionScore: reflection,
        features: features,
        processingStrategy: strategy,
        assessmentTime: assessmentTime,
      );
    } catch (e) {
      Log.e('图像质量评估失败: $e', tag: 'QualityAssessor');
      // 返回默认配置，使用完整处理
      return ImageQualityProfile.defaultProfile();
    }
  }

  /// 为分析优化图像尺寸
  static img.Image _resizeForAnalysis(img.Image image) {
    const maxSize = 800; // 最大尺寸限制，提高分析速度

    if (image.width <= maxSize && image.height <= maxSize) {
      return image;
    }

    final aspectRatio = image.width / image.height;
    int newWidth, newHeight;

    if (aspectRatio > 1.0) {
      newWidth = maxSize;
      newHeight = (maxSize / aspectRatio).round();
    } else {
      newHeight = maxSize;
      newWidth = (maxSize * aspectRatio).round();
    }

    return img.copyResize(image, width: newWidth, height: newHeight);
  }

  /// 计算锐度评分（0-100）
  static Future<double> _computeSharpnessScore(img.Image image) async {
    final gray = img.grayscale(image);
    double totalVariance = 0.0;
    int pixelCount = 0;

    // 拉普拉斯算子
    const kernel = [0, -1, 0, -1, 4, -1, 0, -1, 0];

    for (int y = 1; y < gray.height - 1; y++) {
      for (int x = 1; x < gray.width - 1; x++) {
        double convSum = 0.0;
        int k = 0;

        for (int ky = -1; ky <= 1; ky++) {
          for (int kx = -1; kx <= 1; kx++) {
            final pixel = gray.getPixel(x + kx, y + ky);
            final luminance = img.getLuminance(pixel);
            convSum += luminance * kernel[k];
            k++;
          }
        }

        totalVariance += convSum * convSum;
        pixelCount++;
      }
    }

    final variance = pixelCount > 0 ? totalVariance / pixelCount : 0.0;
    return math.min(100.0, variance / 50.0); // 归一化到0-100
  }

  /// 计算曝光评分（0-100）
  static Future<double> _computeExposureScore(img.Image image) async {
    final histogram = List<int>.filled(256, 0);
    int totalPixels = 0;

    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final luminance = img.getLuminance(pixel);
        histogram[luminance.toInt().clamp(0, 255)]++;
        totalPixels++;
      }
    }

    // 计算过曝和欠曝像素比例
    final underexposed = histogram.sublist(0, 25).reduce((a, b) => a + b);
    final overexposed = histogram.sublist(230, 256).reduce((a, b) => a + b);

    final problemRatio = (underexposed + overexposed) / totalPixels;
    return math.max(0.0, (1.0 - problemRatio * 5) * 100); // 归一化到0-100
  }

  /// 计算对比度评分（0-100）
  static Future<double> _computeContrastScore(img.Image image) async {
    double sum = 0.0;
    double sumSquares = 0.0;
    int count = 0;

    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final luminance = img.getLuminance(pixel);
        sum += luminance;
        sumSquares += luminance * luminance;
        count++;
      }
    }

    if (count == 0) return 0.0;

    final mean = sum / count;
    final variance = (sumSquares / count) - (mean * mean);
    final stdDev = math.sqrt(variance);

    return math.min(100.0, stdDev / 2.0); // 归一化到0-100
  }

  /// 计算噪声评分（0-100）
  static Future<double> _computeNoiseScore(img.Image image) async {
    final gray = img.grayscale(image);
    double totalNoise = 0.0;
    int pixelCount = 0;

    // 使用高斯拉普拉斯算子检测噪声
    for (int y = 1; y < gray.height - 1; y++) {
      for (int x = 1; x < gray.width - 1; x++) {
        final center = img.getLuminance(gray.getPixel(x, y));
        double neighbors = 0.0;

        // 计算8邻域平均值
        for (int dy = -1; dy <= 1; dy++) {
          for (int dx = -1; dx <= 1; dx++) {
            if (dx == 0 && dy == 0) continue;
            final pixel = gray.getPixel(x + dx, y + dy);
            neighbors += img.getLuminance(pixel);
          }
        }
        neighbors /= 8.0;

        final noise = (center - neighbors).abs();
        totalNoise += noise;
        pixelCount++;
      }
    }

    final avgNoise = pixelCount > 0 ? totalNoise / pixelCount : 0.0;
    return math.max(0.0, (1.0 - avgNoise / 50.0) * 100); // 归一化到0-100
  }

  /// 计算反光评分（0-100）
  static Future<double> _computeReflectionScore(img.Image image) async {
    int totalPixels = 0;
    int brightPixels = 0;

    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final luminance = img.getLuminance(pixel);

        if (luminance > 240) {
          brightPixels++;
        }
        totalPixels++;
      }
    }

    final reflectionRatio = brightPixels / totalPixels;
    return math.max(0.0, (1.0 - reflectionRatio * 10) * 100); // 归一化到0-100
  }

  /// 计算综合评分
  static double _calculateOverallScore(double sharpness, double exposure,
      double contrast, double noise, double reflection) {
    // 权重分配
    const sharpnessWeight = 0.3;
    const exposureWeight = 0.25;
    const contrastWeight = 0.2;
    const noiseWeight = 0.15;
    const reflectionWeight = 0.1;

    return sharpness * sharpnessWeight +
        exposure * exposureWeight +
        contrast * contrastWeight +
        noise * noiseWeight +
        reflection * reflectionWeight;
  }

  /// 分析图像特征
  static Map<String, dynamic> _analyzeImageFeatures(img.Image image) {
    double totalLuminance = 0;
    int darkPixels = 0;
    int brightPixels = 0;
    int totalPixels = 0;

    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final luminance = img.getLuminance(pixel);

        totalLuminance += luminance;
        totalPixels++;

        if (luminance < 50) darkPixels++;
        if (luminance > 200) brightPixels++;
      }
    }

    final avgLuminance = totalLuminance / totalPixels;
    final darkRatio = darkPixels / totalPixels;
    final brightRatio = brightPixels / totalPixels;

    return {
      'isDark': avgLuminance < 80,
      'isBright': avgLuminance > 180,
      'hasReflections': brightRatio > 0.05,
      'darkRatio': darkRatio,
      'brightRatio': brightRatio,
      'avgLuminance': avgLuminance,
      'needsExposureAdjustment': avgLuminance < 60 || avgLuminance > 200,
      'needsContrastEnhancement': false, // 将在后续计算中确定
      'needsNoiseReduction': false, // 将在后续计算中确定
    };
  }

  /// 确定处理策略
  static ProcessingStrategy _determineProcessingStrategy(
      double overallScore, Map<String, dynamic> features) {
    if (overallScore >= 85) {
      // 高质量图像，跳过预处理或仅做轻微调整
      return ProcessingStrategy.minimal;
    } else if (overallScore >= 70) {
      // 中等质量图像，选择性处理
      return ProcessingStrategy.selective;
    } else if (overallScore >= 50) {
      // 低质量图像，标准处理
      return ProcessingStrategy.standard;
    } else {
      // 极低质量图像，完整处理
      return ProcessingStrategy.aggressive;
    }
  }
}

/// 图像质量档案
class ImageQualityProfile {
  final double overallScore;
  final double sharpnessScore;
  final double exposureScore;
  final double contrastScore;
  final double noiseScore;
  final double reflectionScore;
  final Map<String, dynamic> features;
  final ProcessingStrategy processingStrategy;
  final int assessmentTime;

  const ImageQualityProfile({
    required this.overallScore,
    required this.sharpnessScore,
    required this.exposureScore,
    required this.contrastScore,
    required this.noiseScore,
    required this.reflectionScore,
    required this.features,
    required this.processingStrategy,
    required this.assessmentTime,
  });

  /// 默认配置（用于评估失败时）
  factory ImageQualityProfile.defaultProfile() {
    return ImageQualityProfile(
      overallScore: 50.0,
      sharpnessScore: 50.0,
      exposureScore: 50.0,
      contrastScore: 50.0,
      noiseScore: 50.0,
      reflectionScore: 50.0,
      features: {
        'isDark': false,
        'isBright': false,
        'hasReflections': false,
        'needsExposureAdjustment': true,
        'needsContrastEnhancement': true,
        'needsNoiseReduction': true,
      },
      processingStrategy: ProcessingStrategy.standard,
      assessmentTime: 0,
    );
  }

  /// 是否需要预处理
  bool get needsPreprocessing =>
      processingStrategy != ProcessingStrategy.minimal;

  /// 获取质量等级描述
  String get qualityLevel {
    if (overallScore >= 85) return '优秀';
    if (overallScore >= 70) return '良好';
    if (overallScore >= 50) return '一般';
    return '较差';
  }
}

/// 处理策略枚举
enum ProcessingStrategy {
  minimal, // 最小处理：跳过预处理或仅做轻微调整
  selective, // 选择性处理：仅应用必要的算法
  standard, // 标准处理：应用常规算法组合
  aggressive, // 激进处理：应用全套算法
}
