import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:image/image.dart' as img;

/// 图像预处理增强服务
/// 针对工业标签识别的真实图像处理算法
class ImagePreprocessor {
  /// 自适应图像增强处理
  static Future<File> enhanceImageForOCR(
      File inputFile, String outputPath) async {
    try {
      final bytes = await inputFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        throw Exception('无法解码图像');
      }

      // 处理流程：曝光补偿 → 对比度增强 → 锐化 → 去噪
      var enhanced = _adjustExposure(image);
      enhanced = _enhanceContrast(enhanced);
      enhanced = _sharpenImage(enhanced);
      enhanced = _reduceNoise(enhanced);

      // 保存处理后的图像
      final outputFile = File(outputPath);
      await outputFile.writeAsBytes(img.encodeJpg(enhanced, quality: 95));

      return outputFile;
    } catch (e) {
      throw Exception('图像预处理失败: $e');
    }
  }

  /// 透视校正（检测矩形区域并校正）
  static Future<File> correctPerspective(
      File inputFile, String outputPath) async {
    try {
      final bytes = await inputFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        throw Exception('无法解码图像');
      }

      // 检测文档边界
      final corners = _detectDocumentCorners(image);

      if (corners.length == 4) {
        // 执行透视变换
        final corrected = _performPerspectiveTransform(image, corners);

        final outputFile = File(outputPath);
        await outputFile.writeAsBytes(img.encodeJpg(corrected, quality: 95));
        return outputFile;
      } else {
        // 如果检测失败，返回原图
        return inputFile;
      }
    } catch (e) {
      return inputFile; // 失败时返回原图
    }
  }

  /// 自适应曝光补偿
  static img.Image _adjustExposure(img.Image image) {
    // 计算图像亮度直方图
    final histogram = List<int>.filled(256, 0);
    int totalPixels = 0;

    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final luminance = img.getLuminance(pixel);
        histogram[luminance.toInt().clamp(0, 255)]++;
        totalPixels++;
      }
    }

    // 计算累积分布
    final cdf = List<double>.filled(256, 0);
    cdf[0] = histogram[0] / totalPixels;
    for (int i = 1; i < 256; i++) {
      cdf[i] = cdf[i - 1] + histogram[i] / totalPixels;
    }

    // 应用直方图均衡化
    final result = img.Image.from(image);
    for (int y = 0; y < result.height; y++) {
      for (int x = 0; x < result.width; x++) {
        final pixel = result.getPixel(x, y);
        final r = pixel.r;
        final g = pixel.g;
        final b = pixel.b;

        // 对每个通道应用直方图均衡化
        final newR = (cdf[r.toInt()] * 255).round().clamp(0, 255);
        final newG = (cdf[g.toInt()] * 255).round().clamp(0, 255);
        final newB = (cdf[b.toInt()] * 255).round().clamp(0, 255);

        result.setPixel(x, y, img.ColorRgb8(newR, newG, newB));
      }
    }

    return result;
  }

  /// 对比度限制自适应直方图均衡化 (CLAHE)
  static img.Image _enhanceContrast(img.Image image) {
    const int tileSize = 64; // 瓦片大小
    const double clipLimit = 2.0; // 对比度限制

    final result = img.Image.from(image);

    // 分块处理
    for (int tileY = 0; tileY < image.height; tileY += tileSize) {
      for (int tileX = 0; tileX < image.width; tileX += tileSize) {
        final endX = min(tileX + tileSize, image.width);
        final endY = min(tileY + tileSize, image.height);

        _enhanceContrastTile(result, tileX, tileY, endX, endY, clipLimit);
      }
    }

    return result;
  }

  /// 处理单个瓦片的对比度增强
  static void _enhanceContrastTile(img.Image image, int startX, int startY,
      int endX, int endY, double clipLimit) {
    // 计算瓦片直方图
    final histogram = List<int>.filled(256, 0);
    int pixelCount = 0;

    for (int y = startY; y < endY; y++) {
      for (int x = startX; x < endX; x++) {
        final pixel = image.getPixel(x, y);
        final luminance = img.getLuminance(pixel);
        histogram[luminance.toInt().clamp(0, 255)]++;
        pixelCount++;
      }
    }

    // 对比度限制
    final clipValue = (clipLimit * pixelCount / 256).round();
    int excess = 0;

    for (int i = 0; i < 256; i++) {
      if (histogram[i] > clipValue) {
        excess += histogram[i] - clipValue;
        histogram[i] = clipValue;
      }
    }

    // 重新分布超出部分
    final redistribution = excess ~/ 256;
    for (int i = 0; i < 256; i++) {
      histogram[i] += redistribution;
    }

    // 计算映射表
    final mapping = List<int>.filled(256, 0);
    int sum = 0;
    for (int i = 0; i < 256; i++) {
      sum += histogram[i];
      mapping[i] = (sum * 255.0 / pixelCount).round().clamp(0, 255);
    }

    // 应用映射
    for (int y = startY; y < endY; y++) {
      for (int x = startX; x < endX; x++) {
        final pixel = image.getPixel(x, y);
        final r = pixel.r;
        final g = pixel.g;
        final b = pixel.b;

        final newR = mapping[r.toInt()];
        final newG = mapping[g.toInt()];
        final newB = mapping[b.toInt()];

        image.setPixel(x, y, img.ColorRgb8(newR, newG, newB));
      }
    }
  }

  /// 图像锐化（Unsharp Mask）
  static img.Image _sharpenImage(img.Image image) {
    // 创建高斯模糊版本
    final blurred = img.gaussianBlur(image, radius: 1);
    final result = img.Image.from(image);

    const double amount = 1.5; // 锐化强度

    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final original = image.getPixel(x, y);
        final blur = blurred.getPixel(x, y);

        final origR = original.r;
        final origG = original.g;
        final origB = original.b;

        final blurR = blur.r;
        final blurG = blur.g;
        final blurB = blur.b;

        // Unsharp Mask公式: Original + Amount * (Original - Blur)
        final sharpR = (origR + amount * (origR - blurR)).round().clamp(0, 255);
        final sharpG = (origG + amount * (origG - blurG)).round().clamp(0, 255);
        final sharpB = (origB + amount * (origB - blurB)).round().clamp(0, 255);

        result.setPixel(x, y, img.ColorRgb8(sharpR, sharpG, sharpB));
      }
    }

    return result;
  }

  /// 自适应去噪（双边滤波）
  static img.Image _reduceNoise(img.Image image) {
    const int radius = 3;
    const double sigmaColor = 20.0;
    const double sigmaSpace = 20.0;

    final result = img.Image.from(image);

    for (int y = radius; y < image.height - radius; y++) {
      for (int x = radius; x < image.width - radius; x++) {
        final centerPixel = image.getPixel(x, y);
        final centerR = centerPixel.r;
        final centerG = centerPixel.g;
        final centerB = centerPixel.b;

        double weightSum = 0.0;
        double r = 0.0, g = 0.0, b = 0.0;

        for (int dy = -radius; dy <= radius; dy++) {
          for (int dx = -radius; dx <= radius; dx++) {
            final neighborPixel = image.getPixel(x + dx, y + dy);
            final neighborR = neighborPixel.r;
            final neighborG = neighborPixel.g;
            final neighborB = neighborPixel.b;

            // 空间距离权重
            final spatialDist = sqrt(dx * dx + dy * dy);
            final spatialWeight = exp(
                -(spatialDist * spatialDist) / (2 * sigmaSpace * sigmaSpace));

            // 颜色距离权重
            final colorDist = sqrt(pow(centerR - neighborR, 2) +
                pow(centerG - neighborG, 2) +
                pow(centerB - neighborB, 2));
            final colorWeight =
                exp(-(colorDist * colorDist) / (2 * sigmaColor * sigmaColor));

            final weight = spatialWeight * colorWeight;
            weightSum += weight;

            r += neighborR * weight;
            g += neighborG * weight;
            b += neighborB * weight;
          }
        }

        if (weightSum > 0) {
          final newR = (r / weightSum).round().clamp(0, 255);
          final newG = (g / weightSum).round().clamp(0, 255);
          final newB = (b / weightSum).round().clamp(0, 255);

          result.setPixel(x, y, img.ColorRgb8(newR, newG, newB));
        }
      }
    }

    return result;
  }

  /// 检测文档四个角点
  static List<Point<int>> _detectDocumentCorners(img.Image image) {
    // 转换为灰度
    final gray = img.grayscale(image);

    // 边缘检测
    final edges = _cannyEdgeDetection(gray);

    // 轮廓检测
    final contours = _findContours(edges);

    // 寻找最大的四边形轮廓
    return _findLargestQuadrilateral(contours, image.width, image.height);
  }

  /// Canny边缘检测
  static img.Image _cannyEdgeDetection(img.Image gray) {
    // 简化的Canny实现
    final blurred = img.gaussianBlur(gray, radius: 1);

    // Sobel梯度计算
    final gradX = img.Image(width: blurred.width, height: blurred.height);
    final gradY = img.Image(width: blurred.width, height: blurred.height);

    for (int y = 1; y < blurred.height - 1; y++) {
      for (int x = 1; x < blurred.width - 1; x++) {
        // Sobel X
        final gx = -1 * img.getLuminance(blurred.getPixel(x - 1, y - 1)) +
            1 * img.getLuminance(blurred.getPixel(x + 1, y - 1)) +
            -2 * img.getLuminance(blurred.getPixel(x - 1, y)) +
            2 * img.getLuminance(blurred.getPixel(x + 1, y)) +
            -1 * img.getLuminance(blurred.getPixel(x - 1, y + 1)) +
            1 * img.getLuminance(blurred.getPixel(x + 1, y + 1));

        // Sobel Y
        final gy = -1 * img.getLuminance(blurred.getPixel(x - 1, y - 1)) +
            -2 * img.getLuminance(blurred.getPixel(x, y - 1)) +
            -1 * img.getLuminance(blurred.getPixel(x + 1, y - 1)) +
            1 * img.getLuminance(blurred.getPixel(x - 1, y + 1)) +
            2 * img.getLuminance(blurred.getPixel(x, y + 1)) +
            1 * img.getLuminance(blurred.getPixel(x + 1, y + 1));

        final magnitude = sqrt(gx * gx + gy * gy);
        final intensity = magnitude.clamp(0, 255).toInt();

        gradX.setPixel(x, y, img.ColorRgb8(intensity, intensity, intensity));
        gradY.setPixel(x, y, img.ColorRgb8(intensity, intensity, intensity));
      }
    }

    // 非极大值抑制和双阈值处理（简化版）
    final result = img.Image(width: gray.width, height: gray.height);
    const lowThreshold = 50;
    const highThreshold = 150;

    for (int y = 0; y < gray.height; y++) {
      for (int x = 0; x < gray.width; x++) {
        final intensity = img.getLuminance(gradX.getPixel(x, y));

        int output = 0;
        if (intensity > highThreshold) {
          output = 255;
        } else if (intensity > lowThreshold) {
          output = 128;
        }

        result.setPixel(x, y, img.ColorRgb8(output, output, output));
      }
    }

    return result;
  }

  /// 查找轮廓
  static List<List<Point<int>>> _findContours(img.Image edges) {
    final contours = <List<Point<int>>>[];
    final visited =
        List.generate(edges.height, (_) => List.filled(edges.width, false));

    for (int y = 0; y < edges.height; y++) {
      for (int x = 0; x < edges.width; x++) {
        if (!visited[y][x] && img.getLuminance(edges.getPixel(x, y)) > 128) {
          final contour = _traceContour(edges, x, y, visited);
          if (contour.length > 10) {
            // 过滤小轮廓
            contours.add(contour);
          }
        }
      }
    }

    return contours;
  }

  /// 追踪轮廓
  static List<Point<int>> _traceContour(
      img.Image edges, int startX, int startY, List<List<bool>> visited) {
    final contour = <Point<int>>[];
    final stack = <Point<int>>[Point(startX, startY)];

    while (stack.isNotEmpty) {
      final point = stack.removeLast();
      final x = point.x;
      final y = point.y;

      if (x < 0 ||
          x >= edges.width ||
          y < 0 ||
          y >= edges.height ||
          visited[y][x] ||
          img.getLuminance(edges.getPixel(x, y)) <= 128) {
        continue;
      }

      visited[y][x] = true;
      contour.add(point);

      // 8邻域搜索
      for (int dy = -1; dy <= 1; dy++) {
        for (int dx = -1; dx <= 1; dx++) {
          if (dx != 0 || dy != 0) {
            stack.add(Point(x + dx, y + dy));
          }
        }
      }
    }

    return contour;
  }

  /// 查找最大四边形
  static List<Point<int>> _findLargestQuadrilateral(
      List<List<Point<int>>> contours, int width, int height) {
    List<Point<int>> bestQuad = [];
    double maxArea = 0;

    for (final contour in contours) {
      // Douglas-Peucker算法简化轮廓
      final simplified =
          _simplifyContour(contour, 0.02 * _contourPerimeter(contour));

      if (simplified.length == 4) {
        final area = _quadrilateralArea(simplified);
        if (area > maxArea && area > width * height * 0.1) {
          maxArea = area;
          bestQuad = simplified;
        }
      }
    }

    return bestQuad;
  }

  /// 简化轮廓（Douglas-Peucker算法）
  static List<Point<int>> _simplifyContour(
      List<Point<int>> contour, double epsilon) {
    if (contour.length < 3) return contour;

    // 找到距离最远的点
    double maxDistance = 0;
    int maxIndex = 0;

    final start = contour.first;
    final end = contour.last;

    for (int i = 1; i < contour.length - 1; i++) {
      final distance = _pointToLineDistance(contour[i], start, end);
      if (distance > maxDistance) {
        maxDistance = distance;
        maxIndex = i;
      }
    }

    if (maxDistance > epsilon) {
      // 递归简化
      final left = _simplifyContour(contour.sublist(0, maxIndex + 1), epsilon);
      final right = _simplifyContour(contour.sublist(maxIndex), epsilon);

      return [...left.sublist(0, left.length - 1), ...right];
    } else {
      return [start, end];
    }
  }

  /// 点到直线距离
  static double _pointToLineDistance(
      Point<int> point, Point<int> lineStart, Point<int> lineEnd) {
    final a = lineEnd.y - lineStart.y;
    final b = lineStart.x - lineEnd.x;
    final c = lineEnd.x * lineStart.y - lineStart.x * lineEnd.y;

    return (a * point.x + b * point.y + c).abs() / sqrt(a * a + b * b);
  }

  /// 计算轮廓周长
  static double _contourPerimeter(List<Point<int>> contour) {
    double perimeter = 0;
    for (int i = 0; i < contour.length; i++) {
      final p1 = contour[i];
      final p2 = contour[(i + 1) % contour.length];
      perimeter += sqrt(pow(p2.x - p1.x, 2) + pow(p2.y - p1.y, 2));
    }
    return perimeter;
  }

  /// 计算四边形面积
  static double _quadrilateralArea(List<Point<int>> quad) {
    if (quad.length != 4) return 0;

    // 使用鞋带公式
    double area = 0;
    for (int i = 0; i < 4; i++) {
      final j = (i + 1) % 4;
      area += quad[i].x * quad[j].y;
      area -= quad[j].x * quad[i].y;
    }
    return area.abs() / 2;
  }

  /// 执行透视变换
  static img.Image _performPerspectiveTransform(
      img.Image image, List<Point<int>> corners) {
    // 简化的透视变换实现
    // 实际应用中建议使用更精确的矩阵变换

    // 排序角点：左上、右上、右下、左下
    corners.sort((a, b) {
      final sumA = a.x + a.y;
      final sumB = b.x + b.y;
      return sumA.compareTo(sumB);
    });

    final topLeft = corners[0];
    final bottomRight = corners[3];

    // 计算目标尺寸
    final width = (bottomRight.x - topLeft.x).abs();
    final height = (bottomRight.y - topLeft.y).abs();

    if (width <= 0 || height <= 0) return image;

    final result = img.Image(width: width, height: height);

    // 简单的仿射变换（实际需要更复杂的透视变换）
    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final srcX = topLeft.x + x;
        final srcY = topLeft.y + y;

        if (srcX >= 0 &&
            srcX < image.width &&
            srcY >= 0 &&
            srcY < image.height) {
          result.setPixel(x, y, image.getPixel(srcX, srcY));
        }
      }
    }

    return result;
  }
}
