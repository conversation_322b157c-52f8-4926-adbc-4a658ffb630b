import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;

/// 🧠 内存优化服务 - 图片资源管理
///
/// 智能管理图片内存，及时释放资源
/// 降低内存占用，提升应用性能
class MemoryOptimizationService {
  static final MemoryOptimizationService _instance =
      MemoryOptimizationService._internal();
  factory MemoryOptimizationService() => _instance;
  MemoryOptimizationService._internal();

  // 内存管理配置
  static const int _maxCachedImages = 20;
  static const int _maxMemoryUsageMB = 100;
  static const int _imageQualityForThumbnail = 60;
  static const int _thumbnailMaxSize = 300;

  // 图片缓存
  final Map<String, Uint8List> _imageCache = {};
  final Map<String, Uint8List> _thumbnailCache = {};
  final Map<String, DateTime> _lastAccessTime = {};
  final Map<String, int> _imageSizes = {};

  Timer? _memoryCleanupTimer;
  bool _isInitialized = false;
  int _currentMemoryUsage = 0;

  /// 初始化内存优化服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 启动内存清理定时器
      _memoryCleanupTimer = Timer.periodic(
        const Duration(minutes: 1),
        (_) => _performMemoryCleanup(),
      );

      _isInitialized = true;
    } catch (e) {
      // 即使初始化失败，也标记为已初始化，避免重复尝试
      _isInitialized = true;
    }
  }

  /// 📸 加载和优化图片
  Future<Uint8List> loadImageOptimized(
    String imagePath, {
    int? maxWidth,
    int? maxHeight,
    int quality = 85,
    bool createThumbnail = true,
  }) async {
    final cacheKey = _generateCacheKey(imagePath, maxWidth, maxHeight, quality);

    // 检查缓存
    if (_imageCache.containsKey(cacheKey)) {
      _lastAccessTime[cacheKey] = DateTime.now();
      return _imageCache[cacheKey]!;
    }

    try {
      Uint8List originalBytes;

      // Web环境和移动端不同的文件加载方式
      if (kIsWeb) {
        // Web环境：返回空数据，避免错误
        return Uint8List(0);
      } else {
        // 移动端：使用File操作
        final file = File(imagePath);
        originalBytes = await file.readAsBytes();
      }

      final originalImage = img.decodeImage(originalBytes);

      if (originalImage == null) {
        throw Exception('无法解码图片: $imagePath');
      }

      // 根据需求调整图片
      var processedImage = originalImage;

      // 调整大小
      if (maxWidth != null || maxHeight != null) {
        processedImage = _resizeImageIntelligently(
          processedImage,
          maxWidth ?? processedImage.width,
          maxHeight ?? processedImage.height,
        );
      }

      // 编码优化后的图片
      final optimizedBytes = img.encodeJpg(processedImage, quality: quality);
      final optimizedData = Uint8List.fromList(optimizedBytes);

      // 检查内存使用情况
      await _ensureMemorySpace(optimizedData.length);

      // 添加到缓存
      _imageCache[cacheKey] = optimizedData;
      _lastAccessTime[cacheKey] = DateTime.now();
      _imageSizes[cacheKey] = optimizedData.length;
      _currentMemoryUsage += optimizedData.length;

      // 创建缩略图
      if (createThumbnail && !kIsWeb) {
        await _createThumbnail(imagePath, originalImage);
      }

      return optimizedData;
    } catch (e) {
      // Web环境下返回空的占位数据，避免崩溃
      if (kIsWeb) {
        return Uint8List(0);
      }
      rethrow;
    }
  }

  /// 📸 Web环境专用：从Uint8List加载图片
  Future<Uint8List> loadImageFromBytes(
    Uint8List imageBytes,
    String cacheKey, {
    int? maxWidth,
    int? maxHeight,
    int quality = 85,
  }) async {
    final fullCacheKey =
        _generateCacheKey(cacheKey, maxWidth, maxHeight, quality);

    // 检查缓存
    if (_imageCache.containsKey(fullCacheKey)) {
      _lastAccessTime[fullCacheKey] = DateTime.now();
      return _imageCache[fullCacheKey]!;
    }

    try {
      final originalImage = img.decodeImage(imageBytes);

      if (originalImage == null) {
        throw Exception('无法解码图片数据');
      }

      // 根据需求调整图片
      var processedImage = originalImage;

      // 调整大小
      if (maxWidth != null || maxHeight != null) {
        processedImage = _resizeImageIntelligently(
          processedImage,
          maxWidth ?? processedImage.width,
          maxHeight ?? processedImage.height,
        );
      }

      // 编码优化后的图片
      final optimizedBytes = img.encodeJpg(processedImage, quality: quality);
      final optimizedData = Uint8List.fromList(optimizedBytes);

      // 检查内存使用情况
      await _ensureMemorySpace(optimizedData.length);

      // 添加到缓存
      _imageCache[fullCacheKey] = optimizedData;
      _lastAccessTime[fullCacheKey] = DateTime.now();
      _imageSizes[fullCacheKey] = optimizedData.length;
      _currentMemoryUsage += optimizedData.length;

      return optimizedData;
    } catch (e) {
      return imageBytes; // 返回原始数据
    }
  }

  /// 🖼️ 获取缩略图
  Future<Uint8List?> getThumbnail(String imagePath) async {
    final thumbnailKey = 'thumb_$imagePath';

    if (_thumbnailCache.containsKey(thumbnailKey)) {
      _lastAccessTime[thumbnailKey] = DateTime.now();
      return _thumbnailCache[thumbnailKey];
    }

    // 如果没有缓存，尝试创建
    try {
      final file = File(imagePath);
      final originalBytes = await file.readAsBytes();
      final originalImage = img.decodeImage(originalBytes);

      if (originalImage != null) {
        await _createThumbnail(imagePath, originalImage);
        return _thumbnailCache[thumbnailKey];
      }
    } catch (e) {
    }

    return null;
  }

  /// 🖼️ 创建缩略图
  Future<void> _createThumbnail(
      String imagePath, img.Image originalImage) async {
    final thumbnailKey = 'thumb_$imagePath';

    if (_thumbnailCache.containsKey(thumbnailKey)) return;

    try {
      // 创建缩略图
      final thumbnail = _resizeImageIntelligently(
        originalImage,
        _thumbnailMaxSize,
        _thumbnailMaxSize,
      );

      final thumbnailBytes =
          img.encodeJpg(thumbnail, quality: _imageQualityForThumbnail);
      final thumbnailData = Uint8List.fromList(thumbnailBytes);

      // 添加到缓存
      _thumbnailCache[thumbnailKey] = thumbnailData;
      _lastAccessTime[thumbnailKey] = DateTime.now();
      _currentMemoryUsage += thumbnailData.length;

    } catch (e) {
    }
  }

  /// 📏 智能调整图片大小
  img.Image _resizeImageIntelligently(
      img.Image image, int maxWidth, int maxHeight) {
    final originalWidth = image.width;
    final originalHeight = image.height;

    // 如果图片已经小于目标尺寸，直接返回
    if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
      return image;
    }

    // 计算缩放比例，保持宽高比
    final widthRatio = maxWidth / originalWidth;
    final heightRatio = maxHeight / originalHeight;
    final scale = widthRatio < heightRatio ? widthRatio : heightRatio;

    final newWidth = (originalWidth * scale).round();
    final newHeight = (originalHeight * scale).round();

    return img.copyResize(
      image,
      width: newWidth,
      height: newHeight,
      interpolation: img.Interpolation.cubic,
    );
  }

  /// 💾 确保内存空间
  Future<void> _ensureMemorySpace(int requiredBytes) async {
    final maxBytes = _maxMemoryUsageMB * 1024 * 1024;

    while (_currentMemoryUsage + requiredBytes > maxBytes &&
        _imageCache.isNotEmpty) {
      await _removeLeastRecentlyUsedImage();
    }
  }

  /// 🗑️ 移除最近最少使用的图片
  Future<void> _removeLeastRecentlyUsedImage() async {
    if (_lastAccessTime.isEmpty) return;

    final oldestEntry = _lastAccessTime.entries
        .reduce((a, b) => a.value.isBefore(b.value) ? a : b);

    await _removeImageFromCache(oldestEntry.key);
  }

  /// 🗑️ 从缓存中移除图片
  Future<void> _removeImageFromCache(String cacheKey) async {
    final imageSize = _imageSizes[cacheKey] ?? 0;

    _imageCache.remove(cacheKey);
    _thumbnailCache.remove(cacheKey);
    _lastAccessTime.remove(cacheKey);
    _imageSizes.remove(cacheKey);

    _currentMemoryUsage -= imageSize;

  }

  /// 🧹 执行内存清理
  Future<void> _performMemoryCleanup() async {
    final now = DateTime.now();
    final keysToRemove = <String>[];

    // 查找超过5分钟未访问的图片
    _lastAccessTime.forEach((key, lastAccess) {
      if (now.difference(lastAccess).inMinutes > 5) {
        keysToRemove.add(key);
      }
    });

    // 移除旧图片
    for (final key in keysToRemove) {
      await _removeImageFromCache(key);
    }

    // 如果内存使用仍然过高，移除更多图片
    while (_currentMemoryUsage > _maxMemoryUsageMB * 1024 * 1024 * 0.8 &&
        _imageCache.isNotEmpty) {
      await _removeLeastRecentlyUsedImage();
    }

    // 强制垃圾回收
    if (keysToRemove.isNotEmpty) {
    }
  }

  /// 🔧 预加载图片（异步）
  Future<void> preloadImages(List<String> imagePaths) async {
    final futures = imagePaths.map((path) async {
      try {
        await loadImageOptimized(path, createThumbnail: true);
      } catch (e) {
      }
    });

    await Future.wait(futures);
  }

  /// 🧹 清理指定图片
  Future<void> clearImage(String imagePath) async {
    final keysToRemove =
        _imageCache.keys.where((key) => key.contains(imagePath)).toList();

    for (final key in keysToRemove) {
      await _removeImageFromCache(key);
    }
  }

  /// 🧹 清理所有缓存
  Future<void> clearAllCache() async {
    final totalSize = _currentMemoryUsage;

    _imageCache.clear();
    _thumbnailCache.clear();
    _lastAccessTime.clear();
    _imageSizes.clear();
    _currentMemoryUsage = 0;

  }

  /// 🧹 清理缓存
  Future<void> clearCache() async {
    _imageCache.clear();
    _thumbnailCache.clear();
    _lastAccessTime.clear();
    _imageSizes.clear();
    _currentMemoryUsage = 0;
  }

  /// 📊 获取内存使用统计
  Map<String, dynamic> getMemoryStats() {
    return {
      'currentMemoryUsage': _formatBytes(_currentMemoryUsage),
      'currentMemoryUsageBytes': _currentMemoryUsage,
      'maxMemoryUsage': '${_maxMemoryUsageMB}MB',
      'cachedImages': _imageCache.length,
      'cachedThumbnails': _thumbnailCache.length,
      'memoryUsagePercentage':
          (_currentMemoryUsage / (_maxMemoryUsageMB * 1024 * 1024) * 100)
              .toStringAsFixed(1),
    };
  }

  /// 🔧 生成缓存键
  String _generateCacheKey(
      String imagePath, int? maxWidth, int? maxHeight, int quality) {
    return '${imagePath}_${maxWidth ?? 'null'}_${maxHeight ?? 'null'}_$quality';
  }

  /// 📏 格式化字节大小
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  /// 🧹 销毁服务
  void dispose() {
    _memoryCleanupTimer?.cancel();
    clearAllCache();
  }
}
