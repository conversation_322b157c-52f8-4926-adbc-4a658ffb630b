import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// 专业日志服务
///
/// 替代print语句，提供结构化的日志记录功能
/// 支持不同日志级别，生产环境优化
class LoggingService {
  static final LoggingService _instance = LoggingService._internal();
  factory LoggingService() => _instance;
  LoggingService._internal();

  /// 日志级别枚举
  static const String _debug = 'DEBUG';
  static const String _info = 'INFO';
  static const String _warning = 'WARNING';
  static const String _error = 'ERROR';

  /// 是否在生产环境中启用日志
  static bool get _isProductionLoggingEnabled => kDebugMode;

  /// Debug 级别日志 - 调试信息
  static void debug(String message, {String? tag}) {
    if (_isProductionLoggingEnabled) {
      _log(_debug, message, tag: tag);
    }
  }

  /// Info 级别日志 - 一般信息
  static void info(String message, {String? tag}) {
    if (_isProductionLoggingEnabled) {
      _log(_info, message, tag: tag);
    }
  }

  /// Warning 级别日志 - 警告信息
  static void warning(String message, {String? tag}) {
    if (_isProductionLoggingEnabled) {
      _log(_warning, message, tag: tag);
    }
  }

  /// Error 级别日志 - 错误信息
  static void error(String message,
      {String? tag, Object? error, StackTrace? stackTrace}) {
    if (_isProductionLoggingEnabled) {
      _log(_error, message, tag: tag, error: error, stackTrace: stackTrace);
    }
  }

  /// 内部日志记录方法
  static void _log(String level, String message,
      {String? tag, Object? error, StackTrace? stackTrace}) {
    final String logTag = tag ?? 'LoadGuard';
    final String timestamp = DateTime.now().toIso8601String();

    // 构建日志消息
    final String logMessage = '[$timestamp] [$level] [$logTag] $message';

    // 使用developer.log而不是print，在生产环境中更高效
    developer.log(
      logMessage,
      name: logTag,
      level: _getLevelValue(level),
      error: error,
      stackTrace: stackTrace,
    );
  }

  /// 获取日志级别数值
  static int _getLevelValue(String level) {
    switch (level) {
      case _debug:
        return 300;
      case _info:
        return 800;
      case _warning:
        return 900;
      case _error:
        return 1000;
      default:
        return 800;
    }
  }

  /// 性能日志 - 用于性能监控
  static void performance(String operation, Duration duration, {String? tag}) {
    info('⏱️ $operation completed in ${duration.inMilliseconds}ms',
        tag: tag ?? 'Performance');
  }

  /// 网络请求日志
  static void network(String method, String url, int statusCode,
      {String? tag}) {
    info('🌐 $method $url -> $statusCode', tag: tag ?? 'Network');
  }

  /// 识别结果日志
  static void recognition(String photoId, bool success, String details,
      {String? tag}) {
    if (success) {
      info('🎯 Recognition successful for $photoId: $details',
          tag: tag ?? 'Recognition');
    } else {
      warning('⚠️ Recognition failed for $photoId: $details',
          tag: tag ?? 'Recognition');
    }
  }

  /// 上传进度日志
  static void upload(String photoId, double progress, String status,
      {String? tag}) {
    info(
        '📤 Upload $photoId: ${(progress * 100).toStringAsFixed(1)}% - $status',
        tag: tag ?? 'Upload');
  }
}

/// 便捷的全局日志方法
class Log {
  static void d(String message, {String? tag}) =>
      LoggingService.debug(message, tag: tag);
  static void i(String message, {String? tag}) =>
      LoggingService.info(message, tag: tag);
  static void w(String message, {String? tag}) =>
      LoggingService.warning(message, tag: tag);
  static void e(String message,
          {String? tag, Object? error, StackTrace? stackTrace}) =>
      LoggingService.error(message,
          tag: tag, error: error, stackTrace: stackTrace);

  static void perf(String operation, Duration duration, {String? tag}) =>
      LoggingService.performance(operation, duration, tag: tag);
  static void net(String method, String url, int statusCode, {String? tag}) =>
      LoggingService.network(method, url, statusCode, tag: tag);
  static void rec(String photoId, bool success, String details,
          {String? tag}) =>
      LoggingService.recognition(photoId, success, details, tag: tag);
  static void upload(String photoId, double progress, String status,
          {String? tag}) =>
      LoggingService.upload(photoId, progress, status, tag: tag);
}
