import '../models/task_model.dart';
import '../models/worker_info_data.dart';
import '../models/task_model_extensions.dart';
import '../utils/app_logger.dart';

/// 工作量统计数据模型
class WorkloadStatistics {
  final String workerId;
  final String workerName;
  final String role;
  final String warehouse;
  final String group;
  final double assignedTonnage;
  final double completedTonnage;
  final int totalTasks;
  final int completedTasks;
  final double efficiency;
  final List<WorkloadDetail> taskDetails;
  final DateTime lastUpdated;

  const WorkloadStatistics({
    required this.workerId,
    required this.workerName,
    required this.role,
    required this.warehouse,
    required this.group,
    required this.assignedTonnage,
    required this.completedTonnage,
    required this.totalTasks,
    required this.completedTasks,
    required this.efficiency,
    required this.taskDetails,
    required this.lastUpdated,
  });

  Map<String, dynamic> toMap() {
    return {
      'workerId': workerId,
      'workerName': workerName,
      'role': role,
      'warehouse': warehouse,
      'group': group,
      'assignedTonnage': assignedTonnage,
      'completedTonnage': completedTonnage,
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'efficiency': efficiency,
      'taskDetails': taskDetails.map((d) => d.toMap()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

/// 工作量明细数据模型
class WorkloadDetail {
  final String taskId;
  final String productCode;
  final String batchNumber;
  final int quantity;
  final double allocatedTonnage;
  final bool isCompleted;
  final DateTime createTime;
  final DateTime? completedAt;
  final String template;

  const WorkloadDetail({
    required this.taskId,
    required this.productCode,
    required this.batchNumber,
    required this.quantity,
    required this.allocatedTonnage,
    required this.isCompleted,
    required this.createTime,
    this.completedAt,
    required this.template,
  });

  Map<String, dynamic> toMap() {
    return {
      'taskId': taskId,
      'productCode': productCode,
      'batchNumber': batchNumber,
      'quantity': quantity,
      'allocatedTonnage': allocatedTonnage,
      'isCompleted': isCompleted,
      'createTime': createTime.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'template': template,
    };
  }
}

/// 工作量概览数据模型
class WorkloadOverview {
  final int totalTasks;
  final int completedTasks;
  final double totalTonnage;
  final double completedTonnage;
  final int activeWorkers;
  final double averageEfficiency;
  final double completionRate;

  const WorkloadOverview({
    required this.totalTasks,
    required this.completedTasks,
    required this.totalTonnage,
    required this.completedTonnage,
    required this.activeWorkers,
    required this.averageEfficiency,
    required this.completionRate,
  });

  Map<String, dynamic> toMap() {
    return {
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'totalTonnage': totalTonnage,
      'completedTonnage': completedTonnage,
      'activeWorkers': activeWorkers,
      'averageEfficiency': averageEfficiency,
      'completionRate': completionRate,
    };
  }
}

/// 工作量计算服务
/// 解决工作量统计显示问题，提供准确的计算逻辑
class WorkloadCalculationService {
  static const String _tagName = 'WorkloadCalculationService';

  /// 计算工人工作量统计
  static Map<String, WorkloadStatistics> calculateWorkerStatistics(
    List<TaskModel> tasks, {
    DateTime? startDate,
    DateTime? endDate,
  }) {
    try {
      AppLogger.info('开始计算工人工作量统计，任务数: ${tasks.length}', tag: _tagName);

      // 过滤时间范围内的任务
      final filteredTasks = _filterTasksByDateRange(tasks, startDate, endDate);
      AppLogger.info('时间过滤后任务数: ${filteredTasks.length}', tag: _tagName);

      final workerStats = <String, WorkloadStatistics>{};

      // 初始化所有工人的统计数据
      for (final worker in allWorkers) {
        workerStats[worker.id] = WorkloadStatistics(
          workerId: worker.id,
          workerName: worker.name,
          role: worker.role,
          warehouse: worker.warehouse,
          group: worker.group,
          assignedTonnage: 0.0,
          completedTonnage: 0.0,
          totalTasks: 0,
          completedTasks: 0,
          efficiency: 0.0,
          taskDetails: [],
          lastUpdated: DateTime.now(),
        );
      }

      // 处理每个任务的工作量分配
      for (final task in filteredTasks) {
        _processTaskWorkload(task, workerStats);
      }

      // 计算效率
      for (final workerId in workerStats.keys) {
        final stats = workerStats[workerId]!;
        final efficiency = _calculateEfficiency(stats);
        
        workerStats[workerId] = WorkloadStatistics(
          workerId: stats.workerId,
          workerName: stats.workerName,
          role: stats.role,
          warehouse: stats.warehouse,
          group: stats.group,
          assignedTonnage: stats.assignedTonnage,
          completedTonnage: stats.completedTonnage,
          totalTasks: stats.totalTasks,
          completedTasks: stats.completedTasks,
          efficiency: efficiency,
          taskDetails: stats.taskDetails,
          lastUpdated: DateTime.now(),
        );
      }

      // 过滤掉没有工作量的工人
      final activeWorkerStats = <String, WorkloadStatistics>{};
      for (final entry in workerStats.entries) {
        if (entry.value.totalTasks > 0) {
          activeWorkerStats[entry.key] = entry.value;
        }
      }

      AppLogger.info('工作量统计计算完成，活跃工人: ${activeWorkerStats.length}', tag: _tagName);
      return activeWorkerStats;
    } catch (e, stackTrace) {
      AppLogger.error('计算工人工作量统计失败: $e', tag: _tagName, stackTrace: stackTrace);
      return {};
    }
  }

  /// 计算工作量概览
  static WorkloadOverview calculateWorkloadOverview(
    List<TaskModel> tasks, {
    DateTime? startDate,
    DateTime? endDate,
  }) {
    try {
      AppLogger.info('开始计算工作量概览', tag: _tagName);

      final filteredTasks = _filterTasksByDateRange(tasks, startDate, endDate);
      final workerStats = calculateWorkerStatistics(tasks, startDate: startDate, endDate: endDate);

      int totalTasks = 0;
      int completedTasks = 0;
      double totalTonnage = 0.0;
      double completedTonnage = 0.0;
      double totalEfficiency = 0.0;
      int activeWorkers = 0;

      for (final stats in workerStats.values) {
        totalTasks += stats.totalTasks;
        completedTasks += stats.completedTasks;
        totalTonnage += stats.assignedTonnage;
        completedTonnage += stats.completedTonnage;
        totalEfficiency += stats.efficiency;
        activeWorkers++;
      }

      final averageEfficiency = activeWorkers > 0 ? totalEfficiency / activeWorkers : 0.0;
      final completionRate = totalTasks > 0 ? completedTasks / totalTasks : 0.0;

      final overview = WorkloadOverview(
        totalTasks: totalTasks,
        completedTasks: completedTasks,
        totalTonnage: totalTonnage,
        completedTonnage: completedTonnage,
        activeWorkers: activeWorkers,
        averageEfficiency: averageEfficiency,
        completionRate: completionRate,
      );

      AppLogger.info('工作量概览计算完成: ${overview.toMap()}', tag: _tagName);
      return overview;
    } catch (e, stackTrace) {
      AppLogger.error('计算工作量概览失败: $e', tag: _tagName, stackTrace: stackTrace);
      return const WorkloadOverview(
        totalTasks: 0,
        completedTasks: 0,
        totalTonnage: 0.0,
        completedTonnage: 0.0,
        activeWorkers: 0,
        averageEfficiency: 0.0,
        completionRate: 0.0,
      );
    }
  }

  /// 按时间范围过滤任务
  static List<TaskModel> _filterTasksByDateRange(
    List<TaskModel> tasks,
    DateTime? startDate,
    DateTime? endDate,
  ) {
    if (startDate == null && endDate == null) {
      return tasks;
    }

    return tasks.where((task) {
      if (startDate != null && task.createTime.isBefore(startDate)) {
        return false;
      }
      if (endDate != null && task.createTime.isAfter(endDate)) {
        return false;
      }
      return true;
    }).toList();
  }

  /// 处理单个任务的工作量分配
  static void _processTaskWorkload(
    TaskModel task,
    Map<String, WorkloadStatistics> workerStats,
  ) {
    try {
      // 检查任务是否有工作量分配数据
      final workloadData = task.recognitionMetadata?['workload'];
      
      if (workloadData != null) {
        // 使用现有的工作量分配数据
        _processExistingWorkloadData(task, workloadData, workerStats);
      } else if (task.participants.isNotEmpty) {
        // 为有参与人员但没有工作量数据的任务创建默认分配
        _createDefaultWorkloadAllocation(task, workerStats);
      }
    } catch (e) {
      AppLogger.warning('处理任务${task.id}工作量失败: $e', tag: _tagName);
    }
  }

  /// 处理现有的工作量分配数据
  static void _processExistingWorkloadData(
    TaskModel task,
    dynamic workloadData,
    Map<String, WorkloadStatistics> workerStats,
  ) {
    try {
      final assignment = WorkloadAssignment.fromMap(workloadData as Map<String, dynamic>);
      
      for (final record in assignment.records) {
        final workerId = record.workerId;
        if (workerStats.containsKey(workerId)) {
          _updateWorkerStats(task, record, workerStats[workerId]!);
        }
      }
    } catch (e) {
      AppLogger.warning('处理任务${task.id}现有工作量数据失败: $e', tag: _tagName);
    }
  }

  /// 创建默认工作量分配
  static void _createDefaultWorkloadAllocation(
    TaskModel task,
    Map<String, WorkloadStatistics> workerStats,
  ) {
    try {
      final defaultTonnagePerPerson = (task.quantity * 1.5) / task.participants.length;
      
      for (final participantId in task.participants) {
        if (workerStats.containsKey(participantId)) {
          final record = WorkloadRecord(
            workerId: participantId,
            workerName: workerStats[participantId]!.workerName,
            role: workerStats[participantId]!.role,
            warehouse: workerStats[participantId]!.warehouse,
            group: workerStats[participantId]!.group,
            allocatedTonnage: defaultTonnagePerPerson,
            assignedAt: task.createTime,
            isCompleted: task.isCompleted,
            completedAt: task.completedAt,
          );
          
          _updateWorkerStats(task, record, workerStats[participantId]!);
        }
      }
    } catch (e) {
      AppLogger.warning('为任务${task.id}创建默认工作量分配失败: $e', tag: _tagName);
    }
  }

  /// 更新工人统计数据
  static void _updateWorkerStats(
    TaskModel task,
    WorkloadRecord record,
    WorkloadStatistics currentStats,
  ) {
    final detail = WorkloadDetail(
      taskId: task.id,
      productCode: task.productCode,
      batchNumber: task.batchNumber,
      quantity: task.quantity,
      allocatedTonnage: record.allocatedTonnage,
      isCompleted: task.isCompleted,
      createTime: task.createTime,
      completedAt: task.completedAt,
      template: task.template,
    );

    final updatedDetails = List<WorkloadDetail>.from(currentStats.taskDetails)..add(detail);
    final newAssignedTonnage = currentStats.assignedTonnage + record.allocatedTonnage;
    final newTotalTasks = currentStats.totalTasks + 1;
    
    double newCompletedTonnage = currentStats.completedTonnage;
    int newCompletedTasks = currentStats.completedTasks;
    
    if (task.isCompleted) {
      newCompletedTonnage += record.allocatedTonnage;
      newCompletedTasks += 1;
    }

    // 更新统计数据（通过重新创建对象）
    final updatedStats = WorkloadStatistics(
      workerId: currentStats.workerId,
      workerName: currentStats.workerName,
      role: currentStats.role,
      warehouse: currentStats.warehouse,
      group: currentStats.group,
      assignedTonnage: newAssignedTonnage,
      completedTonnage: newCompletedTonnage,
      totalTasks: newTotalTasks,
      completedTasks: newCompletedTasks,
      efficiency: currentStats.efficiency, // 稍后计算
      taskDetails: updatedDetails,
      lastUpdated: DateTime.now(),
    );

    // 这里需要在调用方更新Map
  }

  /// 计算工人效率
  static double _calculateEfficiency(WorkloadStatistics stats) {
    if (stats.assignedTonnage == 0) return 0.0;
    return (stats.completedTonnage / stats.assignedTonnage) * 100;
  }
}
