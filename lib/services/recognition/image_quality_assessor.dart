import 'dart:io';
import 'dart:typed_data';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import '../../utils/app_logger.dart';

/// 图像质量等级
enum ImageQualityLevel {
  high,    // 高质量：亮度、对比度、清晰度都很好
  medium,  // 中等质量：部分指标良好
  low,     // 低质量：多个指标不佳
}

/// 图像质量评估结果
class ImageQuality {
  final double brightness;     // 亮度 (0.0 - 1.0)
  final double contrast;       // 对比度 (0.0 - 1.0)
  final double sharpness;      // 清晰度 (0.0 - 1.0)
  final double overallScore;   // 综合评分 (0.0 - 1.0)
  final ImageQualityLevel level;
  final Map<String, dynamic> metadata;

  const ImageQuality({
    required this.brightness,
    required this.contrast,
    required this.sharpness,
    required this.overallScore,
    required this.level,
    required this.metadata,
  });

  /// 是否为高质量图像
  bool get isHighQuality => level == ImageQualityLevel.high;
  
  /// 是否为中等质量图像
  bool get isMediumQuality => level == ImageQualityLevel.medium;
  
  /// 是否为低质量图像
  bool get isLowQuality => level == ImageQualityLevel.low;

  /// 从指标创建质量评估结果
  factory ImageQuality.fromMetrics({
    required double brightness,
    required double contrast,
    required double sharpness,
    Map<String, dynamic>? metadata,
  }) {
    // 计算综合评分（加权平均）
    final overallScore = (brightness * 0.3) + (contrast * 0.4) + (sharpness * 0.3);
    
    // 确定质量等级
    ImageQualityLevel level;
    if (overallScore >= 0.7 && brightness >= 0.6 && contrast >= 0.7 && sharpness >= 0.6) {
      level = ImageQualityLevel.high;
    } else if (overallScore >= 0.5 && brightness >= 0.4 && contrast >= 0.5) {
      level = ImageQualityLevel.medium;
    } else {
      level = ImageQualityLevel.low;
    }

    return ImageQuality(
      brightness: brightness,
      contrast: contrast,
      sharpness: sharpness,
      overallScore: overallScore,
      level: level,
      metadata: metadata ?? {},
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'brightness': brightness,
      'contrast': contrast,
      'sharpness': sharpness,
      'overallScore': overallScore,
      'level': level.name,
      'metadata': metadata,
    };
  }

  @override
  String toString() {
    return 'ImageQuality(level: ${level.name}, score: ${overallScore.toStringAsFixed(2)}, '
           'brightness: ${brightness.toStringAsFixed(2)}, '
           'contrast: ${contrast.toStringAsFixed(2)}, '
           'sharpness: ${sharpness.toStringAsFixed(2)})';
  }
}

/// 图像质量评估器
/// 保留ML Kit V2核心能力的同时，增加智能质量评估
class ImageQualityAssessor {
  static const String _tagName = 'ImageQualityAssessor';

  /// 评估图像质量
  /// 
  /// 这个方法不会影响ML Kit V2的核心识别功能，
  /// 只是为智能策略选择提供参考信息
  static Future<ImageQuality> assess(InputImage image) async {
    try {
      AppLogger.info('开始评估图像质量', tag: _tagName);
      
      // 获取图像数据
      final imageData = await _getImageData(image);
      if (imageData == null) {
        AppLogger.warning('无法获取图像数据，使用默认质量评估', tag: _tagName);
        return _getDefaultQuality();
      }

      // 计算各项质量指标
      final brightness = _calculateBrightness(imageData);
      final contrast = _calculateContrast(imageData);
      final sharpness = _calculateSharpness(imageData);

      final quality = ImageQuality.fromMetrics(
        brightness: brightness,
        contrast: contrast,
        sharpness: sharpness,
        metadata: {
          'imageSize': imageData.length,
          'assessedAt': DateTime.now().toIso8601String(),
        },
      );

      AppLogger.info('图像质量评估完成: $quality', tag: _tagName);
      return quality;
    } catch (e, stackTrace) {
      AppLogger.error('图像质量评估失败: $e', tag: _tagName, stackTrace: stackTrace);
      return _getDefaultQuality();
    }
  }

  /// 从文件路径评估图像质量
  static Future<ImageQuality> assessFromPath(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        AppLogger.warning('图像文件不存在: $imagePath', tag: _tagName);
        return _getDefaultQuality();
      }

      final image = InputImage.fromFilePath(imagePath);
      return await assess(image);
    } catch (e, stackTrace) {
      AppLogger.error('从文件路径评估图像质量失败: $imagePath, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      return _getDefaultQuality();
    }
  }

  /// 获取图像数据
  static Future<Uint8List?> _getImageData(InputImage image) async {
    try {
      // 这里简化处理，实际项目中可能需要更复杂的图像数据提取
      if (image.filePath != null) {
        final file = File(image.filePath!);
        return await file.readAsBytes();
      }
      return null;
    } catch (e) {
      AppLogger.warning('获取图像数据失败: $e', tag: _tagName);
      return null;
    }
  }

  /// 计算图像亮度
  /// 简化的亮度计算，基于图像数据的统计特征
  static double _calculateBrightness(Uint8List imageData) {
    try {
      // 简化的亮度计算：基于文件大小和数据分布的启发式方法
      // 实际项目中可能需要更精确的像素级分析
      
      if (imageData.isEmpty) return 0.5;
      
      // 计算数据的平均值作为亮度指标
      int sum = 0;
      final sampleSize = (imageData.length / 100).round().clamp(100, 1000);
      
      for (int i = 0; i < sampleSize && i < imageData.length; i += imageData.length ~/ sampleSize) {
        sum += imageData[i];
      }
      
      final average = sum / sampleSize;
      final brightness = (average / 255.0).clamp(0.0, 1.0);
      
      AppLogger.debug('计算亮度: $brightness (样本: $sampleSize, 平均: $average)', tag: _tagName);
      return brightness;
    } catch (e) {
      AppLogger.warning('计算亮度失败: $e', tag: _tagName);
      return 0.5; // 默认中等亮度
    }
  }

  /// 计算图像对比度
  /// 简化的对比度计算，基于数据方差
  static double _calculateContrast(Uint8List imageData) {
    try {
      if (imageData.isEmpty) return 0.5;
      
      // 计算数据方差作为对比度指标
      final sampleSize = (imageData.length / 100).round().clamp(100, 1000);
      final samples = <int>[];
      
      for (int i = 0; i < sampleSize && i < imageData.length; i += imageData.length ~/ sampleSize) {
        samples.add(imageData[i]);
      }
      
      if (samples.isEmpty) return 0.5;
      
      final mean = samples.reduce((a, b) => a + b) / samples.length;
      final variance = samples.map((x) => (x - mean) * (x - mean)).reduce((a, b) => a + b) / samples.length;
      final standardDeviation = variance > 0 ? variance / (255 * 255) : 0.0;
      
      final contrast = (standardDeviation * 4).clamp(0.0, 1.0); // 放大方差作为对比度
      
      AppLogger.debug('计算对比度: $contrast (方差: $variance)', tag: _tagName);
      return contrast;
    } catch (e) {
      AppLogger.warning('计算对比度失败: $e', tag: _tagName);
      return 0.5; // 默认中等对比度
    }
  }

  /// 计算图像清晰度
  /// 简化的清晰度计算，基于文件大小和压缩比
  static double _calculateSharpness(Uint8List imageData) {
    try {
      if (imageData.isEmpty) return 0.5;
      
      // 基于文件大小的启发式清晰度评估
      // 通常清晰的图像在相同分辨率下文件会更大（包含更多细节）
      final fileSize = imageData.length;
      
      // 根据文件大小范围评估清晰度
      double sharpness;
      if (fileSize > 2 * 1024 * 1024) { // > 2MB
        sharpness = 0.9;
      } else if (fileSize > 1 * 1024 * 1024) { // > 1MB
        sharpness = 0.7;
      } else if (fileSize > 500 * 1024) { // > 500KB
        sharpness = 0.6;
      } else if (fileSize > 200 * 1024) { // > 200KB
        sharpness = 0.4;
      } else {
        sharpness = 0.3;
      }
      
      AppLogger.debug('计算清晰度: $sharpness (文件大小: ${fileSize}字节)', tag: _tagName);
      return sharpness;
    } catch (e) {
      AppLogger.warning('计算清晰度失败: $e', tag: _tagName);
      return 0.5; // 默认中等清晰度
    }
  }

  /// 获取默认质量评估（当评估失败时使用）
  static ImageQuality _getDefaultQuality() {
    return ImageQuality.fromMetrics(
      brightness: 0.5,
      contrast: 0.5,
      sharpness: 0.5,
      metadata: {
        'isDefault': true,
        'reason': 'assessment_failed',
        'createdAt': DateTime.now().toIso8601String(),
      },
    );
  }

  /// 批量评估图像质量
  static Future<List<ImageQuality>> assessMultiple(List<String> imagePaths) async {
    final results = <ImageQuality>[];
    
    AppLogger.info('开始批量评估${imagePaths.length}张图像', tag: _tagName);
    
    for (int i = 0; i < imagePaths.length; i++) {
      final imagePath = imagePaths[i];
      try {
        final quality = await assessFromPath(imagePath);
        results.add(quality);
        AppLogger.debug('图像${i + 1}/${imagePaths.length}评估完成: ${quality.level.name}', tag: _tagName);
      } catch (e) {
        AppLogger.warning('图像${i + 1}评估失败: $imagePath, 错误: $e', tag: _tagName);
        results.add(_getDefaultQuality());
      }
    }
    
    AppLogger.info('批量评估完成，结果: ${results.length}个', tag: _tagName);
    return results;
  }
}
