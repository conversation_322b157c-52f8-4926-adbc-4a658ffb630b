import 'dart:async';
import 'dart:io';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'enhanced_mlkit_recognizer.dart';
import 'image_quality_assessor.dart';
import 'intelligent_recognition_strategy.dart';
import '../../utils/app_logger.dart';

/// 智能识别协调器
/// 
/// 这是整个识别系统的核心，负责协调各个组件：
/// 1. 保留ML Kit V2 0.15.0核心识别能力
/// 2. 智能图像质量评估
/// 3. 策略选择和优化
/// 4. 降级保护机制
class IntelligentRecognitionCoordinator {
  static const String _tagName = 'IntelligentRecognitionCoordinator';
  
  final EnhancedMLKitRecognizer _mlkitRecognizer;
  late final ImageQualityAssessor _qualityAssessor;
  
  // 性能统计
  int _totalRecognitions = 0;
  int _successfulRecognitions = 0;
  double _totalProcessingTime = 0.0;
  final Map<String, int> _strategyUsageCount = {};
  
  IntelligentRecognitionCoordinator()
      : _mlkitRecognizer = EnhancedMLKitRecognizer() {
    _qualityAssessor = ImageQualityAssessor();
    _initializeStatistics();
  }
  
  /// 初始化统计数据
  void _initializeStatistics() {
    for (final strategy in RecognitionStrategy.values) {
      _strategyUsageCount[strategy.name] = 0;
    }
  }
  
  /// 智能识别主入口
  /// 
  /// 这是对外的主要接口，保证ML Kit V2核心能力的同时提供智能优化
  Future<RecognitionResult> recognizeWithIntelligentStrategy(
    String imagePath, {
    RecognitionContext? context,
    Function(double, String)? onProgress,
  }) async {
    final startTime = DateTime.now();
    _totalRecognitions++;
    
    try {
      AppLogger.info('开始智能识别流程，图像: $imagePath', tag: _tagName);
      onProgress?.call(0.05, '初始化识别流程...');
      
      // 验证输入
      final validationResult = await _validateInput(imagePath);
      if (!validationResult.isValid) {
        throw Exception(validationResult.error);
      }
      
      onProgress?.call(0.1, '正在评估图像质量...');
      
      // 1. 图像质量评估
      final image = InputImage.fromFilePath(imagePath);
      final quality = await _qualityAssessor.assess(image);
      
      AppLogger.info('图像质量评估: ${quality.toString()}', tag: _tagName);
      onProgress?.call(0.2, '选择最优识别策略...');
      
      // 2. 策略选择
      context ??= RecognitionContext.defaultContext();
      final strategy = IntelligentRecognitionStrategy.selectOptimalStrategy(
        quality, 
        context,
      );
      
      _strategyUsageCount[strategy.name] = (_strategyUsageCount[strategy.name] ?? 0) + 1;
      AppLogger.info('选择识别策略: ${strategy.name}', tag: _tagName);
      
      onProgress?.call(0.3, '执行ML Kit识别...');
      
      // 3. 执行识别
      final result = await _mlkitRecognizer.recognizeWithIntelligence(
        image,
        context: context,
        onProgress: (progress, message) {
          // 将内部进度映射到外部进度 (0.3 - 0.9)
          final mappedProgress = 0.3 + (progress * 0.6);
          onProgress?.call(mappedProgress, message);
        },
      );
      
      onProgress?.call(0.95, '完成识别处理...');
      
      // 4. 结果验证和优化
      final optimizedResult = await _optimizeResult(result, strategy, quality);
      
      // 5. 更新统计信息
      _updateStatistics(optimizedResult, startTime);
      
      onProgress?.call(1.0, '识别完成');
      
      AppLogger.info('智能识别完成: 成功=${optimizedResult.isSuccessful}, '
                    '策略=${strategy.name}, '
                    '耗时=${optimizedResult.processingTime?.inMilliseconds}ms', 
                    tag: _tagName);
      
      return optimizedResult;
      
    } catch (e, stackTrace) {
      AppLogger.error('智能识别失败: $imagePath, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      
      // 降级策略：如果智能识别失败，回退到基础ML Kit识别
      return await _fallbackRecognition(imagePath, onProgress);
    }
  }
  
  /// 批量识别
  Future<List<RecognitionResult>> recognizeMultiple(
    List<String> imagePaths, {
    RecognitionContext? context,
    Function(int, int, String)? onProgress,
  }) async {
    final results = <RecognitionResult>[];
    
    AppLogger.info('开始批量识别，图像数量: ${imagePaths.length}', tag: _tagName);
    
    for (int i = 0; i < imagePaths.length; i++) {
      final imagePath = imagePaths[i];
      
      try {
        onProgress?.call(i + 1, imagePaths.length, '正在处理: ${imagePath.split('/').last}');
        
        final result = await recognizeWithIntelligentStrategy(
          imagePath,
          context: context,
        );
        
        results.add(result);
        
        AppLogger.info('批量识别进度: ${i + 1}/${imagePaths.length}, '
                      '成功: ${result.isSuccessful}', tag: _tagName);
        
      } catch (e) {
        AppLogger.warning('批量识别中的图像失败: $imagePath, 错误: $e', tag: _tagName);
        
        // 添加失败结果
        results.add(RecognitionResult(
          ocrText: '',
          confidence: 0.0,
          strategy: 'failed',
          isFallback: true,
          metadata: {
            'error': e.toString(),
            'imagePath': imagePath,
            'batchIndex': i,
          },
        ));
      }
    }
    
    AppLogger.info('批量识别完成，成功: ${results.where((r) => r.isSuccessful).length}/${results.length}', 
                  tag: _tagName);
    
    return results;
  }
  
  /// 输入验证
  Future<ValidationResult> _validateInput(String imagePath) async {
    try {
      // 检查文件是否存在
      final file = File(imagePath);
      if (!await file.exists()) {
        return ValidationResult.error('图像文件不存在: $imagePath');
      }
      
      // 检查文件大小
      final fileSize = await file.length();
      if (fileSize == 0) {
        return ValidationResult.error('图像文件为空');
      }
      
      if (fileSize > 50 * 1024 * 1024) { // 50MB限制
        return ValidationResult.error('图像文件过大，请选择小于50MB的文件');
      }
      
      // 检查文件扩展名
      final extension = imagePath.toLowerCase().split('.').last;
      if (!['jpg', 'jpeg', 'png', 'bmp', 'webp'].contains(extension)) {
        return ValidationResult.error('不支持的图像格式: $extension');
      }
      
      return ValidationResult.success();
    } catch (e) {
      return ValidationResult.error('验证输入失败: $e');
    }
  }
  
  /// 结果优化
  Future<RecognitionResult> _optimizeResult(
    RecognitionResult result,
    RecognitionStrategy strategy,
    ImageQuality quality,
  ) async {
    try {
      // 如果识别失败且图像质量不错，可以尝试降级策略
      if (!result.isSuccessful && !quality.isLowQuality && !result.isFallback) {
        AppLogger.info('识别失败但图像质量良好，尝试降级策略', tag: _tagName);
        
        // 这里可以实现策略降级重试逻辑
        // 为了保持简洁，暂时返回原结果
      }
      
      // 添加优化元数据
      final optimizedMetadata = Map<String, dynamic>.from(result.metadata);
      optimizedMetadata.addAll({
        'optimized': true,
        'originalStrategy': strategy.name,
        'qualityLevel': quality.level.name,
        'optimizedAt': DateTime.now().toIso8601String(),
      });
      
      return RecognitionResult(
        ocrText: result.ocrText,
        matchResult: result.matchResult,
        confidence: result.confidence,
        strategy: result.strategy,
        processingTime: result.processingTime,
        imageQuality: quality,
        isFallback: result.isFallback,
        metadata: optimizedMetadata,
      );
    } catch (e) {
      AppLogger.warning('结果优化失败: $e', tag: _tagName);
      return result;
    }
  }
  
  /// 降级识别
  Future<RecognitionResult> _fallbackRecognition(
    String imagePath,
    Function(double, String)? onProgress,
  ) async {
    try {
      onProgress?.call(0.5, '使用基础识别模式...');
      AppLogger.info('使用降级识别模式: $imagePath', tag: _tagName);
      
      final image = InputImage.fromFilePath(imagePath);
      final recognizedText = await _mlkitRecognizer.recognizeText(image);
      
      return RecognitionResult(
        ocrText: recognizedText.text,
        confidence: 0.6, // 基础置信度
        strategy: 'fallback',
        isFallback: true,
        metadata: {
          'reason': 'intelligent_recognition_failed',
          'blockCount': recognizedText.blocks.length,
          'imagePath': imagePath,
        },
      );
    } catch (e, stackTrace) {
      AppLogger.error('降级识别也失败: $imagePath, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      
      return RecognitionResult(
        ocrText: '',
        confidence: 0.0,
        strategy: 'failed',
        isFallback: true,
        metadata: {
          'error': e.toString(),
          'reason': 'fallback_failed',
          'imagePath': imagePath,
        },
      );
    }
  }
  
  /// 更新统计信息
  void _updateStatistics(RecognitionResult result, DateTime startTime) {
    if (result.isSuccessful) {
      _successfulRecognitions++;
    }
    
    final processingTime = DateTime.now().difference(startTime);
    _totalProcessingTime += processingTime.inMilliseconds;
    
    AppLogger.debug('统计更新: 总识别=${_totalRecognitions}, '
                   '成功=${_successfulRecognitions}, '
                   '平均耗时=${(_totalProcessingTime / _totalRecognitions).round()}ms', 
                   tag: _tagName);
  }
  
  /// 获取性能统计
  Map<String, dynamic> getPerformanceStatistics() {
    final successRate = _totalRecognitions > 0 
        ? _successfulRecognitions / _totalRecognitions 
        : 0.0;
    
    final averageProcessingTime = _totalRecognitions > 0 
        ? _totalProcessingTime / _totalRecognitions 
        : 0.0;
    
    return {
      'totalRecognitions': _totalRecognitions,
      'successfulRecognitions': _successfulRecognitions,
      'successRate': successRate,
      'averageProcessingTimeMs': averageProcessingTime,
      'strategyUsage': Map.from(_strategyUsageCount),
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }
  
  /// 重置统计信息
  void resetStatistics() {
    _totalRecognitions = 0;
    _successfulRecognitions = 0;
    _totalProcessingTime = 0.0;
    _strategyUsageCount.clear();
    _initializeStatistics();
    
    AppLogger.info('性能统计已重置', tag: _tagName);
  }
  
  /// 释放资源
  void dispose() {
    _mlkitRecognizer.dispose();
    AppLogger.info('智能识别协调器资源已释放', tag: _tagName);
  }
}

/// 验证结果
class ValidationResult {
  final bool isValid;
  final String? error;
  
  const ValidationResult._(this.isValid, this.error);
  
  factory ValidationResult.success() => const ValidationResult._(true, null);
  factory ValidationResult.error(String error) => ValidationResult._(false, error);
}
