import 'image_quality_assessor.dart';
import '../../utils/app_logger.dart';

/// 识别策略类型
enum RecognitionStrategy {
  precise,   // 精确策略：高质量图像使用，严格匹配
  balanced,  // 平衡策略：中等质量图像使用，适中匹配
  tolerant,  // 容错策略：低质量图像使用，宽松匹配
}

/// 识别上下文
class RecognitionContext {
  final String taskType;           // 任务类型（平板车、集装箱等）
  final String expectedFormat;     // 期望的格式（批号、产品代码等）
  final double timeoutSeconds;     // 超时时间
  final bool isRetry;             // 是否为重试
  final Map<String, dynamic> metadata;

  const RecognitionContext({
    required this.taskType,
    required this.expectedFormat,
    this.timeoutSeconds = 15.0,
    this.isRetry = false,
    this.metadata = const {},
  });

  factory RecognitionContext.defaultContext() {
    return const RecognitionContext(
      taskType: 'general',
      expectedFormat: 'mixed',
      timeoutSeconds: 15.0,
      isRetry: false,
    );
  }

  /// 转换为匹配上下文
  MatchingContext toMatchingContext() {
    return MatchingContext(
      strategy: RecognitionStrategy.balanced, // 默认策略，会被后续覆盖
      taskType: taskType,
      expectedFormat: expectedFormat,
      isRetry: isRetry,
      metadata: metadata,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'taskType': taskType,
      'expectedFormat': expectedFormat,
      'timeoutSeconds': timeoutSeconds,
      'isRetry': isRetry,
      'metadata': metadata,
    };
  }
}

/// 匹配上下文
class MatchingContext {
  final RecognitionStrategy strategy;
  final String taskType;
  final String expectedFormat;
  final bool isRetry;
  final Map<String, dynamic> metadata;

  const MatchingContext({
    required this.strategy,
    required this.taskType,
    required this.expectedFormat,
    this.isRetry = false,
    this.metadata = const {},
  });

  factory MatchingContext.defaultContext() {
    return const MatchingContext(
      strategy: RecognitionStrategy.balanced,
      taskType: 'general',
      expectedFormat: 'mixed',
      isRetry: false,
    );
  }
}

/// 策略配置
class StrategyConfig {
  final double confidenceThreshold;    // 置信度阈值
  final int maxRetries;               // 最大重试次数
  final double timeoutMultiplier;     // 超时时间倍数
  final bool enableFuzzyMatch;        // 是否启用模糊匹配
  final bool enablePostProcessing;    // 是否启用后处理
  final Map<String, dynamic> parameters;

  const StrategyConfig({
    required this.confidenceThreshold,
    required this.maxRetries,
    required this.timeoutMultiplier,
    required this.enableFuzzyMatch,
    required this.enablePostProcessing,
    this.parameters = const {},
  });

  /// 精确策略配置
  static const StrategyConfig precise = StrategyConfig(
    confidenceThreshold: 0.95,
    maxRetries: 1,
    timeoutMultiplier: 1.0,
    enableFuzzyMatch: false,
    enablePostProcessing: true,
    parameters: {
      'strictMatching': true,
      'characterNormalization': 'minimal',
      'patternMatching': 'exact',
    },
  );

  /// 平衡策略配置
  static const StrategyConfig balanced = StrategyConfig(
    confidenceThreshold: 0.85,
    maxRetries: 2,
    timeoutMultiplier: 1.2,
    enableFuzzyMatch: true,
    enablePostProcessing: true,
    parameters: {
      'strictMatching': false,
      'characterNormalization': 'standard',
      'patternMatching': 'flexible',
    },
  );

  /// 容错策略配置
  static const StrategyConfig tolerant = StrategyConfig(
    confidenceThreshold: 0.75,
    maxRetries: 3,
    timeoutMultiplier: 1.5,
    enableFuzzyMatch: true,
    enablePostProcessing: true,
    parameters: {
      'strictMatching': false,
      'characterNormalization': 'aggressive',
      'patternMatching': 'loose',
    },
  );
}

/// 智能识别策略选择器
/// 保留ML Kit V2核心能力，根据图像质量智能选择最优策略
class IntelligentRecognitionStrategy {
  static const String _tagName = 'IntelligentRecognitionStrategy';

  /// 选择最优识别策略
  /// 
  /// 这个方法不会改变ML Kit V2的核心识别功能，
  /// 只是为后续的匹配和处理步骤选择最适合的策略
  static RecognitionStrategy selectOptimalStrategy(
    ImageQuality quality,
    RecognitionContext context,
  ) {
    try {
      AppLogger.info('开始选择识别策略，图像质量: ${quality.level.name}', tag: _tagName);

      RecognitionStrategy strategy;

      // 基于图像质量的基础策略选择
      switch (quality.level) {
        case ImageQualityLevel.high:
          strategy = RecognitionStrategy.precise;
          break;
        case ImageQualityLevel.medium:
          strategy = RecognitionStrategy.balanced;
          break;
        case ImageQualityLevel.low:
          strategy = RecognitionStrategy.tolerant;
          break;
      }

      // 根据上下文进行策略调整
      strategy = _adjustStrategyByContext(strategy, context, quality);

      AppLogger.info('选择的识别策略: ${strategy.name}', tag: _tagName);
      return strategy;
    } catch (e, stackTrace) {
      AppLogger.error('选择识别策略失败: $e', tag: _tagName, stackTrace: stackTrace);
      return RecognitionStrategy.balanced; // 默认策略
    }
  }

  /// 根据上下文调整策略
  static RecognitionStrategy _adjustStrategyByContext(
    RecognitionStrategy baseStrategy,
    RecognitionContext context,
    ImageQuality quality,
  ) {
    try {
      var adjustedStrategy = baseStrategy;

      // 如果是重试，降低策略要求
      if (context.isRetry) {
        switch (baseStrategy) {
          case RecognitionStrategy.precise:
            adjustedStrategy = RecognitionStrategy.balanced;
            AppLogger.debug('重试场景：从精确策略调整为平衡策略', tag: _tagName);
            break;
          case RecognitionStrategy.balanced:
            adjustedStrategy = RecognitionStrategy.tolerant;
            AppLogger.debug('重试场景：从平衡策略调整为容错策略', tag: _tagName);
            break;
          case RecognitionStrategy.tolerant:
            // 已经是最宽松的策略，保持不变
            break;
        }
      }

      // 根据任务类型进行微调
      adjustedStrategy = _adjustStrategyByTaskType(adjustedStrategy, context.taskType, quality);

      // 根据期望格式进行微调
      adjustedStrategy = _adjustStrategyByFormat(adjustedStrategy, context.expectedFormat, quality);

      if (adjustedStrategy != baseStrategy) {
        AppLogger.info('策略已调整：${baseStrategy.name} -> ${adjustedStrategy.name}', tag: _tagName);
      }

      return adjustedStrategy;
    } catch (e) {
      AppLogger.warning('调整策略失败: $e，使用基础策略', tag: _tagName);
      return baseStrategy;
    }
  }

  /// 根据任务类型调整策略
  static RecognitionStrategy _adjustStrategyByTaskType(
    RecognitionStrategy strategy,
    String taskType,
    ImageQuality quality,
  ) {
    switch (taskType.toLowerCase()) {
      case '集装箱':
        // 集装箱号码通常比较清晰，可以使用更严格的策略
        if (strategy == RecognitionStrategy.tolerant && quality.overallScore > 0.4) {
          AppLogger.debug('集装箱任务：提升策略严格度', tag: _tagName);
          return RecognitionStrategy.balanced;
        }
        break;
      case '平板车':
        // 平板车标签可能受环境影响较大，保持宽松策略
        if (strategy == RecognitionStrategy.precise && quality.overallScore < 0.8) {
          AppLogger.debug('平板车任务：降低策略严格度', tag: _tagName);
          return RecognitionStrategy.balanced;
        }
        break;
    }
    return strategy;
  }

  /// 根据期望格式调整策略
  static RecognitionStrategy _adjustStrategyByFormat(
    RecognitionStrategy strategy,
    String expectedFormat,
    ImageQuality quality,
  ) {
    switch (expectedFormat.toLowerCase()) {
      case 'batch_number':
      case '批号':
        // 批号格式相对固定，可以使用较严格的策略
        if (strategy == RecognitionStrategy.tolerant && quality.contrast > 0.6) {
          AppLogger.debug('批号识别：提升策略严格度', tag: _tagName);
          return RecognitionStrategy.balanced;
        }
        break;
      case 'product_code':
      case '产品代码':
        // 产品代码格式多样，保持灵活策略
        break;
      case 'mixed':
      case '混合':
        // 混合格式，保持平衡策略
        break;
    }
    return strategy;
  }

  /// 获取策略配置
  static StrategyConfig getStrategyConfig(RecognitionStrategy strategy) {
    switch (strategy) {
      case RecognitionStrategy.precise:
        return StrategyConfig.precise;
      case RecognitionStrategy.balanced:
        return StrategyConfig.balanced;
      case RecognitionStrategy.tolerant:
        return StrategyConfig.tolerant;
    }
  }

  /// 评估策略效果
  static double evaluateStrategyEffectiveness(
    RecognitionStrategy strategy,
    ImageQuality quality,
    bool recognitionSuccess,
    double processingTime,
  ) {
    try {
      double effectiveness = 0.0;

      // 基础效果评分
      if (recognitionSuccess) {
        effectiveness += 0.6; // 成功识别占60%
      }

      // 处理时间评分（越快越好）
      final timeScore = (15.0 - processingTime.clamp(0.0, 15.0)) / 15.0;
      effectiveness += timeScore * 0.2; // 时间效率占20%

      // 策略适配度评分
      final adaptationScore = _calculateAdaptationScore(strategy, quality);
      effectiveness += adaptationScore * 0.2; // 适配度占20%

      AppLogger.debug('策略效果评估: ${strategy.name}, 成功: $recognitionSuccess, '
                     '时间: ${processingTime}s, 效果: ${effectiveness.toStringAsFixed(2)}', 
                     tag: _tagName);

      return effectiveness.clamp(0.0, 1.0);
    } catch (e) {
      AppLogger.warning('评估策略效果失败: $e', tag: _tagName);
      return recognitionSuccess ? 0.6 : 0.0;
    }
  }

  /// 计算策略适配度评分
  static double _calculateAdaptationScore(RecognitionStrategy strategy, ImageQuality quality) {
    switch (strategy) {
      case RecognitionStrategy.precise:
        // 精确策略适合高质量图像
        return quality.isHighQuality ? 1.0 : (quality.isMediumQuality ? 0.5 : 0.2);
      case RecognitionStrategy.balanced:
        // 平衡策略适合中等质量图像，对其他质量也有一定适应性
        return quality.isMediumQuality ? 1.0 : 0.7;
      case RecognitionStrategy.tolerant:
        // 容错策略适合低质量图像
        return quality.isLowQuality ? 1.0 : (quality.isMediumQuality ? 0.7 : 0.4);
    }
  }

  /// 获取策略描述
  static String getStrategyDescription(RecognitionStrategy strategy) {
    switch (strategy) {
      case RecognitionStrategy.precise:
        return '精确策略：适用于高质量图像，使用严格匹配规则，确保高准确率';
      case RecognitionStrategy.balanced:
        return '平衡策略：适用于中等质量图像，平衡准确率和容错性';
      case RecognitionStrategy.tolerant:
        return '容错策略：适用于低质量图像，使用宽松匹配规则，提高识别成功率';
    }
  }
}
