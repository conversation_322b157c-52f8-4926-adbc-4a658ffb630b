import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🎯 置信度评估服务
/// 基于系统真实算法和模型制定的专业置信度评分标准
class ConfidenceEvaluationService {
  /// 📊 置信度等级定义
  static const double HIGH_CONFIDENCE_THRESHOLD = 0.85; // 高置信度: ≥85%
  static const double MEDIUM_CONFIDENCE_THRESHOLD = 0.60; // 中等置信度: 60-84%
  static const double LOW_CONFIDENCE_THRESHOLD = 0.30; // 低置信度: 30-59%
  // 极低置信度: <30%

  /// 🎯 综合置信度评估
  /// 基于MLKit原始置信度、文本质量、匹配度等多个维度
  static ConfidenceScore evaluateConfidence({
    required RecognitionResult result,
    String? presetProductCode,
    String? presetBatchNumber,
  }) {
    try {
      final isMatched = result.matchesPreset;

      // 1. 获取MLKit原始置信度
      final rawConfidence = result.confidence ??
          (result.ocrText != null && result.ocrText!.isNotEmpty ? 80.0 : 0.0);
      final mlkitConfidence =
          rawConfidence > 1 ? rawConfidence / 100.0 : rawConfidence;

      // 2. 文本质量评估
      final textQuality = _evaluateTextQuality(result.ocrText);

      // 3. 预设匹配度评估 (关键修复)
      // 🔧 修复：如果识别结果已标记为匹配，则匹配度直接为100%
      final double matchScore = isMatched
          ? 1.0
          : _evaluatePresetMatch(
              result.extractedProductCode,
              result.extractedBatchNumber,
              presetProductCode,
              presetBatchNumber,
            );

      // 4. 数据一致性评估 - 修复逻辑
      double consistencyScore;
      if (result.extractedProductCode == null &&
          result.extractedBatchNumber == null) {
        // 如果什么都没识别到，一致性为0
        consistencyScore = 0.0;
      } else if (result.isQrOcrConsistent) {
        // QR码和OCR识别结果一致
        consistencyScore = 1.0;
      } else {
        // 有识别结果但不一致
        consistencyScore = 0.7;
      }

      // 综合置信度加权计算
      // 🔧 优化权重: 成功匹配时，匹配度权重应最高
      final finalConfidence = isMatched
          ? (
              // 识别成功时，权重向匹配度和文本质量倾斜
              mlkitConfidence * 0.15 + // MLKit分数权重降低
                  textQuality * 0.15 +
                  matchScore * 0.60 + // 预设匹配度权重提升至60%
                  consistencyScore * 0.10)
          : (
              // 识别失败时，各项权重均衡
              mlkitConfidence * 0.30 +
                  textQuality * 0.25 +
                  matchScore * 0.35 +
                  consistencyScore * 0.10);

      // 🔧 优化: 保证成功匹配时的置信度在一个较高的合理区间
      final adjustedConfidence = isMatched
          ? finalConfidence.clamp(0.85, 1.0) // 成功匹配时，最低置信度为85%
          : finalConfidence.clamp(0.0, 0.84); // 失败时，最高不超过84%

      // 生成置信度评估结果
      final score = ConfidenceScore(
        finalScore: adjustedConfidence,
        rawMlkitScore: mlkitConfidence,
        textQualityScore: textQuality,
        matchScore: matchScore, // 传递修复后的matchScore
        consistencyScore: consistencyScore,
        level: _getConfidenceLevel(adjustedConfidence),
        recommendation: _getRecommendation(adjustedConfidence, result),
        technicalDetails: _getTechnicalDetails(result),
      );

      AppLogger.info(
          '置信度评估完成: ${(adjustedConfidence * 100).toStringAsFixed(1)}% (${score.level.displayName}) - 匹配状态: $isMatched');

      return score;
    } catch (e) {
      AppLogger.error('置信度评估失败: $e');
      // 返回基础评估结果
      return ConfidenceScore(
        finalScore: result.confidence ?? 0.0,
        rawMlkitScore: result.confidence ?? 0.0,
        textQualityScore: 0.0,
        matchScore: 0.0,
        consistencyScore: 0.0,
        level: _getConfidenceLevel(result.confidence ?? 0.0),
        recommendation: ConfidenceRecommendation.retry,
        technicalDetails: {},
      );
    }
  }

  /// 📝 文本质量评估
  static double _evaluateTextQuality(String? ocrText) {
    if (ocrText == null || ocrText.isEmpty) return 0.0;

    double score = 0.0;

    // 文本长度合理性 (20分)
    final textLength = ocrText.length;
    if (textLength >= 8 && textLength <= 100) {
      score += 0.20;
    } else if (textLength >= 4) {
      score += 0.10;
    }

    // 包含数字和字母 (30分)
    final hasNumbers = RegExp(r'\d').hasMatch(ocrText);
    final hasLetters = RegExp(r'[a-zA-Z]').hasMatch(ocrText);
    if (hasNumbers && hasLetters) {
      score += 0.30;
    } else if (hasNumbers || hasLetters) {
      score += 0.15;
    }

    // 产品代码模式匹配 (25分)
    if (RegExp(r'[A-Z]{2,}-\d{4}').hasMatch(ocrText)) {
      score += 0.25;
    } else if (RegExp(r'[A-Z]+-\d+').hasMatch(ocrText)) {
      score += 0.15;
    }

    // 批号模式匹配 (25分)
    if (RegExp(r'\d{6}[A-Z]\d{5}').hasMatch(ocrText)) {
      score += 0.25;
    } else if (RegExp(r'\d{6}[A-Z]\d+').hasMatch(ocrText)) {
      score += 0.15;
    } else if (RegExp(r'\d{8,}').hasMatch(ocrText)) {
      score += 0.10;
    }

    return score.clamp(0.0, 1.0);
  }

  /// 🎯 预设匹配度评估
  static double _evaluatePresetMatch(
    String? extractedProductCode,
    String? extractedBatchNumber,
    String? presetProductCode,
    String? presetBatchNumber,
  ) {
    // 如果没有预设信息，则无法进行匹配，返回0
    if ((presetProductCode == null || presetProductCode.isEmpty) &&
        (presetBatchNumber == null || presetBatchNumber.isEmpty)) {
      return 0.0;
    }

    double score = 0.0;
    int itemCount = 0;

    // 产品代码匹配
    if (presetProductCode != null && presetProductCode.isNotEmpty) {
      itemCount++;
      if (extractedProductCode != null && extractedProductCode.isNotEmpty) {
        final normalizedExtracted =
            extractedProductCode.replaceAll(' ', '').toUpperCase();
        final normalizedPreset =
            presetProductCode.replaceAll(' ', '').toUpperCase();
        if (normalizedExtracted == normalizedPreset) {
          score += 1.0;
        } else if (_isPartialMatch(normalizedExtracted, normalizedPreset)) {
          score += 0.5; // 部分匹配得一半分数
        }
      }
    }

    // 批号匹配 - 🔧 修复：支持核心批号匹配（去除后缀）
    if (presetBatchNumber != null && presetBatchNumber.isNotEmpty) {
      itemCount++;
      if (extractedBatchNumber != null && extractedBatchNumber.isNotEmpty) {
        final normalizedExtracted =
            extractedBatchNumber.replaceAll(' ', '').toUpperCase();
        final normalizedPreset =
            presetBatchNumber.replaceAll(' ', '').toUpperCase();

        // 🔧 先尝试完全匹配
        if (normalizedExtracted == normalizedPreset) {
          score += 1.0;
        }
        // 🔧 然后尝试核心批号匹配（去除后缀）
        else {
          final extractedCore = _extractCoreBatchNumber(normalizedExtracted);
          final presetCore = _extractCoreBatchNumber(normalizedPreset);
          if (extractedCore == presetCore) {
            score += 1.0; // 核心匹配也给满分
          } else if (_isPartialMatch(normalizedExtracted, normalizedPreset)) {
            score += 0.5; // 部分匹配得一半分数
          }
        }
      }
    }

    if (itemCount == 0) return 0.0;

    // 返回平均分
    return (score / itemCount).clamp(0.0, 1.0);
  }

  /// 🔧 新增：提取核心批号（去除后缀）- 与MLKit服务保持一致
  static String _extractCoreBatchNumber(String batchNumber) {
    // 常见的批号后缀模式
    final suffixPatterns = [
      RegExp(r'-[0-9]+[A-Z]$'), // 如 -1B, -2A, -4B
      RegExp(r'-[A-Z]+$'), // 如 -B, -A, -C
      RegExp(r'_[0-9]+$'), // 如 _1, _2
    ];

    for (final pattern in suffixPatterns) {
      if (pattern.hasMatch(batchNumber)) {
        final match = pattern.firstMatch(batchNumber);
        if (match != null) {
          return batchNumber.substring(0, match.start);
        }
      }
    }

    return batchNumber; // 如果没有后缀，返回原批号
  }

  /// 部分匹配检查
  static bool _isPartialMatch(String extracted, String preset) {
    final cleanExtracted =
        extracted.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '').toLowerCase();
    final cleanPreset =
        preset.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '').toLowerCase();

    // 检查包含关系或较高的Jaro-Winkler相似度
    return cleanExtracted.contains(cleanPreset) ||
        cleanPreset.contains(cleanExtracted) ||
        _calculateSimilarity(cleanExtracted, cleanPreset) > 0.85; // 提高相似度阈值
  }

  /// 字符串相似度计算 (Jaro-Winkler)
  static double _calculateSimilarity(String s1, String s2) {
    if (s1.isEmpty && s2.isEmpty) return 1.0;
    if (s1.isEmpty || s2.isEmpty) return 0.0;

    int matchDistance =
        (s1.length > s2.length ? s1.length : s2.length) ~/ 2 - 1;
    if (matchDistance < 0) matchDistance = 0;

    List<bool> s1Matches = List.filled(s1.length, false);
    List<bool> s2Matches = List.filled(s2.length, false);

    int matches = 0;
    for (int i = 0; i < s1.length; i++) {
      int start = i - matchDistance;
      if (start < 0) start = 0;
      int end = i + matchDistance + 1;
      if (end > s2.length) end = s2.length;

      for (int k = start; k < end; k++) {
        if (s2Matches[k]) continue;
        if (s1[i] != s2[k]) continue;
        s1Matches[i] = true;
        s2Matches[k] = true;
        matches++;
        break;
      }
    }

    if (matches == 0) return 0.0;

    int t = 0;
    int k = 0;
    for (int i = 0; i < s1.length; i++) {
      if (!s1Matches[i]) continue;
      while (!s2Matches[k]) {
        k++;
      }
      if (s1[i] != s2[k]) t++;
      k++;
    }
    t ~/= 2;

    double jaro =
        (matches / s1.length + matches / s2.length + (matches - t) / matches) /
            3;

    // Jaro-Winkler a
    double p = 0.1;
    int l = 0;
    if (jaro > 0.7) {
      while (l < 4 && s1.length > l && s2.length > l && s1[l] == s2[l]) {
        l++;
      }
    }

    return (jaro + l * p * (1 - jaro)).clamp(0.0, 1.0);
  }

  /// 废弃：编辑距离计算
  // static int _levenshteinDistance(String s1, String s2) { ... }

  /// 获取置信度等级
  static ConfidenceLevel _getConfidenceLevel(double confidence) {
    if (confidence >= HIGH_CONFIDENCE_THRESHOLD) {
      return ConfidenceLevel.high;
    } else if (confidence >= MEDIUM_CONFIDENCE_THRESHOLD) {
      return ConfidenceLevel.medium;
    } else if (confidence >= LOW_CONFIDENCE_THRESHOLD) {
      return ConfidenceLevel.low;
    } else {
      return ConfidenceLevel.veryLow;
    }
  }

  /// 获取操作建议
  static ConfidenceRecommendation _getRecommendation(
      double confidence, RecognitionResult result) {
    if (confidence >= HIGH_CONFIDENCE_THRESHOLD) {
      return ConfidenceRecommendation.accept;
    } else if (confidence >= MEDIUM_CONFIDENCE_THRESHOLD) {
      return result.matchesPreset
          ? ConfidenceRecommendation.accept
          : ConfidenceRecommendation.manualReview;
    } else if (confidence >= LOW_CONFIDENCE_THRESHOLD) {
      return ConfidenceRecommendation.manualReview;
    } else {
      return ConfidenceRecommendation.retry;
    }
  }

  /// 获取技术详情
  static Map<String, dynamic> _getTechnicalDetails(RecognitionResult result) {
    return {
      'mlkitEngine': 'Google ML Kit v2',
      'recognitionTime': result.recognitionTime.millisecondsSinceEpoch,
      'hasBoundingBox': result.boundingBox != null,
      'textLength': result.ocrText?.length ?? 0,
      'hasQrCode': result.qrCode != null && result.qrCode!.isNotEmpty,
      'isConsistent': result.isQrOcrConsistent,
      'processingStrategy': '企业级高精度模式',
      'algorithmVersion': '2.0',
    };
  }
}

/// 📊 置信度评估结果
class ConfidenceScore {
  final double finalScore; // 最终综合置信度 (0-1)
  final double rawMlkitScore; // MLKit原始置信度
  final double textQualityScore; // 文本质量分数
  final double matchScore; // 预设匹配分数
  final double consistencyScore; // 数据一致性分数
  final ConfidenceLevel level; // 置信度等级
  final ConfidenceRecommendation recommendation; // 操作建议
  final Map<String, dynamic> technicalDetails; // 技术详情

  ConfidenceScore({
    required this.finalScore,
    required this.rawMlkitScore,
    required this.textQualityScore,
    required this.matchScore,
    required this.consistencyScore,
    required this.level,
    required this.recommendation,
    required this.technicalDetails,
  });

  /// 转换为百分比字符串
  String get percentageString => '${(finalScore * 100).toStringAsFixed(1)}%';

  /// 获取详细分析
  Map<String, String> get detailedAnalysis => {
        'ML Kit原始分数': '${(rawMlkitScore * 100).toStringAsFixed(1)}%',
        '文本质量评估': '${(textQualityScore * 100).toStringAsFixed(1)}%',
        '预设匹配度': '${(matchScore * 100).toStringAsFixed(1)}%',
        '数据一致性': '${(consistencyScore * 100).toStringAsFixed(1)}%',
        '综合置信度': percentageString,
      };
}

/// 🎯 置信度等级
enum ConfidenceLevel {
  veryLow('极低', '< 30%', 0xFF757575),
  low('低', '30-59%', 0xFFFF9800),
  medium('中等', '60-84%', 0xFF2196F3),
  high('高', '≥ 85%', 0xFF4CAF50);

  const ConfidenceLevel(
      this.displayName, this.rangeDescription, this.colorValue);

  final String displayName;
  final String rangeDescription; // 🔧 修复命名
  final int colorValue;
}

/// 🎯 操作建议
enum ConfidenceRecommendation {
  accept('可以接受', '识别结果可信，建议直接使用'),
  manualReview('人工审核', '建议人工确认识别结果'),
  retry('重新识别', '建议重新拍照或调整角度');

  const ConfidenceRecommendation(this.displayName, this.description);

  final String displayName;
  final String description;
}
