import '../models/workload_models.dart';
import '../models/worker_info_data.dart';
import '../repositories/config_repository.dart';
import '../utils/app_logger.dart';

/// 配置数据迁移服务
/// 负责将硬编码数据迁移到动态配置存储
class ConfigMigrationService {
  static const String _tagName = 'ConfigMigrationService';
  
  final ConfigRepository _configRepository;
  
  ConfigMigrationService(this._configRepository);
  
  /// 执行完整的数据迁移
  Future<MigrationResult> migrateAllData() async {
    try {
      AppLogger.info('开始执行完整数据迁移', tag: _tagName);
      
      final result = MigrationResult();
      
      // 1. 迁移工作人员数据
      final workerResult = await migrateWorkerData();
      result.mergeWorkerResult(workerResult);
      
      // 2. 迁移仓库数据
      final warehouseResult = await migrateWarehouseData();
      result.mergeWarehouseResult(warehouseResult);
      
      // 3. 迁移小组数据
      final groupResult = await migrateGroupData();
      result.mergeGroupResult(groupResult);
      
      // 4. 迁移模板数据
      final templateResult = await migrateTemplateData();
      result.mergeTemplateResult(templateResult);
      
      AppLogger.info('完整数据迁移完成: ${result.toString()}', tag: _tagName);
      return result;
    } catch (e, stackTrace) {
      AppLogger.error('完整数据迁移失败: $e', tag: _tagName, stackTrace: stackTrace);
      return MigrationResult(
        success: false,
        error: e.toString(),
      );
    }
  }
  
  /// 迁移工作人员数据
  /// 将worker_info_data.dart中的88名工作人员迁移到动态配置
  Future<WorkerMigrationResult> migrateWorkerData() async {
    try {
      AppLogger.info('开始迁移工作人员数据', tag: _tagName);
      
      final result = WorkerMigrationResult();
      
      // 检查是否已经迁移过
      final existingWorkers = await _configRepository.getWorkers();
      if (existingWorkers.isNotEmpty) {
        AppLogger.info('工作人员数据已存在，跳过迁移', tag: _tagName);
        result.skipped = existingWorkers.length;
        return result;
      }
      
      // 从硬编码数据源获取工作人员信息
      final legacyWorkers = allWorkers;
      AppLogger.info('发现${legacyWorkers.length}名工作人员需要迁移', tag: _tagName);
      
      for (final legacyWorker in legacyWorkers) {
        try {
          final workerConfig = WorkerConfig(
            id: legacyWorker.id,
            name: legacyWorker.name,
            role: legacyWorker.role,
            warehouse: legacyWorker.warehouse,
            group: legacyWorker.group,
            isActive: true,
            createdAt: DateTime.now(),
            metadata: {
              'migratedFrom': 'worker_info_data.dart',
              'migratedAt': DateTime.now().toIso8601String(),
              'originalData': {
                'id': legacyWorker.id,
                'name': legacyWorker.name,
                'role': legacyWorker.role,
                'warehouse': legacyWorker.warehouse,
                'group': legacyWorker.group,
              },
            },
          );
          
          await _configRepository.saveWorker(workerConfig);
          result.migrated++;
          
          AppLogger.debug('工作人员迁移成功: ${legacyWorker.name} (${legacyWorker.id})', tag: _tagName);
        } catch (e) {
          result.failed++;
          result.errors.add('迁移工作人员失败 ${legacyWorker.name}: $e');
          AppLogger.warning('工作人员迁移失败: ${legacyWorker.name}, 错误: $e', tag: _tagName);
        }
      }
      
      AppLogger.info('工作人员数据迁移完成: 成功${result.migrated}, 失败${result.failed}', tag: _tagName);
      return result;
    } catch (e, stackTrace) {
      AppLogger.error('工作人员数据迁移失败: $e', tag: _tagName, stackTrace: stackTrace);
      return WorkerMigrationResult(
        success: false,
        error: e.toString(),
      );
    }
  }
  
  /// 迁移仓库数据
  Future<WarehouseMigrationResult> migrateWarehouseData() async {
    try {
      AppLogger.info('开始迁移仓库数据', tag: _tagName);
      
      final result = WarehouseMigrationResult();
      
      // 检查是否已经迁移过
      final existingWarehouses = await _configRepository.getWarehouses();
      if (existingWarehouses.isNotEmpty) {
        AppLogger.info('仓库数据已存在，跳过迁移', tag: _tagName);
        result.skipped = existingWarehouses.length;
        return result;
      }
      
      // 从工作人员数据中提取仓库信息
      final legacyWorkers = allWorkers;
      final warehouseNames = legacyWorkers.map((w) => w.warehouse).toSet().toList();
      
      AppLogger.info('发现${warehouseNames.length}个仓库需要迁移', tag: _tagName);
      
      for (int i = 0; i < warehouseNames.length; i++) {
        final warehouseName = warehouseNames[i];
        try {
          final warehouseConfig = WarehouseConfig(
            id: 'warehouse_${i + 1}',
            name: warehouseName,
            description: '从工作人员数据迁移的仓库: $warehouseName',
            isActive: true,
            supportedTemplates: ['平板车', '集装箱'], // 默认支持所有模板
            createdAt: DateTime.now(),
            metadata: {
              'migratedFrom': 'worker_info_data.dart',
              'migratedAt': DateTime.now().toIso8601String(),
              'workerCount': legacyWorkers.where((w) => w.warehouse == warehouseName).length,
            },
          );
          
          await _configRepository.saveWarehouse(warehouseConfig);
          result.migrated++;
          
          AppLogger.debug('仓库迁移成功: $warehouseName', tag: _tagName);
        } catch (e) {
          result.failed++;
          result.errors.add('迁移仓库失败 $warehouseName: $e');
          AppLogger.warning('仓库迁移失败: $warehouseName, 错误: $e', tag: _tagName);
        }
      }
      
      AppLogger.info('仓库数据迁移完成: 成功${result.migrated}, 失败${result.failed}', tag: _tagName);
      return result;
    } catch (e, stackTrace) {
      AppLogger.error('仓库数据迁移失败: $e', tag: _tagName, stackTrace: stackTrace);
      return WarehouseMigrationResult(
        success: false,
        error: e.toString(),
      );
    }
  }
  
  /// 迁移小组数据
  Future<GroupMigrationResult> migrateGroupData() async {
    try {
      AppLogger.info('开始迁移小组数据', tag: _tagName);
      
      final result = GroupMigrationResult();
      
      // 检查是否已经迁移过
      final existingGroups = await _configRepository.getGroups();
      if (existingGroups.isNotEmpty) {
        AppLogger.info('小组数据已存在，跳过迁移', tag: _tagName);
        result.skipped = existingGroups.length;
        return result;
      }
      
      // 从工作人员数据中提取小组信息
      final legacyWorkers = allWorkers;
      final groupsByWarehouse = <String, Set<String>>{};
      
      for (final worker in legacyWorkers) {
        if (!groupsByWarehouse.containsKey(worker.warehouse)) {
          groupsByWarehouse[worker.warehouse] = <String>{};
        }
        groupsByWarehouse[worker.warehouse]!.add(worker.group);
      }
      
      int groupIndex = 1;
      for (final warehouseEntry in groupsByWarehouse.entries) {
        final warehouse = warehouseEntry.key;
        final groups = warehouseEntry.value;
        
        for (final groupName in groups) {
          try {
            // 获取该小组的成员
            final members = legacyWorkers
                .where((w) => w.warehouse == warehouse && w.group == groupName)
                .map((w) => w.id)
                .toList();
            
            final groupConfig = GroupConfig(
              id: 'group_${groupIndex++}',
              name: groupName,
              warehouse: warehouse,
              isActive: true,
              memberIds: members,
              createdAt: DateTime.now(),
              metadata: {
                'migratedFrom': 'worker_info_data.dart',
                'migratedAt': DateTime.now().toIso8601String(),
                'memberCount': members.length,
              },
            );
            
            await _configRepository.saveGroup(groupConfig);
            result.migrated++;
            
            AppLogger.debug('小组迁移成功: $groupName ($warehouse)', tag: _tagName);
          } catch (e) {
            result.failed++;
            result.errors.add('迁移小组失败 $groupName: $e');
            AppLogger.warning('小组迁移失败: $groupName, 错误: $e', tag: _tagName);
          }
        }
      }
      
      AppLogger.info('小组数据迁移完成: 成功${result.migrated}, 失败${result.failed}', tag: _tagName);
      return result;
    } catch (e, stackTrace) {
      AppLogger.error('小组数据迁移失败: $e', tag: _tagName, stackTrace: stackTrace);
      return GroupMigrationResult(
        success: false,
        error: e.toString(),
      );
    }
  }
  
  /// 迁移模板数据
  Future<TemplateMigrationResult> migrateTemplateData() async {
    try {
      AppLogger.info('开始迁移模板数据', tag: _tagName);
      
      final result = TemplateMigrationResult();
      
      // 检查是否已经迁移过
      final existingTemplates = await _configRepository.getTemplates();
      if (existingTemplates.isNotEmpty) {
        AppLogger.info('模板数据已存在，跳过迁移', tag: _tagName);
        result.skipped = existingTemplates.length;
        return result;
      }
      
      // 创建平板车模板
      try {
        final flatbedTemplate = _createFlatbedTemplate();
        await _configRepository.saveTemplate(flatbedTemplate);
        result.migrated++;
        AppLogger.debug('平板车模板迁移成功', tag: _tagName);
      } catch (e) {
        result.failed++;
        result.errors.add('迁移平板车模板失败: $e');
        AppLogger.warning('平板车模板迁移失败: $e', tag: _tagName);
      }
      
      // 创建集装箱模板
      try {
        final containerTemplate = _createContainerTemplate();
        await _configRepository.saveTemplate(containerTemplate);
        result.migrated++;
        AppLogger.debug('集装箱模板迁移成功', tag: _tagName);
      } catch (e) {
        result.failed++;
        result.errors.add('迁移集装箱模板失败: $e');
        AppLogger.warning('集装箱模板迁移失败: $e', tag: _tagName);
      }
      
      AppLogger.info('模板数据迁移完成: 成功${result.migrated}, 失败${result.failed}', tag: _tagName);
      return result;
    } catch (e, stackTrace) {
      AppLogger.error('模板数据迁移失败: $e', tag: _tagName, stackTrace: stackTrace);
      return TemplateMigrationResult(
        success: false,
        error: e.toString(),
      );
    }
  }
  
  /// 创建平板车模板配置
  TemplateConfig _createFlatbedTemplate() {
    final photoConfigs = [
      PhotoConfig(
        id: 'flatbed_front',
        label: '车头照片',
        description: '拍摄车头正面照片',
        isRequired: true,
        needRecognition: false,
        groupId: 'vehicle_group',
      ),
      PhotoConfig(
        id: 'flatbed_cargo',
        label: '货物照片',
        description: '拍摄货物整体照片',
        isRequired: true,
        needRecognition: true,
        groupId: 'cargo_group',
        recognitionSettings: {
          'expectedFormat': 'mixed',
          'enableBatchRecognition': true,
        },
      ),
      PhotoConfig(
        id: 'flatbed_label',
        label: '标签照片',
        description: '拍摄货物标签照片',
        isRequired: true,
        needRecognition: true,
        groupId: 'label_group',
        recognitionSettings: {
          'expectedFormat': 'batch_number',
          'enableProductCodeRecognition': true,
        },
      ),
    ];
    
    final photoGroups = [
      PhotoGroupConfig(
        id: 'vehicle_group',
        name: '车辆信息',
        description: '车辆相关照片',
        isRequired: true,
        photoIds: ['flatbed_front'],
      ),
      PhotoGroupConfig(
        id: 'cargo_group',
        name: '货物信息',
        description: '货物相关照片',
        isRequired: true,
        photoIds: ['flatbed_cargo'],
      ),
      PhotoGroupConfig(
        id: 'label_group',
        name: '标签信息',
        description: '标签相关照片',
        isRequired: true,
        photoIds: ['flatbed_label'],
      ),
    ];
    
    return TemplateConfig(
      id: 'flatbed_template',
      name: '平板车',
      description: '平板车运输任务模板',
      isActive: true,
      photoGroups: photoGroups,
      photoConfigs: photoConfigs,
      createdAt: DateTime.now(),
      settings: {
        'migratedFrom': 'template_config.dart',
        'migratedAt': DateTime.now().toIso8601String(),
        'defaultQuantityUnit': '托',
        'defaultTonnagePerPallet': 1.5,
      },
    );
  }
  
  /// 创建集装箱模板配置
  TemplateConfig _createContainerTemplate() {
    final photoConfigs = [
      PhotoConfig(
        id: 'container_number',
        label: '箱号照片',
        description: '拍摄集装箱号码',
        isRequired: true,
        needRecognition: true,
        groupId: 'container_group',
        recognitionSettings: {
          'expectedFormat': 'container_number',
          'enableContainerValidation': true,
        },
      ),
      PhotoConfig(
        id: 'container_seal',
        label: '铅封照片',
        description: '拍摄铅封号码',
        isRequired: true,
        needRecognition: false,
        groupId: 'security_group',
      ),
      PhotoConfig(
        id: 'container_cargo',
        label: '货物照片',
        description: '拍摄集装箱内货物',
        isRequired: true,
        needRecognition: true,
        groupId: 'cargo_group',
        recognitionSettings: {
          'expectedFormat': 'mixed',
          'enableBatchRecognition': true,
        },
      ),
    ];
    
    final photoGroups = [
      PhotoGroupConfig(
        id: 'container_group',
        name: '集装箱信息',
        description: '集装箱相关照片',
        isRequired: true,
        photoIds: ['container_number'],
      ),
      PhotoGroupConfig(
        id: 'security_group',
        name: '安全信息',
        description: '安全相关照片',
        isRequired: true,
        photoIds: ['container_seal'],
      ),
      PhotoGroupConfig(
        id: 'cargo_group',
        name: '货物信息',
        description: '货物相关照片',
        isRequired: true,
        photoIds: ['container_cargo'],
      ),
    ];
    
    return TemplateConfig(
      id: 'container_template',
      name: '集装箱',
      description: '集装箱运输任务模板',
      isActive: true,
      photoGroups: photoGroups,
      photoConfigs: photoConfigs,
      createdAt: DateTime.now(),
      settings: {
        'migratedFrom': 'template_config.dart',
        'migratedAt': DateTime.now().toIso8601String(),
        'defaultQuantityUnit': '托',
        'defaultTonnagePerPallet': 1.5,
        'requiresContainerValidation': true,
      },
    );
  }
}

/// 迁移结果基类
abstract class BaseMigrationResult {
  bool success;
  int migrated;
  int failed;
  int skipped;
  List<String> errors;
  String? error;
  
  BaseMigrationResult({
    this.success = true,
    this.migrated = 0,
    this.failed = 0,
    this.skipped = 0,
    List<String>? errors,
    this.error,
  }) : errors = errors ?? [];
  
  bool get hasErrors => failed > 0 || error != null;
  int get total => migrated + failed + skipped;
}

/// 完整迁移结果
class MigrationResult extends BaseMigrationResult {
  int workersMigrated = 0;
  int warehousesMigrated = 0;
  int groupsMigrated = 0;
  int templatesMigrated = 0;
  
  void mergeWorkerResult(WorkerMigrationResult result) {
    workersMigrated = result.migrated;
    migrated += result.migrated;
    failed += result.failed;
    skipped += result.skipped;
    errors.addAll(result.errors);
    if (result.error != null) {
      success = false;
      error = result.error;
    }
  }
  
  void mergeWarehouseResult(WarehouseMigrationResult result) {
    warehousesMigrated = result.migrated;
    migrated += result.migrated;
    failed += result.failed;
    skipped += result.skipped;
    errors.addAll(result.errors);
  }
  
  void mergeGroupResult(GroupMigrationResult result) {
    groupsMigrated = result.migrated;
    migrated += result.migrated;
    failed += result.failed;
    skipped += result.skipped;
    errors.addAll(result.errors);
  }
  
  void mergeTemplateResult(TemplateMigrationResult result) {
    templatesMigrated = result.migrated;
    migrated += result.migrated;
    failed += result.failed;
    skipped += result.skipped;
    errors.addAll(result.errors);
  }
  
  @override
  String toString() {
    return 'MigrationResult(success: $success, '
           'workers: $workersMigrated, warehouses: $warehousesMigrated, '
           'groups: $groupsMigrated, templates: $templatesMigrated, '
           'total: $total, failed: $failed, errors: ${errors.length})';
  }
}

/// 工作人员迁移结果
class WorkerMigrationResult extends BaseMigrationResult {
  WorkerMigrationResult({
    super.success,
    super.migrated,
    super.failed,
    super.skipped,
    super.errors,
    super.error,
  });
}

/// 仓库迁移结果
class WarehouseMigrationResult extends BaseMigrationResult {
  WarehouseMigrationResult({
    super.success,
    super.migrated,
    super.failed,
    super.skipped,
    super.errors,
    super.error,
  });
}

/// 小组迁移结果
class GroupMigrationResult extends BaseMigrationResult {
  GroupMigrationResult({
    super.success,
    super.migrated,
    super.failed,
    super.skipped,
    super.errors,
    super.error,
  });
}

/// 模板迁移结果
class TemplateMigrationResult extends BaseMigrationResult {
  TemplateMigrationResult({
    super.success,
    super.migrated,
    super.failed,
    super.skipped,
    super.errors,
    super.error,
  });
}
