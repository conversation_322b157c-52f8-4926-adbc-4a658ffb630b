import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'secure_key_manager.dart';
import 'hardware_fingerprint.dart';

/// 🔍 安全审计日志服务
/// 记录和管理应用的安全相关事件
class SecurityAuditLogger {
  static const String _auditLogKey = 'security_audit_log_v2';
  static const String _logIndexKey = 'audit_log_index_v2';
  static const String _logStatsKey = 'audit_log_stats_v2';
  static const int _maxLogEntries = 1000;
  static const int _maxLogAge = 30; // 天

  /// 记录安全事件
  static Future<void> logSecurityEvent(SecurityEvent event) async {
    try {
      final timestamp = DateTime.now();
      final deviceFingerprint = await HardwareFingerprint.generateFingerprint();

      // 创建日志条目
      final logEntry = SecurityLogEntry(
        id: _generateLogId(),
        timestamp: timestamp,
        deviceFingerprint: deviceFingerprint.substring(0, 8), // 只记录部分指纹
        eventType: event.type,
        severity: event.severity,
        description: event.description,
        metadata: event.metadata,
        sessionId: await _getCurrentSessionId(),
      );

      // 加密并存储日志
      await _appendLogEntry(logEntry);

      // 更新统计信息
      await _updateLogStats(event);

      // 检查是否需要清理旧日志
      await _cleanupOldLogs();
    } catch (e) {
      print('安全日志记录失败: $e');
      // 即使日志记录失败也不应该影响主要功能
    }
  }

  /// 获取安全日志（需要管理员权限）
  static Future<List<SecurityLogEntry>> getSecurityLogs({
    SecurityEventType? eventType,
    SecuritySeverity? severity,
    DateTime? startDate,
    DateTime? endDate,
    int? limit = 100,
  }) async {
    try {
      // 验证访问权限（这里简化处理，实际应该检查用户权限）
      if (!await _hasLogViewPermission()) {
        return [];
      }

      final logs = await _readAllLogs();

      // 应用过滤条件
      var filteredLogs = logs.where((log) {
        bool matches = true;

        if (eventType != null && log.eventType != eventType) {
          matches = false;
        }

        if (severity != null && log.severity != severity) {
          matches = false;
        }

        if (startDate != null && log.timestamp.isBefore(startDate)) {
          matches = false;
        }

        if (endDate != null && log.timestamp.isAfter(endDate)) {
          matches = false;
        }

        return matches;
      }).toList();

      // 按时间倒序排列
      filteredLogs.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // 限制返回数量
      if (limit != null && filteredLogs.length > limit) {
        filteredLogs = filteredLogs.sublist(0, limit);
      }

      return filteredLogs;
    } catch (e) {
      print('读取安全日志失败: $e');
      return [];
    }
  }

  /// 获取安全统计信息
  static Future<SecurityStats> getSecurityStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = prefs.getString(_logStatsKey);

      if (statsJson == null) {
        return SecurityStats.empty();
      }

      final statsData = jsonDecode(statsJson) as Map<String, dynamic>;
      return SecurityStats.fromJson(statsData);
    } catch (e) {
      return SecurityStats.empty();
    }
  }

  /// 检查安全威胁
  static Future<List<SecurityThreat>> detectSecurityThreats() async {
    try {
      final logs = await getSecurityLogs(limit: 500);
      final threats = <SecurityThreat>[];

      // 检测异常登录尝试
      threats.addAll(await _detectAbnormalLoginAttempts(logs));

      // 检测试用期滥用
      threats.addAll(await _detectTrialAbuse(logs));

      // 检测数据篡改尝试
      threats.addAll(await _detectTamperingAttempts(logs));

      // 检测可疑设备活动
      threats.addAll(await _detectSuspiciousDeviceActivity(logs));

      return threats;
    } catch (e) {
      print('安全威胁检测失败: $e');
      return [];
    }
  }

  /// 清除日志（需要超级管理员权限）
  static Future<bool> clearSecurityLogs() async {
    try {
      if (!await _hasSuperAdminPermission()) {
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_auditLogKey);
      await prefs.remove(_logIndexKey);
      await prefs.remove(_logStatsKey);

      // 记录清除事件
      await logSecurityEvent(
        SecurityEvent.systemOperation(
          description: '安全日志已清除',
          metadata: {'operation': 'clear_logs'},
        ),
      );

      return true;
    } catch (e) {
      print('清除安全日志失败: $e');
      return false;
    }
  }

  /// 导出安全报告
  static Future<SecurityReport> generateSecurityReport() async {
    try {
      final stats = await getSecurityStats();
      final threats = await detectSecurityThreats();
      final recentLogs = await getSecurityLogs(limit: 100);

      return SecurityReport(
        generatedAt: DateTime.now(),
        stats: stats,
        threats: threats,
        recentEvents: recentLogs,
        deviceInfo: await HardwareFingerprint.getDeviceSummary(),
      );
    } catch (e) {
      print('生成安全报告失败: $e');
      return SecurityReport.error(e.toString());
    }
  }

  // 私有方法实现

  /// 生成日志ID
  static String _generateLogId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(999999);
    return '${timestamp.toRadixString(36)}_${random.toRadixString(36)}';
  }

  /// 获取当前会话ID
  static Future<String> _getCurrentSessionId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? sessionId = prefs.getString('current_session_id');

      if (sessionId == null) {
        sessionId = _generateSessionId();
        await prefs.setString('current_session_id', sessionId);
      }

      return sessionId;
    } catch (e) {
      return 'UNKNOWN_SESSION';
    }
  }

  /// 生成会话ID
  static String _generateSessionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random.secure().nextInt(999999);
    final data = '$timestamp:$random';
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 16).toUpperCase();
  }

  /// 添加日志条目
  static Future<void> _appendLogEntry(SecurityLogEntry entry) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 读取现有日志
      final existingLogs = await _readAllLogs();
      existingLogs.add(entry);

      // 限制日志数量
      if (existingLogs.length > _maxLogEntries) {
        existingLogs.removeRange(0, existingLogs.length - _maxLogEntries);
      }

      // 加密并存储
      final encryptedLogs = await _encryptLogs(existingLogs);
      await prefs.setString(_auditLogKey, encryptedLogs);

      // 更新索引
      await _updateLogIndex(entry);
    } catch (e) {
      print('添加日志条目失败: $e');
    }
  }

  /// 读取所有日志
  static Future<List<SecurityLogEntry>> _readAllLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedLogs = prefs.getString(_auditLogKey);

      if (encryptedLogs == null) {
        return [];
      }

      return await _decryptLogs(encryptedLogs);
    } catch (e) {
      print('读取日志失败: $e');
      return [];
    }
  }

  /// 加密日志
  static Future<String> _encryptLogs(List<SecurityLogEntry> logs) async {
    try {
      final jsonData = jsonEncode(logs.map((log) => log.toJson()).toList());
      final encryptionKey = SecureKeyManager.getAuditLogKey();

      // 简化的加密实现（实际应用中建议使用更强的加密）
      final bytes = utf8.encode(jsonData);
      final keyBytes = utf8.encode(encryptionKey);

      final encrypted = <int>[];
      for (int i = 0; i < bytes.length; i++) {
        encrypted.add(bytes[i] ^ keyBytes[i % keyBytes.length]);
      }

      return base64Encode(encrypted);
    } catch (e) {
      throw Exception('日志加密失败: $e');
    }
  }

  /// 解密日志
  static Future<List<SecurityLogEntry>> _decryptLogs(
      String encryptedData) async {
    try {
      final encryptionKey = SecureKeyManager.getAuditLogKey();

      // 解密
      final encryptedBytes = base64Decode(encryptedData);
      final keyBytes = utf8.encode(encryptionKey);

      final decrypted = <int>[];
      for (int i = 0; i < encryptedBytes.length; i++) {
        decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }

      final jsonData = utf8.decode(decrypted);
      final logsJson = jsonDecode(jsonData) as List<dynamic>;

      return logsJson.map((json) => SecurityLogEntry.fromJson(json)).toList();
    } catch (e) {
      throw Exception('日志解密失败: $e');
    }
  }

  /// 更新日志索引
  static Future<void> _updateLogIndex(SecurityLogEntry entry) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final indexJson = prefs.getString(_logIndexKey) ?? '{}';
      final index = jsonDecode(indexJson) as Map<String, dynamic>;

      // 按事件类型索引
      final eventTypeKey = entry.eventType.name;
      index[eventTypeKey] = (index[eventTypeKey] ?? 0) + 1;

      // 按严重性索引
      final severityKey = 'severity_${entry.severity.name}';
      index[severityKey] = (index[severityKey] ?? 0) + 1;

      await prefs.setString(_logIndexKey, jsonEncode(index));
    } catch (e) {
      print('更新日志索引失败: $e');
    }
  }

  /// 更新统计信息
  static Future<void> _updateLogStats(SecurityEvent event) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = prefs.getString(_logStatsKey) ?? '{}';
      final statsData = jsonDecode(statsJson) as Map<String, dynamic>;

      final stats = SecurityStats.fromJson(statsData);

      // 更新统计
      stats.totalEvents++;

      switch (event.severity) {
        case SecuritySeverity.low:
          stats.lowSeverityEvents++;
          break;
        case SecuritySeverity.medium:
          stats.mediumSeverityEvents++;
          break;
        case SecuritySeverity.high:
          stats.highSeverityEvents++;
          break;
        case SecuritySeverity.critical:
          stats.criticalSeverityEvents++;
          break;
      }

      stats.lastEventTime = DateTime.now();

      await prefs.setString(_logStatsKey, jsonEncode(stats.toJson()));
    } catch (e) {
      print('更新统计信息失败: $e');
    }
  }

  /// 清理旧日志
  static Future<void> _cleanupOldLogs() async {
    try {
      final logs = await _readAllLogs();
      final cutoffDate = DateTime.now().subtract(Duration(days: _maxLogAge));

      final filteredLogs =
          logs.where((log) => log.timestamp.isAfter(cutoffDate)).toList();

      if (filteredLogs.length != logs.length) {
        final encryptedLogs = await _encryptLogs(filteredLogs);
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_auditLogKey, encryptedLogs);
      }
    } catch (e) {
      print('清理旧日志失败: $e');
    }
  }

  /// 检查日志查看权限
  static Future<bool> _hasLogViewPermission() async {
    // 这里应该检查用户权限
    // 简化实现，实际应用中需要更严格的权限控制
    return true;
  }

  /// 检查超级管理员权限
  static Future<bool> _hasSuperAdminPermission() async {
    // 这里应该检查超级管理员权限
    return false; // 默认不允许清除日志
  }

  // 威胁检测方法

  static Future<List<SecurityThreat>> _detectAbnormalLoginAttempts(
      List<SecurityLogEntry> logs) async {
    final threats = <SecurityThreat>[];

    // 检测短时间内的多次登录失败
    final loginAttempts = logs
        .where((log) =>
            log.eventType == SecurityEventType.loginAttempt ||
            log.eventType == SecurityEventType.licenseActivation)
        .toList();

    if (loginAttempts.length > 5) {
      threats.add(SecurityThreat(
        type: ThreatType.bruteForce,
        severity: SecuritySeverity.high,
        description: '检测到异常登录尝试',
        detectedAt: DateTime.now(),
        evidence: {'attempts': loginAttempts.length},
      ));
    }

    return threats;
  }

  static Future<List<SecurityThreat>> _detectTrialAbuse(
      List<SecurityLogEntry> logs) async {
    final threats = <SecurityThreat>[];

    final trialEvents = logs
        .where((log) =>
            log.eventType == SecurityEventType.trialActivated ||
            log.eventType == SecurityEventType.trialEligibilityCheck)
        .toList();

    if (trialEvents.length > 10) {
      threats.add(SecurityThreat(
        type: ThreatType.trialAbuse,
        severity: SecuritySeverity.medium,
        description: '检测到试用期滥用行为',
        detectedAt: DateTime.now(),
        evidence: {'trial_events': trialEvents.length},
      ));
    }

    return threats;
  }

  static Future<List<SecurityThreat>> _detectTamperingAttempts(
      List<SecurityLogEntry> logs) async {
    final threats = <SecurityThreat>[];

    final tamperingEvents = logs
        .where((log) =>
            log.eventType == SecurityEventType.suspiciousActivity ||
            log.severity == SecuritySeverity.critical)
        .toList();

    if (tamperingEvents.isNotEmpty) {
      threats.add(SecurityThreat(
        type: ThreatType.tampering,
        severity: SecuritySeverity.high,
        description: '检测到数据篡改尝试',
        detectedAt: DateTime.now(),
        evidence: {'tampering_events': tamperingEvents.length},
      ));
    }

    return threats;
  }

  static Future<List<SecurityThreat>> _detectSuspiciousDeviceActivity(
      List<SecurityLogEntry> logs) async {
    final threats = <SecurityThreat>[];

    final deviceFingerprints = logs.map((log) => log.deviceFingerprint).toSet();

    if (deviceFingerprints.length > 3) {
      threats.add(SecurityThreat(
        type: ThreatType.deviceSpoofing,
        severity: SecuritySeverity.medium,
        description: '检测到可疑的设备活动',
        detectedAt: DateTime.now(),
        evidence: {'unique_devices': deviceFingerprints.length},
      ));
    }

    return threats;
  }
}

// 数据类定义

/// 安全事件类型
enum SecurityEventType {
  licenseActivation,
  licenseValidation,
  trialActivated,
  trialStarted, // 🔧 新增：试用期开始
  trialEligibilityCheck,
  loginAttempt,
  dataAccess,
  configChange,
  suspiciousActivity,
  systemOperation,
  error,
}

/// 安全严重性级别
enum SecuritySeverity {
  low,
  medium,
  high,
  critical,
}

/// 威胁类型
enum ThreatType {
  bruteForce,
  trialAbuse,
  tampering,
  deviceSpoofing,
  unauthorizedAccess,
}

/// 安全事件
class SecurityEvent {
  final SecurityEventType type;
  final SecuritySeverity severity;
  final String description;
  final Map<String, dynamic> metadata;

  SecurityEvent({
    required this.type,
    required this.severity,
    required this.description,
    this.metadata = const {},
  });

  factory SecurityEvent.licenseActivation({
    required String description,
    Map<String, dynamic> metadata = const {},
  }) =>
      SecurityEvent(
        type: SecurityEventType.licenseActivation,
        severity: SecuritySeverity.medium,
        description: description,
        metadata: metadata,
      );

  factory SecurityEvent.trialActivated({
    required String description,
    Map<String, dynamic> metadata = const {},
  }) =>
      SecurityEvent(
        type: SecurityEventType.trialActivated,
        severity: SecuritySeverity.low,
        description: description,
        metadata: metadata,
      );

  factory SecurityEvent.trialEligibilityCheck({
    required String description,
    Map<String, dynamic> metadata = const {},
  }) =>
      SecurityEvent(
        type: SecurityEventType.trialEligibilityCheck,
        severity: SecuritySeverity.low,
        description: description,
        metadata: metadata,
      );

  factory SecurityEvent.suspiciousActivity({
    required String description,
    Map<String, dynamic> metadata = const {},
  }) =>
      SecurityEvent(
        type: SecurityEventType.suspiciousActivity,
        severity: SecuritySeverity.high,
        description: description,
        metadata: metadata,
      );

  factory SecurityEvent.systemOperation({
    required String description,
    Map<String, dynamic> metadata = const {},
  }) =>
      SecurityEvent(
        type: SecurityEventType.systemOperation,
        severity: SecuritySeverity.medium,
        description: description,
        metadata: metadata,
      );

  factory SecurityEvent.trialError({
    required String description,
    Map<String, dynamic> metadata = const {},
  }) =>
      SecurityEvent(
        type: SecurityEventType.error,
        severity: SecuritySeverity.medium,
        description: description,
        metadata: metadata,
      );

  factory SecurityEvent.dataAccess({
    required String description,
    Map<String, dynamic> metadata = const {},
  }) =>
      SecurityEvent(
        type: SecurityEventType.dataAccess,
        severity: SecuritySeverity.low,
        description: description,
        metadata: metadata,
      );

  factory SecurityEvent.trialStarted({
    required String description,
    Map<String, dynamic> metadata = const {},
  }) =>
      SecurityEvent(
        type: SecurityEventType.trialStarted,
        severity: SecuritySeverity.low,
        description: description,
        metadata: metadata,
      );
}

/// 安全日志条目
class SecurityLogEntry {
  final String id;
  final DateTime timestamp;
  final String deviceFingerprint;
  final SecurityEventType eventType;
  final SecuritySeverity severity;
  final String description;
  final Map<String, dynamic> metadata;
  final String sessionId;

  SecurityLogEntry({
    required this.id,
    required this.timestamp,
    required this.deviceFingerprint,
    required this.eventType,
    required this.severity,
    required this.description,
    required this.metadata,
    required this.sessionId,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'timestamp': timestamp.toIso8601String(),
        'deviceFingerprint': deviceFingerprint,
        'eventType': eventType.name,
        'severity': severity.name,
        'description': description,
        'metadata': metadata,
        'sessionId': sessionId,
      };

  factory SecurityLogEntry.fromJson(Map<String, dynamic> json) =>
      SecurityLogEntry(
        id: json['id'],
        timestamp: DateTime.parse(json['timestamp']),
        deviceFingerprint: json['deviceFingerprint'],
        eventType: SecurityEventType.values
            .firstWhere((e) => e.name == json['eventType']),
        severity: SecuritySeverity.values
            .firstWhere((e) => e.name == json['severity']),
        description: json['description'],
        metadata: json['metadata'] ?? {},
        sessionId: json['sessionId'],
      );
}

/// 安全统计信息
class SecurityStats {
  int totalEvents;
  int lowSeverityEvents;
  int mediumSeverityEvents;
  int highSeverityEvents;
  int criticalSeverityEvents;
  DateTime? lastEventTime;

  SecurityStats({
    this.totalEvents = 0,
    this.lowSeverityEvents = 0,
    this.mediumSeverityEvents = 0,
    this.highSeverityEvents = 0,
    this.criticalSeverityEvents = 0,
    this.lastEventTime,
  });

  factory SecurityStats.empty() => SecurityStats();

  Map<String, dynamic> toJson() => {
        'totalEvents': totalEvents,
        'lowSeverityEvents': lowSeverityEvents,
        'mediumSeverityEvents': mediumSeverityEvents,
        'highSeverityEvents': highSeverityEvents,
        'criticalSeverityEvents': criticalSeverityEvents,
        'lastEventTime': lastEventTime?.toIso8601String(),
      };

  factory SecurityStats.fromJson(Map<String, dynamic> json) => SecurityStats(
        totalEvents: json['totalEvents'] ?? 0,
        lowSeverityEvents: json['lowSeverityEvents'] ?? 0,
        mediumSeverityEvents: json['mediumSeverityEvents'] ?? 0,
        highSeverityEvents: json['highSeverityEvents'] ?? 0,
        criticalSeverityEvents: json['criticalSeverityEvents'] ?? 0,
        lastEventTime: json['lastEventTime'] != null
            ? DateTime.parse(json['lastEventTime'])
            : null,
      );
}

/// 安全威胁
class SecurityThreat {
  final ThreatType type;
  final SecuritySeverity severity;
  final String description;
  final DateTime detectedAt;
  final Map<String, dynamic> evidence;

  SecurityThreat({
    required this.type,
    required this.severity,
    required this.description,
    required this.detectedAt,
    this.evidence = const {},
  });
}

/// 安全报告
class SecurityReport {
  final DateTime generatedAt;
  final SecurityStats stats;
  final List<SecurityThreat> threats;
  final List<SecurityLogEntry> recentEvents;
  final Map<String, String> deviceInfo;
  final String? error;

  SecurityReport({
    required this.generatedAt,
    required this.stats,
    required this.threats,
    required this.recentEvents,
    required this.deviceInfo,
    this.error,
  });

  factory SecurityReport.error(String error) => SecurityReport(
        generatedAt: DateTime.now(),
        stats: SecurityStats.empty(),
        threats: [],
        recentEvents: [],
        deviceInfo: {},
        error: error,
      );
}
