import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import '../../utils/common_utils.dart';

/// 📤 上传队列管理器
/// 负责管理图片上传队列，控制并发数量，优化性能
class UploadQueueManager {
  static final UploadQueueManager _instance = UploadQueueManager._internal();
  factory UploadQueueManager() => _instance;
  UploadQueueManager._internal();

  /// 上传队列
  final Queue<UploadTask> _uploadQueue = Queue();

  /// 正在处理的任务
  final Set<UploadTask> _processingTasks = {};

  /// 最大并发数
  int _maxConcurrent = 3;

  /// 队列状态
  bool _isProcessing = false;

  /// 队列状态流控制器
  final StreamController<QueueStatus> _statusController =
      StreamController<QueueStatus>.broadcast();

  /// 队列状态流
  Stream<QueueStatus> get statusStream => _statusController.stream;

  /// 设置最大并发数
  void setMaxConcurrent(int max) {
    _maxConcurrent = max.clamp(1, 10);
  }

  /// 添加上传任务
  Future<void> addTask(UploadTask task) async {
    _uploadQueue.add(task);
    _updateStatus();

    if (!_isProcessing) {
      _processQueue();
    }
  }

  /// 处理队列
  Future<void> _processQueue() async {
    if (_isProcessing) return;

    _isProcessing = true;

    while (_uploadQueue.isNotEmpty || _processingTasks.isNotEmpty) {
      // 启动新的任务
      while (
          _processingTasks.length < _maxConcurrent && _uploadQueue.isNotEmpty) {
        final task = _uploadQueue.removeFirst();
        _processingTasks.add(task);

        _processTask(task).then((_) {
          _processingTasks.remove(task);
          _updateStatus();
        }).catchError((error) {
          _processingTasks.remove(task);
          _updateStatus();
          debugPrint('任务处理失败: $error');
        });
      }

      // 等待一段时间再检查
      await Future.delayed(const Duration(milliseconds: 100));
    }

    _isProcessing = false;
    _updateStatus();
  }

  /// 处理单个任务
  Future<void> _processTask(UploadTask task) async {
    try {
      task.status = UploadStatus.uploading;
      _updateStatus();

      // 图片压缩
      await CommonUtils.compressImage(
        task.imageData,
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 85,
      );

      // 模拟上传过程
      await Future.delayed(
          Duration(milliseconds: 500 + (task.imageData.length / 1000).round()));

      task.status = UploadStatus.completed;
      task.result = '上传成功';
    } catch (e) {
      task.status = UploadStatus.failed;
      task.error = e.toString();
      rethrow;
    }
  }

  /// 更新状态
  void _updateStatus() {
    final status = QueueStatus(
      queueLength: _uploadQueue.length,
      processingCount: _processingTasks.length,
      maxConcurrent: _maxConcurrent,
      isProcessing: _isProcessing,
    );
    _statusController.add(status);
  }

  /// 清空队列
  void clearQueue() {
    _uploadQueue.clear();
    _processingTasks.clear();
    _updateStatus();
  }

  /// 暂停队列
  void pauseQueue() {
    _isProcessing = false;
    _updateStatus();
  }

  /// 恢复队列
  void resumeQueue() {
    if (_uploadQueue.isNotEmpty || _processingTasks.isNotEmpty) {
      _processQueue();
    }
  }

  /// 获取队列状态
  QueueStatus getCurrentStatus() {
    return QueueStatus(
      queueLength: _uploadQueue.length,
      processingCount: _processingTasks.length,
      maxConcurrent: _maxConcurrent,
      isProcessing: _isProcessing,
    );
  }

  /// 释放资源
  void dispose() {
    _statusController.close();
  }
}

/// 📋 上传任务
class UploadTask {
  final String id;
  final Uint8List imageData;
  final String fileName;
  final DateTime createdAt;

  UploadStatus status;
  String? result;
  String? error;
  double progress;

  UploadTask({
    required this.id,
    required this.imageData,
    required this.fileName,
    DateTime? createdAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        status = UploadStatus.queued,
        progress = 0.0;
}

/// 📊 上传状态
enum UploadStatus {
  queued, // 排队中
  uploading, // 上传中
  recognizing, // 识别中
  completed, // 已完成
  failed, // 失败
}

/// 📈 队列状态
class QueueStatus {
  final int queueLength;
  final int processingCount;
  final int maxConcurrent;
  final bool isProcessing;

  QueueStatus({
    required this.queueLength,
    required this.processingCount,
    required this.maxConcurrent,
    required this.isProcessing,
  });

  bool get isIdle => !isProcessing && queueLength == 0 && processingCount == 0;
  bool get isFull => processingCount >= maxConcurrent;
}
