import 'dart:async';
import 'package:flutter/material.dart';
import '../exceptions/app_exceptions.dart';
import '../utils/app_logger.dart';

/// 全局错误处理器
/// 提供统一的错误处理、日志记录和用户通知
class GlobalErrorHandler {
  static const String _tagName = 'GlobalErrorHandler';
  
  static GlobalKey<ScaffoldMessengerState>? _scaffoldMessengerKey;
  static final List<ErrorListener> _listeners = [];
  
  /// 初始化全局错误处理器
  static void initialize({
    GlobalKey<ScaffoldMessengerState>? scaffoldMessengerKey,
  }) {
    _scaffoldMessengerKey = scaffoldMessengerKey;
    
    // 设置Flutter框架错误处理
    FlutterError.onError = (FlutterErrorDetails details) {
      handleFlutterError(details);
    };
    
    // 设置Zone错误处理
    runZonedGuarded(() {
      // 应用启动代码会在这里运行
    }, (error, stackTrace) {
      handleError(
        error,
        stackTrace: stackTrace,
        context: 'Zone Error',
      );
    });
    
    AppLogger.info('全局错误处理器初始化完成', tag: _tagName);
  }
  
  /// 处理应用错误
  static void handleError(
    Object error, {
    StackTrace? stackTrace,
    String? context,
    bool showToUser = true,
    ErrorSeverity? forceSeverity,
  }) {
    try {
      // 转换为应用异常
      final appException = error is AppException 
          ? error 
          : AppExceptionFactory.fromException(error, stackTrace: stackTrace);
      
      // 确定错误严重程度
      final severity = forceSeverity ?? appException.severity;
      
      // 记录错误日志
      _logError(appException, context, severity);
      
      // 通知监听器
      _notifyListeners(appException, context);
      
      // 显示用户通知
      if (showToUser) {
        _showUserNotification(appException, severity);
      }
      
      // 根据严重程度执行特殊处理
      _handleBySeverity(appException, severity);
      
    } catch (handlerError) {
      // 错误处理器本身出错，使用基础日志记录
      AppLogger.error(
        '错误处理器处理失败: $handlerError, 原始错误: $error',
        tag: _tagName,
        stackTrace: stackTrace,
      );
    }
  }
  
  /// 处理Flutter框架错误
  static void handleFlutterError(FlutterErrorDetails details) {
    // 记录Flutter错误
    AppLogger.error(
      'Flutter Error: ${details.exception}',
      tag: _tagName,
      stackTrace: details.stack,
    );
    
    // 转换为应用异常并处理
    final appException = AppExceptionFactory.fromException(
      details.exception,
      stackTrace: details.stack,
    );
    
    _notifyListeners(appException, 'Flutter Framework');
    
    // 根据是否为调试模式决定是否显示红屏
    if (details.silent) {
      return;
    }
    
    // 在发布模式下，显示用户友好的错误信息
    _showUserNotification(appException, ErrorSeverity.high);
  }
  
  /// 处理异步错误
  static Future<T> handleAsync<T>(
    Future<T> Function() operation, {
    String? context,
    T? fallbackValue,
    bool showToUser = true,
  }) async {
    try {
      return await operation();
    } catch (error, stackTrace) {
      handleError(
        error,
        stackTrace: stackTrace,
        context: context,
        showToUser: showToUser,
      );
      
      if (fallbackValue != null) {
        return fallbackValue;
      }
      
      rethrow;
    }
  }
  
  /// 处理同步错误
  static T handleSync<T>(
    T Function() operation, {
    String? context,
    T? fallbackValue,
    bool showToUser = true,
  }) {
    try {
      return operation();
    } catch (error, stackTrace) {
      handleError(
        error,
        stackTrace: stackTrace,
        context: context,
        showToUser: showToUser,
      );
      
      if (fallbackValue != null) {
        return fallbackValue;
      }
      
      rethrow;
    }
  }
  
  /// 添加错误监听器
  static void addListener(ErrorListener listener) {
    _listeners.add(listener);
  }
  
  /// 移除错误监听器
  static void removeListener(ErrorListener listener) {
    _listeners.remove(listener);
  }
  
  /// 记录错误日志
  static void _logError(
    AppException exception,
    String? context,
    ErrorSeverity severity,
  ) {
    final contextInfo = context != null ? ' in $context' : '';
    final message = '${severity.name.toUpperCase()} ERROR$contextInfo: ${exception.message}';
    
    switch (severity) {
      case ErrorSeverity.low:
        AppLogger.info(message, tag: _tagName);
        break;
      case ErrorSeverity.medium:
        AppLogger.warning(message, tag: _tagName);
        break;
      case ErrorSeverity.high:
      case ErrorSeverity.critical:
        AppLogger.error(
          message,
          tag: _tagName,
          stackTrace: exception.stackTrace,
        );
        break;
    }
    
    // 记录详细信息
    if (exception.metadata != null) {
      AppLogger.debug('Error metadata: ${exception.metadata}', tag: _tagName);
    }
    
    if (exception.originalError != null) {
      AppLogger.debug('Original error: ${exception.originalError}', tag: _tagName);
    }
  }
  
  /// 通知监听器
  static void _notifyListeners(AppException exception, String? context) {
    for (final listener in _listeners) {
      try {
        listener.onError(exception, context);
      } catch (e) {
        AppLogger.warning('错误监听器通知失败: $e', tag: _tagName);
      }
    }
  }
  
  /// 显示用户通知
  static void _showUserNotification(AppException exception, ErrorSeverity severity) {
    final messenger = _scaffoldMessengerKey?.currentState;
    if (messenger == null) {
      AppLogger.warning('ScaffoldMessenger不可用，无法显示用户通知', tag: _tagName);
      return;
    }
    
    // 根据严重程度选择颜色和图标
    Color backgroundColor;
    IconData icon;
    Duration duration;
    
    switch (severity) {
      case ErrorSeverity.low:
        backgroundColor = Colors.orange;
        icon = Icons.warning;
        duration = const Duration(seconds: 3);
        break;
      case ErrorSeverity.medium:
        backgroundColor = Colors.red;
        icon = Icons.error;
        duration = const Duration(seconds: 5);
        break;
      case ErrorSeverity.high:
      case ErrorSeverity.critical:
        backgroundColor = Colors.red[800]!;
        icon = Icons.error_outline;
        duration = const Duration(seconds: 8);
        break;
    }
    
    messenger.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                exception.displayMessage,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: duration,
        action: severity == ErrorSeverity.high || severity == ErrorSeverity.critical
            ? SnackBarAction(
                label: '详情',
                textColor: Colors.white,
                onPressed: () => _showErrorDetails(exception),
              )
            : null,
      ),
    );
  }
  
  /// 根据严重程度处理
  static void _handleBySeverity(AppException exception, ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.low:
      case ErrorSeverity.medium:
        // 低和中等严重程度，只记录和通知
        break;
      case ErrorSeverity.high:
        // 高严重程度，可能需要特殊处理
        _handleHighSeverityError(exception);
        break;
      case ErrorSeverity.critical:
        // 严重错误，可能需要应用重启或其他紧急措施
        _handleCriticalError(exception);
        break;
    }
  }
  
  /// 处理高严重程度错误
  static void _handleHighSeverityError(AppException exception) {
    AppLogger.error('高严重程度错误需要特殊处理: ${exception.message}', tag: _tagName);
    
    // 这里可以添加特殊处理逻辑，比如：
    // - 清理缓存
    // - 重置状态
    // - 发送错误报告
  }
  
  /// 处理严重错误
  static void _handleCriticalError(AppException exception) {
    AppLogger.error('严重错误，系统可能需要重启: ${exception.message}', tag: _tagName);
    
    // 这里可以添加紧急处理逻辑，比如：
    // - 保存关键数据
    // - 发送紧急报告
    // - 提示用户重启应用
  }
  
  /// 显示错误详情
  static void _showErrorDetails(AppException exception) {
    final context = _scaffoldMessengerKey?.currentContext;
    if (context == null) return;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red),
            const SizedBox(width: 8),
            const Text('错误详情'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('错误消息: ${exception.message}'),
              if (exception.errorCode != null) ...[
                const SizedBox(height: 8),
                Text('错误代码: ${exception.errorCode}'),
              ],
              if (exception.originalError != null) ...[
                const SizedBox(height: 8),
                Text('原始错误: ${exception.originalError}'),
              ],
              const SizedBox(height: 8),
              Text('严重程度: ${exception.severity.name}'),
              Text('错误类型: ${exception.isUserError ? "用户错误" : "系统错误"}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}

/// 错误监听器接口
abstract class ErrorListener {
  void onError(AppException exception, String? context);
}

/// 错误统计监听器
class ErrorStatisticsListener implements ErrorListener {
  final Map<String, int> _errorCounts = {};
  final Map<ErrorSeverity, int> _severityCounts = {};
  
  @override
  void onError(AppException exception, String? context) {
    // 统计错误类型
    final errorType = exception.runtimeType.toString();
    _errorCounts[errorType] = (_errorCounts[errorType] ?? 0) + 1;
    
    // 统计严重程度
    _severityCounts[exception.severity] = (_severityCounts[exception.severity] ?? 0) + 1;
  }
  
  /// 获取错误统计
  Map<String, dynamic> getStatistics() {
    return {
      'errorCounts': Map.from(_errorCounts),
      'severityCounts': _severityCounts.map((k, v) => MapEntry(k.name, v)),
      'totalErrors': _errorCounts.values.fold(0, (sum, count) => sum + count),
    };
  }
  
  /// 重置统计
  void reset() {
    _errorCounts.clear();
    _severityCounts.clear();
  }
}
