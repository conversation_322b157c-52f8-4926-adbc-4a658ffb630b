import 'dart:async';
import 'dart:collection';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/services/task_service.dart';

/// 🗄️ 批量数据库操作服务 - 数据库性能优化
///
/// 将多个数据库操作批量处理，减少I/O开销
/// 提升数据持久化性能
class BatchDatabaseService {
  static final BatchDatabaseService _instance =
      BatchDatabaseService._internal();
  factory BatchDatabaseService() => _instance;
  BatchDatabaseService._internal();

  // 批处理配置
  static const int _maxBatchSize = 50;
  static const Duration _batchTimeout = Duration(seconds: 2);

  // 批处理队列
  final Queue<DatabaseOperation> _operationQueue = Queue<DatabaseOperation>();
  final Map<String, Completer<void>> _operationCompleters = {};

  Timer? _batchTimer;
  bool _isProcessing = false;
  bool _isInitialized = false;

  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    _isInitialized = true;
    print('🗄️ 批量数据库服务初始化完成');
  }

  /// 📝 批量保存任务
  Future<void> saveTaskBatch(TaskModel task) async {
    if (!_isInitialized) await initialize();

    final operation = DatabaseOperation(
      type: DatabaseOperationType.saveTask,
      data: task,
      id: task.id,
    );

    return _addOperation(operation);
  }

  /// 📝 批量更新照片
  Future<void> updatePhotoBatch(
      String taskId, String photoId, String imagePath) async {
    if (!_isInitialized) await initialize();

    final operation = DatabaseOperation(
      type: DatabaseOperationType.updatePhoto,
      data: {
        'taskId': taskId,
        'photoId': photoId,
        'imagePath': imagePath,
      },
      id: '${taskId}_$photoId',
    );

    return _addOperation(operation);
  }

  /// 📝 批量更新识别结果
  Future<void> updateRecognitionResultBatch(
      String taskId, String photoId, RecognitionResult result) async {
    if (!_isInitialized) await initialize();

    final operation = DatabaseOperation(
      type: DatabaseOperationType.updateRecognition,
      data: {
        'taskId': taskId,
        'photoId': photoId,
        'result': result,
      },
      id: '${taskId}_${photoId}_recognition',
    );

    return _addOperation(operation);
  }

  /// ➕ 添加操作到队列
  Future<void> _addOperation(DatabaseOperation operation) async {
    final completer = Completer<void>();
    _operationCompleters[operation.id] = completer;

    _operationQueue.add(operation);

    // 如果队列达到最大大小，立即处理
    if (_operationQueue.length >= _maxBatchSize) {
      _processBatch();
    } else {
      // 否则启动或重启定时器
      _restartBatchTimer();
    }

    return completer.future;
  }

  /// ⏰ 重启批处理定时器
  void _restartBatchTimer() {
    _batchTimer?.cancel();
    _batchTimer = Timer(_batchTimeout, () {
      if (_operationQueue.isNotEmpty) {
        _processBatch();
      }
    });
  }

  /// 🔄 处理批次
  Future<void> _processBatch() async {
    if (_isProcessing || _operationQueue.isEmpty) return;

    _isProcessing = true;
    _batchTimer?.cancel();

    try {
      final batch = <DatabaseOperation>[];

      // 收集当前批次的操作
      while (_operationQueue.isNotEmpty && batch.length < _maxBatchSize) {
        batch.add(_operationQueue.removeFirst());
      }

      print('🗄️ 开始处理数据库批次，操作数: ${batch.length}');

      // 按类型分组操作
      final groupedOperations = _groupOperationsByType(batch);

      // 并行执行不同类型的操作
      await Future.wait([
        _processSaveTaskOperations(
            groupedOperations[DatabaseOperationType.saveTask] ?? []),
        _processUpdatePhotoOperations(
            groupedOperations[DatabaseOperationType.updatePhoto] ?? []),
        _processUpdateRecognitionOperations(
            groupedOperations[DatabaseOperationType.updateRecognition] ?? []),
      ]);

      // 完成所有操作的Future
      for (final operation in batch) {
        final completer = _operationCompleters.remove(operation.id);
        completer?.complete();
      }

      print('✅ 数据库批次处理完成');
    } catch (e) {
      print('❌ 数据库批次处理失败: $e');

      // 标记所有操作为失败
      for (final operation in _operationQueue) {
        final completer = _operationCompleters.remove(operation.id);
        completer?.completeError(e);
      }
    } finally {
      _isProcessing = false;

      // 如果还有操作在队列中，继续处理
      if (_operationQueue.isNotEmpty) {
        _restartBatchTimer();
      }
    }
  }

  /// 📊 按类型分组操作
  Map<DatabaseOperationType, List<DatabaseOperation>> _groupOperationsByType(
    List<DatabaseOperation> operations,
  ) {
    final grouped = <DatabaseOperationType, List<DatabaseOperation>>{};

    for (final operation in operations) {
      grouped.putIfAbsent(operation.type, () => []).add(operation);
    }

    return grouped;
  }

  /// 💾 处理保存任务操作
  Future<void> _processSaveTaskOperations(
      List<DatabaseOperation> operations) async {
    if (operations.isEmpty) return;

    final taskService = TaskService();

    for (final operation in operations) {
      final task = operation.data as TaskModel;
      await taskService.saveTaskDirectly(task);
    }

    print('💾 批量保存了 ${operations.length} 个任务');
  }

  /// 📸 处理更新照片操作
  Future<void> _processUpdatePhotoOperations(
      List<DatabaseOperation> operations) async {
    if (operations.isEmpty) return;

    final taskService = TaskService();

    // 按任务ID分组
    final taskGroups = <String, List<DatabaseOperation>>{};
    for (final operation in operations) {
      final data = operation.data as Map<String, dynamic>;
      final taskId = data['taskId'] as String;
      taskGroups.putIfAbsent(taskId, () => []).add(operation);
    }

    // 为每个任务批量更新照片
    for (final entry in taskGroups.entries) {
      final taskId = entry.key;
      final photoOperations = entry.value;

      for (final operation in photoOperations) {
        final data = operation.data as Map<String, dynamic>;
        await taskService.updatePhotoDirectly(
          taskId,
          data['photoId'] as String,
          data['imagePath'] as String,
        );
      }
    }

    print('📸 批量更新了 ${operations.length} 个照片');
  }

  /// 🔍 处理更新识别结果操作
  Future<void> _processUpdateRecognitionOperations(
      List<DatabaseOperation> operations) async {
    if (operations.isEmpty) return;

    final taskService = TaskService();

    for (final operation in operations) {
      final data = operation.data as Map<String, dynamic>;
      await taskService.updateRecognitionResultDirectly(
        data['taskId'] as String,
        data['photoId'] as String,
        data['result'] as RecognitionResult,
      );
    }

    print('🔍 批量更新了 ${operations.length} 个识别结果');
  }

  /// 🚀 强制处理所有待处理的操作
  Future<void> flushAll() async {
    while (_operationQueue.isNotEmpty) {
      await _processBatch();
    }
  }

  /// 📊 获取队列状态
  Map<String, dynamic> getQueueStatus() {
    return {
      'queueSize': _operationQueue.length,
      'isProcessing': _isProcessing,
      'pendingCompleters': _operationCompleters.length,
    };
  }

  /// 🧹 清理服务
  void dispose() {
    _batchTimer?.cancel();

    // 完成所有待处理的操作
    for (final completer in _operationCompleters.values) {
      if (!completer.isCompleted) {
        completer.completeError('Service disposed');
      }
    }

    _operationQueue.clear();
    _operationCompleters.clear();

    print('🗄️ 批量数据库服务已清理');
  }

  /// 📊 获取批次统计
  Map<String, dynamic> getBatchStats() {
    return {
      'pending_operations': _operationQueue.length,
      'processing': _isProcessing,
      'pending_completers': _operationCompleters.length,
      'max_batch_size': _maxBatchSize,
      'batch_timeout_seconds': _batchTimeout.inSeconds,
    };
  }
}

/// 📋 数据库操作
class DatabaseOperation {
  final DatabaseOperationType type;
  final dynamic data;
  final String id;
  final DateTime createdAt;

  DatabaseOperation({
    required this.type,
    required this.data,
    required this.id,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();
}

/// 📋 数据库操作类型
enum DatabaseOperationType {
  saveTask, // 保存任务
  updatePhoto, // 更新照片
  updateRecognition, // 更新识别结果
}
