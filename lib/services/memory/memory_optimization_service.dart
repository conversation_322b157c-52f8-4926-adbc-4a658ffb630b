import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../../utils/common_utils.dart';

/// 🧠 内存优化服务
/// 负责监控内存使用情况，提供内存清理和优化功能
class MemoryOptimizationService {
  static final MemoryOptimizationService _instance =
      MemoryOptimizationService._internal();
  factory MemoryOptimizationService() => _instance;
  MemoryOptimizationService._internal();

  /// 内存监控定时器
  Timer? _monitorTimer;

  /// 内存使用历史
  final List<MemoryInfo> _memoryHistory = [];

  /// 最大历史记录数
  static const int _maxHistorySize = 100;

  /// 内存阈值（MB）
  static const double _warningThreshold = 512.0; // 512MB
  static const double _criticalThreshold = 1024.0; // 1GB

  /// 内存状态流控制器
  final StreamController<MemoryInfo> _memoryController =
      StreamController<MemoryInfo>.broadcast();

  /// 内存状态流
  Stream<MemoryInfo> get memoryStream => _memoryController.stream;

  /// 启动内存监控
  void startMonitoring({Duration interval = const Duration(seconds: 30)}) {
    if (_monitorTimer != null) {
      _monitorTimer!.cancel();
    }

    _monitorTimer = Timer.periodic(interval, (timer) {
      _checkMemoryUsage();
    });

    // 立即检查一次
    _checkMemoryUsage();
  }

  /// 停止内存监控
  void stopMonitoring() {
    _monitorTimer?.cancel();
    _monitorTimer = null;
  }

  /// 检查内存使用情况
  Future<void> _checkMemoryUsage() async {
    try {
      final memoryInfo = await _getMemoryInfo();
      _memoryHistory.add(memoryInfo);

      // 限制历史记录数量
      if (_memoryHistory.length > _maxHistorySize) {
        _memoryHistory.removeAt(0);
      }

      // 发送内存信息
      _memoryController.add(memoryInfo);

      // 检查是否需要清理
      if (memoryInfo.usedMB > _criticalThreshold) {
        await _performCriticalCleanup();
      } else if (memoryInfo.usedMB > _warningThreshold) {
        await _performWarningCleanup();
      }
    } catch (e) {
      // debugPrint('内存监控失败: $e');
    }
  }

  /// 获取内存信息
  Future<MemoryInfo> _getMemoryInfo() async {
    try {
      if (Platform.isAndroid) {
        return await _getAndroidMemoryInfo();
      } else if (Platform.isIOS) {
        return await _getIOSMemoryInfo();
      } else {
        return _getDefaultMemoryInfo();
      }
    } catch (e) {
      // debugPrint('获取内存信息失败: $e');
      return _getDefaultMemoryInfo();
    }
  }

  /// 获取Android内存信息
  Future<MemoryInfo> _getAndroidMemoryInfo() async {
    // 这里可以集成Android原生内存监控
    // 目前返回模拟数据
    return MemoryInfo(
      timestamp: DateTime.now(),
      totalMB: 4096.0,
      availableMB: 2048.0,
      usedMB: 2048.0,
      platform: 'Android',
    );
  }

  /// 获取iOS内存信息
  Future<MemoryInfo> _getIOSMemoryInfo() async {
    // 这里可以集成iOS原生内存监控
    // 目前返回模拟数据
    return MemoryInfo(
      timestamp: DateTime.now(),
      totalMB: 4096.0,
      availableMB: 2048.0,
      usedMB: 2048.0,
      platform: 'iOS',
    );
  }

  /// 获取默认内存信息
  MemoryInfo _getDefaultMemoryInfo() {
    return MemoryInfo(
      timestamp: DateTime.now(),
      totalMB: 4096.0,
      availableMB: 2048.0,
      usedMB: 2048.0,
      platform: 'Unknown',
    );
  }

  /// 执行警告级别清理
  Future<void> _performWarningCleanup() async {
    // debugPrint('执行警告级别内存清理');

    // 清理图片缓存
    await _clearImageCache();

    // 清理临时文件
    await _clearTempFiles();

    // 建议垃圾回收
    _suggestGarbageCollection();
  }

  /// 执行严重级别清理
  Future<void> _performCriticalCleanup() async {
    // debugPrint('执行严重级别内存清理');

    // 强制清理所有缓存
    await _clearAllCaches();

    // 强制垃圾回收
    _forceGarbageCollection();

    // 发送内存警告
    _sendMemoryWarning();
  }

  /// 清理图片缓存
  Future<void> _clearImageCache() async {
    try {
      // 这里可以集成具体的图片缓存清理
      // debugPrint('清理图片缓存');
    } catch (e) {
      // debugPrint('清理图片缓存失败: $e');
    }
  }

  /// 清理临时文件
  Future<void> _clearTempFiles() async {
    try {
      final tempPath = await CommonUtils.getAppCachePath();
      final tempDir = Directory(tempPath);

      if (await tempDir.exists()) {
        final files = tempDir.listSync();
        for (final file in files) {
          if (file is File) {
            final stat = await file.stat();
            final age = DateTime.now().difference(stat.modified);

            // 删除超过1小时的临时文件
            if (age.inHours > 1) {
              await CommonUtils.safeDeleteFile(file.path);
            }
          }
        }
      }
    } catch (e) {
      // debugPrint('清理临时文件失败: $e');
    }
  }

  /// 清理所有缓存
  Future<void> _clearAllCaches() async {
    try {
      await _clearImageCache();
      await _clearTempFiles();

      // 清理其他缓存
      // debugPrint('清理所有缓存');
    } catch (e) {
      // debugPrint('清理所有缓存失败: $e');
    }
  }

  /// 建议垃圾回收
  void _suggestGarbageCollection() {
    // 在Dart中，垃圾回收是自动的
    // 这里可以添加一些提示或日志
    // debugPrint('建议进行垃圾回收');
  }

  /// 强制垃圾回收
  void _forceGarbageCollection() {
    // 在Dart中，无法直接强制垃圾回收
    // 这里可以添加一些内存优化建议
    // debugPrint('强制内存优化');
  }

  /// 发送内存警告
  void _sendMemoryWarning() {
    // 这里可以发送内存警告通知
    // debugPrint('内存使用过高，请关闭不必要的应用');
  }

  /// 获取内存使用历史
  List<MemoryInfo> getMemoryHistory() {
    return List.unmodifiable(_memoryHistory);
  }

  /// 获取当前内存状态
  MemoryInfo? getCurrentMemoryInfo() {
    if (_memoryHistory.isEmpty) return null;
    return _memoryHistory.last;
  }

  /// 获取内存使用趋势
  MemoryTrend getMemoryTrend() {
    if (_memoryHistory.length < 2) {
      return MemoryTrend.stable;
    }

    final recent = _memoryHistory.take(5).toList();
    final avgRecent =
        recent.map((m) => m.usedMB).reduce((a, b) => a + b) / recent.length;
    final avgOlder = _memoryHistory
            .take(_memoryHistory.length - 5)
            .map((m) => m.usedMB)
            .reduce((a, b) => a + b) /
        (_memoryHistory.length - 5);

    if (avgRecent > avgOlder * 1.1) {
      return MemoryTrend.increasing;
    } else if (avgRecent < avgOlder * 0.9) {
      return MemoryTrend.decreasing;
    } else {
      return MemoryTrend.stable;
    }
  }

  /// 释放资源
  void dispose() {
    stopMonitoring();
    _memoryController.close();
  }
}

/// 📊 内存信息
class MemoryInfo {
  final DateTime timestamp;
  final double totalMB;
  final double availableMB;
  final double usedMB;
  final String platform;

  MemoryInfo({
    required this.timestamp,
    required this.totalMB,
    required this.availableMB,
    required this.usedMB,
    required this.platform,
  });

  double get usagePercentage => (usedMB / totalMB) * 100;
  double get availablePercentage => (availableMB / totalMB) * 100;

  bool get isWarning => usedMB > MemoryOptimizationService._warningThreshold;
  bool get isCritical => usedMB > MemoryOptimizationService._criticalThreshold;
}

/// 📈 内存趋势
enum MemoryTrend {
  increasing, // 上升
  decreasing, // 下降
  stable, // 稳定
}
