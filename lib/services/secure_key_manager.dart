import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';

/// 🔐 安全密钥管理器
/// 使用密钥分片和动态生成技术，避免硬编码密钥
class SecureKeyManager {
  // 密钥分片存储 - 避免单点密钥泄露
  static const List<String> _keyFragments = [
    'L5E2024', // 片段1: 产品标识
    'GUARD789', // 片段2: 安全标识
    'SECURE42', // 片段3: 加密标识
    'VERIFY13', // 片段4: 验证标识
    'CHECK256', // 片段5: 校验标识
  ];

  // 时间种子配置
  static const int _timeSeedInterval = 24 * 60 * 60 * 1000; // 24小时
  static const String _saltString = 'LoadGuard_Enterprise_V2_2024';

  /// 获取主密钥（基于时间种子动态生成）
  static String getMasterKey() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final timeSeed = timestamp ~/ _timeSeedInterval; // 每24小时变化一次
    return _generateMasterKey(timeSeed);
  }

  /// 获取签名密钥（用于许可证签名验证）
  static String getSignatureKey() {
    final masterKey = getMasterKey();
    return _deriveKey(masterKey, 'SIGNATURE_KEY_2024');
  }

  /// 获取加密密钥（用于数据加密）
  static String getEncryptionKey() {
    final masterKey = getMasterKey();
    return _deriveKey(masterKey, 'ENCRYPTION_KEY_2024');
  }

  /// 获取HMAC密钥（用于消息认证）
  static String getHmacKey() {
    final masterKey = getMasterKey();
    return _deriveKey(masterKey, 'HMAC_KEY_2024');
  }

  /// 获取设备绑定密钥（基于设备指纹）
  static String getDeviceBindingKey(String deviceFingerprint) {
    final masterKey = getMasterKey();
    final combined = '$masterKey:$deviceFingerprint:DEVICE_BINDING';
    return _hashString(combined);
  }

  /// 生成许可证签名密钥（用于验证许可证真实性）
  static String getLicenseSigningKey() {
    final masterKey = getMasterKey();
    return _deriveKey(masterKey, 'LICENSE_SIGNING_2024');
  }

  /// 生成试用期保护密钥
  static String getTrialProtectionKey() {
    final masterKey = getMasterKey();
    return _deriveKey(masterKey, 'TRIAL_PROTECTION_2024');
  }

  /// 生成审计日志加密密钥
  static String getAuditLogKey() {
    final masterKey = getMasterKey();
    return _deriveKey(masterKey, 'AUDIT_LOG_2024');
  }

  /// 生成会话密钥（临时密钥，每次调用都不同）
  static String generateSessionKey() {
    final random = Random.secure();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomBytes = List<int>.generate(16, (_) => random.nextInt(256));
    final sessionData = '$timestamp:${randomBytes.join(',')}';
    return _hashString(sessionData);
  }

  /// 验证密钥是否有效（用于检测密钥完整性）
  static bool validateKeyIntegrity() {
    try {
      // 验证密钥分片完整性
      if (_keyFragments.length != 5) return false;

      // 验证生成的密钥长度
      final masterKey = getMasterKey();
      if (masterKey.length != 64) return false;

      // 验证派生密钥
      final signatureKey = getSignatureKey();
      final encryptionKey = getEncryptionKey();

      if (signatureKey.length != 64 || encryptionKey.length != 64) {
        return false;
      }

      // 验证密钥唯一性
      if (signatureKey == encryptionKey || signatureKey == masterKey) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取密钥状态信息（用于调试和监控）
  static Map<String, dynamic> getKeyStatus() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final currentSeed = timestamp ~/ _timeSeedInterval;
    final nextRotation = (currentSeed + 1) * _timeSeedInterval;
    final timeToRotation = nextRotation - timestamp;

    return {
      'fragments_count': _keyFragments.length,
      'current_seed': currentSeed,
      'key_rotation_in_ms': timeToRotation,
      'key_rotation_in_hours': (timeToRotation / (1000 * 60 * 60)).round(),
      'master_key_length': getMasterKey().length,
      'integrity_valid': validateKeyIntegrity(),
      'last_check': DateTime.now().toIso8601String(),
    };
  }

  // 私有方法实现

  /// 生成主密钥
  static String _generateMasterKey(int timeSeed) {
    // 组合所有密钥片段
    final combinedFragments = _keyFragments.join(':');

    // 添加时间种子和盐值
    final keyMaterial = '$combinedFragments:$timeSeed:$_saltString';

    // 生成SHA-256哈希
    return _hashString(keyMaterial);
  }

  /// 密钥派生函数（HKDF简化版本）
  static String _deriveKey(String masterKey, String context) {
    final combined = '$masterKey:$context:$_saltString';
    return _hashString(combined);
  }

  /// 哈希字符串（使用SHA-256）
  static String _hashString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// 组合密钥片段
  static String _combineFragments(int seed) {
    final random = Random(seed);
    final shuffled = List<String>.from(_keyFragments);

    // 基于种子打乱片段顺序
    for (int i = shuffled.length - 1; i > 0; i--) {
      final j = random.nextInt(i + 1);
      final temp = shuffled[i];
      shuffled[i] = shuffled[j];
      shuffled[j] = temp;
    }

    return shuffled.join('_');
  }

  /// 生成随机盐值
  static String generateRandomSalt([int length = 32]) {
    final random = Random.secure();
    final bytes = List<int>.generate(length, (_) => random.nextInt(256));
    return base64Encode(bytes);
  }

  /// 密钥强度验证
  static int calculateKeyStrength(String key) {
    if (key.length < 32) return 0; // 弱密钥
    if (key.length < 64) return 1; // 中等密钥
    return 2; // 强密钥
  }

  /// 检查是否需要密钥轮换
  static bool needsKeyRotation() {
    final status = getKeyStatus();
    final hoursToRotation = status['key_rotation_in_hours'] as int;
    return hoursToRotation <= 2; // 2小时内需要轮换时发出警告
  }

  /// 获取密钥指纹（用于密钥识别，不暴露实际密钥）
  static String getKeyFingerprint(String key) {
    final hash = _hashString(key);
    return hash.substring(0, 8).toUpperCase(); // 返回前8位作为指纹
  }

  /// 验证密钥格式
  static bool isValidKeyFormat(String key) {
    // 检查是否为有效的SHA-256哈希格式
    final hexPattern = RegExp(r'^[a-fA-F0-9]{64}$');
    return hexPattern.hasMatch(key);
  }
}
