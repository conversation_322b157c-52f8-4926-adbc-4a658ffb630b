import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/services/mlkit_network_manager.dart';
import 'package:loadguard/services/mlkit_stream_processor_simplified.dart';

/// 📱 **MLKit文本识别服务 - 纯Flutter实现**
/// 基于Google ML Kit Text Recognition v2的文本识别实现
/// 提供稳定可靠的OCR功能，支持真实进度回调
class MLKitTextRecognitionService {
  static final MLKitTextRecognitionService _instance =
      MLKitTextRecognitionService._internal();
  factory MLKitTextRecognitionService() => _instance;

  bool _isInitialized = false;

  // Google ML Kit Text Recognition v2 识别器
  TextRecognizer? _textRecognizer;

  // 🌐 新增：网络管理器
  final _networkManager = MLKitNetworkManager();

  // 📸 新增：简化版流式处理器
  final _streamProcessor = MLKitStreamProcessorSimplified();

  // 🔧 日志标签
  static const String _tag = 'MLKit';

  MLKitTextRecognitionService._internal();

  /// 初始化服务 - 官方标准ML Kit Text Recognition v2实现
  Future<void> initialize() async {
    final initStart = DateTime.now();
    AppLogger.info('[PERF] MLKitTextRecognitionService.initialize() start');
    if (_isInitialized) return;

    try {
      AppLogger.info('🚀 开始初始化 MLKit Text Recognition v2 服务...');

      // 🔧 按官方文档0.15.0标准初始化
      if (_textRecognizer == null) {
        // 🚀 使用官方推荐的初始化方式
        _textRecognizer = TextRecognizer(script: TextRecognitionScript.latin);
        AppLogger.info('✅ MLKit Text Recognition 0.15.0 初始化成功');
      }

      // 🌐 启动网络监听
      _networkManager.startNetworkMonitoring();

      // 📸 初始化流式处理器（如果需要）
      await _streamProcessor.initialize();

      _isInitialized = true;
      AppLogger.info('🏆 MLKit文本识别服务初始化完成 - 支持离线模型和流式处理');
    } catch (e) {
      AppLogger.error('❌ MLKit初始化失败: $e');
      rethrow;
    } finally {
      AppLogger.info(
          '[PERF] MLKitTextRecognitionService.initialize() end, duration: ${DateTime.now().difference(initStart).inMilliseconds}ms');
    }
  }

  /// 📸 获取简化版流式处理器（用于文件批量处理）
  MLKitStreamProcessorSimplified get streamProcessor => _streamProcessor;

  /// 🌐 获取网络管理器
  MLKitNetworkManager get networkManager => _networkManager;

  /// 🔍 检查是否支持多语言识别
  bool get supportsMultiLanguage => _networkManager.isOnline;

  /// 🔒 强制使用离线模式
  Future<void> forceOfflineMode() async {
    _networkManager.forceOfflineMode();

    // 重新初始化识别器为离线模式（按官方文档）
    _textRecognizer?.close();
    _textRecognizer = TextRecognizer(script: TextRecognitionScript.latin);

    AppLogger.info('🔒 已切换到强制离线模式');
  }

  /// 🎯 处理图像并返回识别结果 - 官方ML Kit Text Recognition v2标准流程
  /// 分阶段进度：验证(0-30%) → 识别(30-80%) → 结果处理(80-100%)
  Future<List<RecognitionResult>> processImage(
    String imagePath, {
    Function(double progress, String status)? onProgress,
    String? presetProductCode,
    String? presetBatchNumber,
  }) async {
    final procStart = DateTime.now();
    AppLogger.info('[PERF] processImage() start: $imagePath');
    try {
      // Step 1: 初始化检查
      if (!_isInitialized) {
        onProgress?.call(0.05, '正在初始化MLKit服务...');
        await initialize().timeout(const Duration(seconds: 8), onTimeout: () {
          throw TimeoutException('MLKit初始化超时', const Duration(seconds: 8));
        });
      }

      AppLogger.info('🚀 开始使用MLKit Text Recognition v2处理图像: $imagePath');

      // Step 2: 验证文件存在性和有效性
      final file = File(imagePath);
      if (!await file.exists()) {
        throw Exception('图像文件不存在: $imagePath');
      }

      final fileSize = await file.length();
      if (fileSize == 0) {
        throw Exception('图像文件为空');
      }

      if (fileSize > 50 * 1024 * 1024) {
        // 50MB限制
        throw Exception(
            '图像文件过大: ${(fileSize / 1024 / 1024).toStringAsFixed(1)}MB');
      }

      AppLogger.debug('✅ 图像文件验证通过: ${(fileSize / 1024).toStringAsFixed(1)}KB');

      onProgress?.call(0.2, '正在准备识别引擎...');
      
      // 详细进度更新
      onProgress?.call(0.25, '正在验证图像格式...');
      
      // 检查图像格式和质量
      final fileName = imagePath.toLowerCase();
      if (!fileName.endsWith('.jpg') && !fileName.endsWith('.jpeg') && 
          !fileName.endsWith('.png') && !fileName.endsWith('.bmp')) {
        throw Exception('不支持的图像格式，请使用JPG、PNG或BMP格式');
      }
      
      onProgress?.call(0.3, '图像验证完成，准备识别...');

      // Step 3: 确保识别器可用
      if (_textRecognizer == null) {
        throw Exception('文本识别器未初始化');
      }

      // Step 4: 创建输入图像
      final inputImage = InputImage.fromFilePath(imagePath);
      AppLogger.debug('✅ 输入图像创建成功');

      // Step 5: 执行文本识别 - 优化超时时间提升用户体验
      onProgress?.call(0.4, '正在执行文本识别...');
      
      final recognizedText = await _textRecognizer!
          .processImage(inputImage)
          .timeout(const Duration(seconds: 15), onTimeout: () {
        throw TimeoutException('MLKit文本识别超时', const Duration(seconds: 15));
      });

      // Step 6: 处理识别结果 - 按官方VisionText结构解析
      onProgress?.call(0.7, '识别完成，正在处理结果...');
      AppLogger.info('✅ MLKit识别完成，检测到${recognizedText.blocks.length}个文本块');

      // 收集所有文本块用于统一匹配
      final List<String> allTextBlocks = [];
      if (recognizedText.text.isNotEmpty) {
        allTextBlocks.add(recognizedText.text);
      }
      for (final block in recognizedText.blocks) {
        if (block.text.isNotEmpty && block.text != recognizedText.text) {
          allTextBlocks.add(block.text);
        }
      }

      // 统一进行智能匹配
      onProgress?.call(0.8, '正在匹配预设信息...');
      final bool overallMatch = _checkPresetMatchBlocks(
          allTextBlocks, presetProductCode, presetBatchNumber);

      // 转换为我们的RecognitionResult格式
      final List<RecognitionResult> results = [];

      // 添加整体文本作为主要结果
      if (recognizedText.text.isNotEmpty) {
        // 🔧 新增：清理识别文本
        final cleanedText = _cleanRecognitionText(recognizedText.text);

        results.add(RecognitionResult(
          ocrText: cleanedText,
          isQrOcrConsistent: true,
          matchesPreset: overallMatch,
          recognitionTime: DateTime.now(),
          status: overallMatch
              ? RecognitionStatus.completed
              : RecognitionStatus.failed,
        ));
      }

      // 添加每个文本块的结果
      for (final block in recognizedText.blocks) {
        if (block.text.isNotEmpty && block.text != recognizedText.text) {
          // 🔧 新增：清理识别文本
          final cleanedBlockText = _cleanRecognitionText(block.text);

          results.add(RecognitionResult(
            ocrText: cleanedBlockText,
            isQrOcrConsistent: true,
            matchesPreset: _checkPresetMatch(
                cleanedBlockText, presetProductCode, presetBatchNumber),
            recognitionTime: DateTime.now(),
            status: _checkPresetMatch(
                    cleanedBlockText, presetProductCode, presetBatchNumber)
                ? RecognitionStatus.completed
                : RecognitionStatus.failed,
          ));
        }
      }

      onProgress?.call(0.95, '识别结果处理完成');
      AppLogger.info(
          '📊 识别结果汇总: 主文本="${recognizedText.text}" | 文本块数量=${recognizedText.blocks.length}');
      
      onProgress?.call(1.0, '识别完成');
      return results;
    } on TimeoutException catch (e) {
      AppLogger.error('❌ MLKit识别超时: $e');
      onProgress?.call(0.0, '识别超时(${e.duration?.inSeconds ?? 15}秒)，请重试');
      // 🔧 优化：提供更用户友好的超时信息
      final timeoutSeconds = e.duration?.inSeconds ?? 15;
      throw TimeoutException('文本识别超时($timeoutSeconds秒)，请检查图片质量或网络连接', e.duration);
    } on FileSystemException catch (e) {
      AppLogger.error('❌ 文件系统错误: ${e.message} - ${e.path}');
      onProgress?.call(0.0, '文件访问失败');
      rethrow;
    } on Exception catch (e, stackTrace) {
      AppLogger.error('❌ MLKit文本识别异常: $e');
      onProgress?.call(0.0, '识别失败: $e');
      if (e.toString().contains('PERMISSION_DENIED')) {
        throw Exception('权限不足，请检查相机权限');
      } else if (e.toString().contains('NETWORK_ERROR')) {
        throw Exception('网络错误，请检查连接');
      } else if (e.toString().contains('INSUFFICIENT_SPACE')) {
        throw Exception('存储空间不足');
      } else {
        throw Exception('文本识别失败: $e');
      }
    } catch (e, stackTrace) {
      AppLogger.error('❌ MLKit未知错误: $e');
      onProgress?.call(0.0, '未知错误');
      throw Exception('文本识别遇到未知错误: $e');
    } finally {
      AppLogger.info(
          '[PERF] processImage() end, duration: ${DateTime.now().difference(procStart).inMilliseconds}ms');
    }
  }

  /// 🔧 修复：清理识别文本 - 过滤乱码和无用信息
  String _cleanRecognitionText(String text) {
    if (text.isEmpty) return text;

    String cleaned = text;

    // 移除明显的乱码字符
    cleaned = cleaned.replaceAll(RegExp(r'[^\w\s\-:/.()]'), '');

    // 移除过短的无意义行
    final lines = cleaned.split('\n');
    final validLines = lines.where((line) {
      final trimmed = line.trim();
      return trimmed.length >= 3 &&
          !RegExp(r'^[^\w]*$').hasMatch(trimmed) && // 不是纯符号
          !RegExp(r'^\d{1,2}$').hasMatch(trimmed); // 不是单独的1-2位数字
    }).toList();

    cleaned = validLines.join('\n');

    return cleaned;
  }

  /// 检查是否匹配预设模式 - 智能匹配核心信息
  bool _checkPresetMatch(
      String text, String? presetProductCode, String? presetBatchNumber) {
    if (text.isEmpty) return false;
    // 1. 清理识别文本，移除多余空格和换行
    final cleanText = text.replaceAll(RegExp(r'\s+'), ' ').trim().toUpperCase();
    // 2. 字符标准化处理（防止O/0、I/1误识别）
    final normalizedText = _normalizeCharacters(cleanText);
    final normalizedProductCode =
        _normalizeCharacters((presetProductCode ?? '').toUpperCase());
    final normalizedBatchNumber =
        _normalizeCharacters((presetBatchNumber ?? '').toUpperCase());
    // 3. 检查产品代码匹配
    bool productCodeMatch = false;
    if (normalizedProductCode.isNotEmpty) {
      // 更宽松：允许前后有其他字符
      productCodeMatch = normalizedText.contains(normalizedProductCode);
    }
    // 4. 检查批号匹配 - 只比对主串，允许后缀
    bool batchNumberMatch = false;
    if (normalizedBatchNumber.isNotEmpty) {
      // 提取批号主串
      final coreBatchNumberPreset =
          _extractCoreBatchNumber(normalizedBatchNumber);
      // 允许主串后有-4B等后缀
      final batchNumberPattern = RegExp(
          RegExp.escape(coreBatchNumberPreset) + r'([\-][A-Z0-9]+)?',
          caseSensitive: false);
      if (batchNumberPattern.hasMatch(normalizedText)) {
        batchNumberMatch = true;
      }
    }
    // 5. 最终匹配结果 - 必须同时匹配牌号和批号
    final finalMatch = productCodeMatch && batchNumberMatch;
    return finalMatch;
  }

  /// 字符标准化处理 - 智能处理字符混淆
  String _normalizeCharacters(String text) {
    if (text.isEmpty) return text;

    String normalized = text.toUpperCase().trim();

    // 🔧 修复：采用更智能的字符标准化策略
    // 1. 先处理明显的数字位置（批号的前6位和后5位）
    final batchPattern = RegExp(r'(\d{2,6})([A-Z01OI])(\d{4,5})');
    normalized = normalized.replaceAllMapped(batchPattern, (match) {
      final datepart =
          match.group(1)!.replaceAll('O', '0').replaceAll('I', '1');
      final letterpart =
          match.group(2)!.replaceAll('0', 'O').replaceAll('1', 'I');
      final numberpart =
          match.group(3)!.replaceAll('O', '0').replaceAll('I', '1');

      return '$datepart$letterpart$numberpart';
    });

    // 2. 处理产品牌号中的字符混淆（保守处理，只处理明确的混淆）
    // 在连字符前后的上下文中，通常是产品代码，谨慎处理

    // 3. 清理多余的空格和特殊字符
    normalized = normalized.replaceAll(RegExp(r'\s+'), '');

    return normalized;
  }

  // 批号主串提取：智能提取核心批号，支持后缀
  String _extractCoreBatchNumber(String batchNumber) {
    if (batchNumber.isEmpty) return batchNumber;

    // 🔧 修复：支持多种批号格式和后缀模式
    // 格式1: YYMMDD + 字母 + 5位数字 (标准12位)
    final standardPattern = RegExp(r'(\d{6}[A-Z]\d{5})');
    final standardMatch = standardPattern.firstMatch(batchNumber);
    if (standardMatch != null) {
      return standardMatch.group(1)!;
    }

    // 格式2: 尝试提取可能的批号格式（允许O/0误识别）
    final flexPattern = RegExp(r'(\d{6}[A-Z0-9]\d{5})');
    final flexMatch = flexPattern.firstMatch(batchNumber);
    if (flexMatch != null) {
      String candidate = flexMatch.group(1)!;
      // 修正第7位字符（应该是字母）
      if (candidate.length == 12 && RegExp(r'\d').hasMatch(candidate[6])) {
        final correctedChar = _digitToLetter(candidate[6]);
        candidate =
            candidate.substring(0, 6) + correctedChar + candidate.substring(7);
      }
      return candidate;
    }

    // 格式3: 如果长度大于12位，可能包含后缀，取前12位
    if (batchNumber.length >= 12) {
      String candidate = batchNumber.substring(0, 12);
      // 验证前12位是否符合批号格式
      if (RegExp(r'\d{6}[A-Z0-9]\d{5}').hasMatch(candidate)) {
        // 修正第7位
        if (RegExp(r'\d').hasMatch(candidate[6])) {
          final correctedChar = _digitToLetter(candidate[6]);
          candidate = candidate.substring(0, 6) +
              correctedChar +
              candidate.substring(7);
        }
        return candidate;
      }
    }

    return batchNumber;
  }

  /// 🔧 新增：数字转字母（批号第7位修正）
  String _digitToLetter(String digit) {
    const digitToLetterMap = {
      '0': 'O',
      '1': 'I',
      '5': 'S',
      '6': 'G',
      '8': 'B',
      '2': 'Z'
    };
    return digitToLetterMap[digit] ?? digit;
  }

  /// 提取主要边界框
  Map<String, double>? _extractMainBoundingBox(RecognizedText recognizedText) {
    if (recognizedText.blocks.isEmpty) return null;

    double left = double.infinity;
    double top = double.infinity;
    double right = 0;
    double bottom = 0;

    for (final block in recognizedText.blocks) {
      final box = block.boundingBox;
      if (box.left < left) left = box.left.toDouble();
      if (box.top < top) top = box.top.toDouble();
      if (box.right > right) right = box.right.toDouble();
      if (box.bottom > bottom) bottom = box.bottom.toDouble();
    }

    return {
      'left': left,
      'top': top,
      'right': right,
      'bottom': bottom,
    };
  }

  /// 释放资源 - 官方标准方式
  Future<void> dispose() async {
    final disposeStart = DateTime.now();
    AppLogger.info('[PERF] MLKitTextRecognitionService.dispose() start');
    try {
      if (_textRecognizer != null) {
        // 官方标准：调用close()方法释放资源
        _textRecognizer!.close();
        _textRecognizer = null;
        AppLogger.info('✅ MLKit文本识别器资源已释放');
      }
      _isInitialized = false;
    } catch (e) {
      AppLogger.error('释放MLKit资源时出错: $e');
    } finally {
      AppLogger.info(
          '[PERF] MLKitTextRecognitionService.dispose() end, duration: ${DateTime.now().difference(disposeStart).inMilliseconds}ms');
    }
  }

  /// 🔧 新增：智能相机专用文本识别方法 - 优化超时和进度反馈
  Future<RecognitionResult> recognizeText(String imagePath,
      {String? presetProductCode, String? presetBatchNumber}) async {
    final recogStart = DateTime.now();
    AppLogger.info('[PERF] recognizeText() start: $imagePath');
    try {
      // 🔧 优化：添加默认进度回调
      int progressStep = 0;
      void defaultProgress(double progress, String status) {
        progressStep++;
        AppLogger.debug('识别进度 ${(progress * 100).toStringAsFixed(0)}%: $status');
      }

      // 使用现有的processImage方法，带超时优化
      final results = await processImage(
        imagePath,
        onProgress: defaultProgress,
        presetProductCode: presetProductCode,
        presetBatchNumber: presetBatchNumber,
      ).timeout(const Duration(seconds: 20), onTimeout: () {
        // 🔧 整体方法的保护性超时，比内部超时稍长
        throw TimeoutException('文本识别整体超时', const Duration(seconds: 20));
      });

      // 返回第一个结果，如果没有结果则返回默认的失败结果
      if (results.isNotEmpty) {
        final firstResult = results.first;
        return RecognitionResult(
          qrCode: firstResult.qrCode,
          ocrText: firstResult.ocrText,
          extractedProductCode: firstResult.extractedProductCode,
          extractedBatchNumber: firstResult.extractedBatchNumber,
          isQrOcrConsistent: firstResult.isQrOcrConsistent,
          matchesPreset: firstResult.matchesPreset,
          confidence: firstResult.confidence,
          boundingBox: firstResult.boundingBox,
          processingTime: firstResult.processingTime,
          extractedQrData: firstResult.extractedQrData,
          batchMatches: firstResult.batchMatches,
          status: firstResult.status,
          errorMessage: firstResult.errorMessage,
          metadata: firstResult.metadata,
        );
      } else {
        return RecognitionResult(
          ocrText: '',
          isQrOcrConsistent: false,
          matchesPreset: false,
          confidence: 0.0,
          status: RecognitionStatus.failed,
          errorMessage: '未检测到文本',
        );
      }
    } catch (e) {
      AppLogger.error('识别文本失败: $e');
      return RecognitionResult(
        ocrText: '',
        isQrOcrConsistent: false,
        matchesPreset: false,
        confidence: 0.0,
        status: RecognitionStatus.failed,
        errorMessage: '识别失败: $e',
      );
    } finally {
      AppLogger.info(
          '[PERF] recognizeText() end, duration: ${DateTime.now().difference(recogStart).inMilliseconds}ms');
    }
  }

  /// 获取服务状态信息 - 按官方文档0.15.0标准
  Map<String, dynamic> getServiceInfo() {
    return {
      'isInitialized': _isInitialized,
      'recognizerAvailable': _textRecognizer != null,
      'implementation': 'ML Kit Text Recognition 0.15.0 (官方标准)',
      'apiVersion': '0.15.0',
      'supportedLanguages': [
        'Latin',
        'Chinese',
        'Devanagari',
        'Japanese',
        'Korean'
      ],
      'primaryScript': 'Latin',
      'localProcessing': true,
      'requiresNetwork': false,
      'offlineCapable': true,
      'bundledModel': true,
      'minAndroidSdk': 21,
      'officialCompliant': true,
      // 🔧 新增：超时配置信息
      'timeoutConfig': {
        'initializationTimeout': '8秒',
        'recognitionTimeout': '15秒',
        'smartMatchingTimeout': '5秒',
        'overallTimeout': '20秒',
        'optimizedForUserExperience': true,
      },
    };
  }

  /// 🔧 新增：智能后处理核心方法
  PostProcessedResult performIntelligentPostProcessing({
    required String ocrText,
    required PostProcessingConfig config,
  }) {
    AppLogger.info(' 开始智能后处理...');
    AppLogger.info(' 原始OCR文本: "$ocrText"');

    String? correctedProductCode;
    String? correctedBatchNumber;
    bool productCodeCorrected = false;
    bool batchNumberCorrected = false;
    Map<String, dynamic> correctionDetails = {};

    // 1. 提取牌号和批号
    final extracted = _extractProductCodeAndBatchNumber(ocrText);
    final recognizedProductCode = extracted['productCode'];
    final recognizedBatchNumber = extracted['batchNumber'];

    AppLogger.info(
        ' 提取结果 - 牌号: "$recognizedProductCode", 批号: "$recognizedBatchNumber"');

    // 2. 牌号智能纠错
    if (recognizedProductCode != null && config.enableFuzzyMatch) {
      final corrected = _correctProductCode(recognizedProductCode, config);
      if (corrected != null) {
        correctedProductCode = corrected['correctedCode'];
        productCodeCorrected = corrected['wasCorrected'];
        correctionDetails['productCodeCorrection'] = corrected['details'];

        AppLogger.info(
            ' 牌号纠错: "$recognizedProductCode" -> "$correctedProductCode"');
      } else {
        correctedProductCode = recognizedProductCode;
        AppLogger.info(' 牌号无需纠错: "$recognizedProductCode"');
      }
    } else {
      correctedProductCode = recognizedProductCode;
    }

    // 3. 批号格式验证与纠错
    if (recognizedBatchNumber != null && config.enableBatchFormatValidation) {
      final corrected =
          _correctBatchNumber(recognizedBatchNumber, config.validBatchLetters);
      if (corrected != null) {
        correctedBatchNumber = corrected['correctedBatch'];
        batchNumberCorrected = corrected['wasCorrected'];
        correctionDetails['batchNumberCorrection'] = corrected['details'];

        AppLogger.info(
            ' 批号纠错: "$recognizedBatchNumber" -> "$correctedBatchNumber"');
      } else {
        correctedBatchNumber = recognizedBatchNumber;
        AppLogger.info(' 批号无需纠错: "$recognizedBatchNumber"');
      }
    } else {
      correctedBatchNumber = recognizedBatchNumber;
    }

    final result = PostProcessedResult(
      originalText: ocrText,
      correctedProductCode: correctedProductCode,
      correctedBatchNumber: correctedBatchNumber,
      productCodeCorrected: productCodeCorrected,
      batchNumberCorrected: batchNumberCorrected,
      correctionDetails: correctionDetails,
    );

    AppLogger.info(' 智能后处理完成');
    AppLogger.info(
        ' 最终结果 - 牌号: "$correctedProductCode", 批号: "$correctedBatchNumber"');

    return result;
  }

  /// 🔧 新增：提取牌号和批号 - 改进识别逻辑
  Map<String, String?> _extractProductCodeAndBatchNumber(String text) {
    final lines = text.split('\n');
    String? productCode;
    String? batchNumber;

    for (final line in lines) {
      final cleanLine = line.trim();
      if (cleanLine.isEmpty) continue;

      // 🔧 修复：严格的批号格式检查 YYMMDD + 字母 + 5位数字
      final batchPattern = RegExp(r'\b(\d{6}[A-Z]\d{5})\b');
      final batchMatch = batchPattern.firstMatch(cleanLine);
      if (batchMatch != null) {
        final candidate = batchMatch.group(1)!;
        // 验证日期部分是否合理
        final year = int.parse(candidate.substring(0, 2));
        final month = int.parse(candidate.substring(2, 4));
        final day = int.parse(candidate.substring(4, 6));

        if (year >= 20 &&
            year <= 30 &&
            month >= 1 &&
            month <= 12 &&
            day >= 1 &&
            day <= 31) {
          batchNumber = candidate;
        }
      }

      // 🔧 修复：牌号格式检查 - 通常是字母+连字符+数字
      final productPattern = RegExp(r'\b([A-Z]{2,4}-\d{4,6})\b');
      final productMatch = productPattern.firstMatch(cleanLine);
      if (productMatch != null) {
        productCode = productMatch.group(1)!;
      }
    }

    return {
      'productCode': productCode,
      'batchNumber': batchNumber,
    };
  }

  /// 🔧 新增：牌号智能纠错
  Map<String, dynamic>? _correctProductCode(
      String recognizedCode, PostProcessingConfig config) {
    AppLogger.info(' 开始牌号纠错: "$recognizedCode"');

    // 查找最相似的预设牌号
    final bestMatch = ProductCodeMatcher.findBestMatch(
        recognizedCode, config.presetProductCodes, config.maxProductCodeDiff);

    if (bestMatch != null) {
      final distance =
          ProductCodeMatcher.levenshteinDistance(recognizedCode, bestMatch);
      final wasCorrected = distance > 0;

      AppLogger.info(' 找到相似牌号: "$bestMatch" (差异: $distance)');

      return {
        'correctedCode': bestMatch,
        'wasCorrected': wasCorrected,
        'details': {
          'originalCode': recognizedCode,
          'correctedCode': bestMatch,
          'editDistance': distance,
          'maxAllowedDiff': config.maxProductCodeDiff,
        },
      };
    }

    AppLogger.info(' 未找到相似牌号，保持原值');
    return null;
  }

  /// 🔧 新增：批号智能纠错
  Map<String, dynamic>? _correctBatchNumber(
      String recognizedBatch, List<String> validLetters) {
    AppLogger.info(' 开始批号纠错: "$recognizedBatch"');

    // 检查长度
    if (recognizedBatch.length != 12) {
      AppLogger.info(' 批号长度不正确: ${recognizedBatch.length}');
      return null;
    }

    // 尝试纠错
    final corrected =
        BatchNumberValidator.correctBatchNumber(recognizedBatch, validLetters);

    if (corrected != null) {
      AppLogger.info(' 批号纠错成功: "$recognizedBatch" -> "$corrected"');

      return {
        'correctedBatch': corrected,
        'wasCorrected': true,
        'details': {
          'originalBatch': recognizedBatch,
          'correctedBatch': corrected,
          'format': BatchNumberValidator.parseBatchNumber(corrected),
        },
      };
    }

    AppLogger.info(' 批号无需纠错: "$recognizedBatch"');
    return null;
  }

  /// 🔧 新增：智能后处理辅助方法
  PostProcessedResult _performSmartPostProcessing(
      String ocrText, String? presetProductCode, String? presetBatchNumber) {
    // 获取预设牌号库（这里需要从数据库或配置中获取）
    final presetProductCodes = _getPresetProductCodes();

    final config = PostProcessingConfig(
      presetProductCodes: presetProductCodes,
      maxProductCodeDiff: 2,
      enableFuzzyMatch: true,
      enableBatchFormatValidation: true,
    );

    return performIntelligentPostProcessing(
      ocrText: ocrText,
      config: config,
    );
  }

  /// 🔧 新增：获取预设牌号库
  List<String> _getPresetProductCodes() {
    // 从产品数据库中获取所有预设牌号
    // 暂时返回空列表，避免编译错误
    return <String>[];
  }

  /// 🔧 新增：多批次匹配处理
  /// 当单批次匹配失败时，尝试匹配任务中的所有批次
  RecognitionResult? tryMatchMultipleBatches(
      String ocrText, List<BatchInfo> batches) {
    if (batches.isEmpty) return null;

    AppLogger.info('尝试多批次匹配: ${batches.length}个批次');

    for (final batch in batches) {
      AppLogger.info('尝试匹配批次: ${batch.productCode} ${batch.batchNumber}');

      // 使用现有的匹配逻辑
      final textBlocks = ocrText
          .split('\n')
          .where((block) => block.trim().isNotEmpty)
          .toList();
      final isMatched = _checkPresetMatchBlocks(
          textBlocks, batch.productCode, batch.batchNumber);

      if (isMatched) {
        AppLogger.info('✅ 批次匹配成功: ${batch.productCode} ${batch.batchNumber}');

        return RecognitionResult(
          ocrText: ocrText,
          extractedProductCode: batch.productCode,
          extractedBatchNumber: batch.batchNumber,
          matchesPreset: true,
          confidence: 0.9, // 高置信度，因为通过了严格的匹配逻辑
          isQrOcrConsistent: true,
          status: RecognitionStatus.completed,
          metadata: {
            'matchedBatchId': batch.id,
            'matchType': 'multipleBatch',
            'totalBatches': batches.length,
          },
        );
      }
    }

    AppLogger.warning('❌ 所有批次匹配失败');
    return null;
  }

  /// 🔧 新增：智能识别处理（支持多批次）- 优化超时机制
  Future<RecognitionResult> processImageWithSmartMatching({
    required String imagePath,
    required RecognitionMatchConfig matchConfig,
    Map<String, dynamic>? metadata,
    Function(double progress, String status)? onProgress,
  }) async {
    try {
      // 初始化检查
      if (!_isInitialized) {
        onProgress?.call(0.0, '正在初始化MLKit服务...');
        await initialize().timeout(const Duration(seconds: 8));
      }

      // 执行OCR识别 - 使用优化的超时时间
      onProgress?.call(0.1, '开始文本识别...');
      final ocrResults = await processImage(
        imagePath,
        onProgress: (progress, status) {
          // 映射进度到0.1-0.7区间
          onProgress?.call(0.1 + progress * 0.6, status);
        },
      ).timeout(const Duration(seconds: 18), onTimeout: () {
        throw TimeoutException('OCR识别超时', const Duration(seconds: 18));
      });

      // 合并所有OCR结果
      onProgress?.call(0.75, '正在合并识别结果...');
      final fullOcrText = ocrResults.map((r) => r.ocrText ?? '').join(' \n ');

      // 执行智能匹配
      onProgress?.call(0.8, '正在执行智能匹配...');
      final matchResult = await _performSmartMatching(
        ocrText: fullOcrText,
        matchConfig: matchConfig,
      ).timeout(const Duration(seconds: 5), onTimeout: () {
        throw TimeoutException('智能匹配超时', const Duration(seconds: 5));
      });

      onProgress?.call(0.95, '正在生成识别结果...');

      // 🔧 优化：使用最佳匹配的批次信息
      String? extractedProductCode;
      String? extractedBatchNumber;

      if (matchResult.isMatched &&
          matchResult.matchDetails?['bestMatchId'] != null) {
        final bestMatchId = matchResult.matchDetails!['bestMatchId'] as String;
        final bestBatch =
            matchConfig.batches.firstWhere((b) => b.id == bestMatchId);
        extractedProductCode = bestBatch.productCode;
        extractedBatchNumber = bestBatch.batchNumber;
      }

      onProgress?.call(1.0, '识别完成');
      return RecognitionResult(
        ocrText: fullOcrText,
        isQrOcrConsistent: true,
        matchesPreset: matchResult.isMatched,
        confidence: matchResult.overallConfidence,
        extractedProductCode: extractedProductCode,
        extractedBatchNumber: extractedBatchNumber,
        batchMatches: matchResult.batchMatches,
        status: matchResult.isMatched
            ? RecognitionStatus.completed
            : RecognitionStatus.failed,
        metadata: {
          ...?metadata,
          'smartMatch': matchResult.matchDetails,
        },
      );
    } on TimeoutException catch (e) {
      onProgress?.call(0.0, '识别超时，请重试');
      AppLogger.error('智能识别超时: $e');
      rethrow;
    } catch (e) {
      onProgress?.call(0.0, '识别失败');
      AppLogger.error('智能识别失败: $e');
      rethrow;
    }
  }

  /// 智能比对：所有文本块中有一个匹配产品代码，另一个匹配批号主串即可通过
  bool _checkPresetMatchBlocks(List<String> textBlocks,
      String? presetProductCode, String? presetBatchNumber) {
    if (presetProductCode == null || presetBatchNumber == null) return false;

    final normalizedProductCode = _normalizeCharacters(presetProductCode);
    final normalizedBatchNumber = _normalizeCharacters(presetBatchNumber);
    final coreBatchNumber = _extractCoreBatchNumber(normalizedBatchNumber);
    final batchFormat = RegExp(r'^[0-9]{6}[A-Z][0-9]{5}$');

    bool productCodeMatched = false;
    bool batchNumberMatched = false;

    for (int i = 0; i < textBlocks.length; i++) {
      final block = textBlocks[i];
      final normalizedBlock = _normalizeCharacters(block);

      // 产品代码匹配
      if (normalizedBlock.contains(normalizedProductCode)) {
        productCodeMatched = true;
      }

      // 批号主串匹配
      final blockCoreBatch = _extractCoreBatchNumber(normalizedBlock);
      if (blockCoreBatch == coreBatchNumber &&
          batchFormat.hasMatch(blockCoreBatch)) {
        batchNumberMatched = true;
      }
    }

    return productCodeMatched && batchNumberMatched;
  }

  /// 🔧 新增：智能匹配算法（支持混合任务多种匹配策略）
  Future<SmartMatchResult> _performSmartMatching({
    required String ocrText,
    required RecognitionMatchConfig matchConfig,
  }) async {
    final batchMatches = <BatchMatchResult>[];
    double overallConfidence = 0.0;
    int matchCount = 0;

    // 🔧 修复：采用多重匹配策略，提高识别成功率
    // 策略1: 逐行匹配（严格）
    // 策略2: 全文匹配（宽松）
    // 策略3: 分块匹配（中等）

    for (final batch in matchConfig.batches) {
      AppLogger.info(
          '【批次匹配】开始处理批次: 产品牌号=${batch.productCode}, 批号=${batch.batchNumber}');
      final normalizedProductCode = _normalizeCharacters(batch.productCode);
      final normalizedBatchNumber = _normalizeCharacters(batch.batchNumber);
      final coreBatchNumber = _extractCoreBatchNumber(normalizedBatchNumber);
      AppLogger.info(
          '【批次匹配】归一化后: 产品牌号=$normalizedProductCode, 批号=$normalizedBatchNumber, 主串=$coreBatchNumber');

      bool isMatched = false;
      String? matchedText;
      double confidence = 0.0;
      String matchStrategy = '';

      // 策略1: 逐行匹配 - 最严格，置信度最高
      final lines = ocrText
          .split(RegExp(r'[\n\r]+'))
          .map((l) => l.trim())
          .where((l) => l.isNotEmpty)
          .toList();
      for (final line in lines) {
        final normalizedLine = _normalizeCharacters(line);
        AppLogger.info('【批次匹配】逐行匹配: 原始行="$line"，归一化="$normalizedLine"');
        final hasProduct = normalizedLine.contains(normalizedProductCode);
        final hasBatch = normalizedLine.contains(coreBatchNumber);
        AppLogger.info(
            '【批次匹配】逐行匹配: hasProduct=$hasProduct, hasBatch=$hasBatch');

        if (hasProduct && hasBatch) {
          AppLogger.info('【批次匹配】逐行匹配成功: $line');
          isMatched = true;
          matchedText = line;
          confidence = 0.95;
          matchStrategy = 'line_match';
          break;
        }
      }

      // 策略2: 如果逐行匹配失败，尝试全文匹配 - 宽松，中等置信度
      if (!isMatched) {
        final normalizedFullText = _normalizeCharacters(ocrText);
        final hasProduct = normalizedFullText.contains(normalizedProductCode);
        final hasBatch = normalizedFullText.contains(coreBatchNumber);
        AppLogger.info(
            '【批次匹配】全文匹配: hasProduct=$hasProduct, hasBatch=$hasBatch, 全文="$normalizedFullText"');

        if (hasProduct && hasBatch) {
          isMatched = true;
          matchedText = ocrText.substring(
              0, math.min(100, ocrText.length)); // 截取前100字符作为匹配文本
          confidence = 0.85;
          matchStrategy = 'full_text_match';
        }
      }

      // 策略3: 如果还是失败，尝试模糊匹配 - 最宽松，低置信度
      if (!isMatched) {
        // 尝试更宽松的批号匹配（允许后缀变化）
        for (final line in lines) {
          final normalizedLine = _normalizeCharacters(line);
          final hasProduct = normalizedLine.contains(normalizedProductCode);

          // 寻找任何符合批号格式的字符串
          final batchPattern = RegExp(r'\d{6}[A-Z]\d{5}');
          final batchMatches = batchPattern.allMatches(normalizedLine);

          for (final match in batchMatches) {
            final foundBatch = match.group(0)!;
            final foundCore = _extractCoreBatchNumber(foundBatch);
            AppLogger.info(
                '【批次匹配】模糊匹配: 行="$line"，foundBatch=$foundBatch, foundCore=$foundCore, coreBatchNumber=$coreBatchNumber');

            if (hasProduct && foundCore == coreBatchNumber) {
              isMatched = true;
              matchedText = line;
              confidence = 0.75;
              matchStrategy = 'fuzzy_match';
              break;
            }
          }
          if (isMatched) break;
        }
      }

      AppLogger.info(
          '【批次匹配】最终结果: isMatched=$isMatched, matchStrategy=$matchStrategy, matchedText=$matchedText, confidence=$confidence');
      // 记录匹配结果
      batchMatches.add(BatchMatchResult(
        batchId: batch.id,
        productCode: batch.productCode,
        batchNumber: batch.batchNumber,
        isMatched: isMatched,
        confidence: confidence,
        matchedText: matchedText,
        details: {
          'matchedText': matchedText,
          'matchStrategy': matchStrategy,
          'normalizedProductCode': normalizedProductCode,
          'coreBatchNumber': coreBatchNumber,
          'originalBatchNumber': batch.batchNumber,
        },
      ));

      if (isMatched) {
        matchCount++;
        overallConfidence += confidence;
      }
    }

    if (matchCount > 0) {
      overallConfidence = overallConfidence / matchCount;
    }

    final isOverallMatched = matchCount > 0;

    // 🔧 优化：选择最佳匹配的批次（优先级：置信度 > 计划数量 > 匹配策略）
    BatchMatchResult? bestMatch;
    if (isOverallMatched) {
      final matchedBatches = batchMatches.where((b) => b.isMatched).toList();
      if (matchedBatches.length == 1) {
        bestMatch = matchedBatches.first;
      } else {
        // 多匹配时，按优先级选择
        matchedBatches.sort((a, b) {
          // 1. 置信度优先
          if ((a.confidence - b.confidence).abs() > 0.1) {
            return b.confidence.compareTo(a.confidence);
          }
          // 2. 计划数量优先（数量大的更重要）
          final batchA =
              matchConfig.batches.firstWhere((batch) => batch.id == a.batchId);
          final batchB =
              matchConfig.batches.firstWhere((batch) => batch.id == b.batchId);
          if (batchA.plannedQuantity != batchB.plannedQuantity) {
            return batchB.plannedQuantity.compareTo(batchA.plannedQuantity);
          }
          // 3. 匹配策略优先级：line_match > full_text_match > fuzzy_match
          final strategyPriority = {
            'line_match': 3,
            'full_text_match': 2,
            'fuzzy_match': 1
          };
          final priorityA =
              strategyPriority[a.details?['matchStrategy'] ?? ''] ?? 0;
          final priorityB =
              strategyPriority[b.details?['matchStrategy'] ?? ''] ?? 0;
          return priorityB.compareTo(priorityA);
        });
        bestMatch = matchedBatches.first;
      }
    }

    final bestMatchedText = bestMatch?.matchedText;

    return SmartMatchResult(
      isMatched: isOverallMatched,
      batchMatches: batchMatches,
      bestMatchedText: bestMatchedText,
      overallConfidence: overallConfidence,
      matchDetails: {
        'totalBatches': matchConfig.batches.length,
        'matchedBatches': matchCount,
        'matchRate': matchConfig.batches.length > 0
            ? matchCount / matchConfig.batches.length
            : 0.0,
        'isMixedLoad': matchConfig.isMixedLoad,
        'strategies':
            batchMatches.map((b) => b.details?['matchStrategy']).toList(),
        'bestMatchId': bestMatch?.batchId, // 🔧 新增：记录最佳匹配的批次ID
      },
    );
  }

  /// 🔧 新增：支持后缀的批号匹配
  String? _findBestMatchWithSuffix(String text, String target) {
    // 1. 直接匹配
    if (text.contains(target)) {
      return target;
    }

    // 2. 提取核心批号（去除后缀）
    final corePattern = RegExp(r'(\d{6}[A-Z]\d{5})');
    final match = corePattern.firstMatch(target);
    if (match != null) {
      final coreBatchNumber = match.group(1)!;
      if (text.contains(coreBatchNumber)) {
        return coreBatchNumber;
      }
    }

    // 3. 模糊匹配
    return _findBestMatch(text, target);
  }

  /// 清理文本
  String _cleanText(String text) {
    return text
        .replaceAll(RegExp(r'\s+'), ' ') // 合并多个空格
        .trim();
  }

  /// 查找最佳匹配
  String? _findBestMatch(String text, String target) {
    if (text.contains(target)) {
      return target;
    }
    return null;
  }
}

/// 🔧 新增：智能匹配配置
class RecognitionMatchConfig {
  final List<BatchInfo> batches; // 所有批次信息
  final bool isMixedLoad; // 是否为混装任务
  final double minConfidence; // 最小置信度
  final bool enableFuzzyMatch; // 是否启用模糊匹配
  final Map<String, String>? characterMappings; // 字符映射（如O->0, I->1）

  RecognitionMatchConfig({
    required this.batches,
    this.isMixedLoad = false,
    this.minConfidence = 0.7,
    this.enableFuzzyMatch = true,
    this.characterMappings,
  });
}

/// 🔧 新增：智能匹配结果
class SmartMatchResult {
  final bool isMatched;
  final List<BatchMatchResult> batchMatches;
  final String? bestMatchedText;
  final double overallConfidence;
  final Map<String, dynamic>? matchDetails;

  SmartMatchResult({
    required this.isMatched,
    required this.batchMatches,
    this.bestMatchedText,
    required this.overallConfidence,
    this.matchDetails,
  });
}

/// 🔧 新增：智能后处理配置
class PostProcessingConfig {
  final List<String> presetProductCodes; // 预设牌号库
  final List<String> validBatchLetters; // 有效批号字母集
  final int maxProductCodeDiff; // 牌号最大差异位数
  final bool enableFuzzyMatch; // 是否启用模糊匹配
  final bool enableBatchFormatValidation; // 是否启用批号格式验证

  PostProcessingConfig({
    required this.presetProductCodes,
    this.validBatchLetters = const [
      'F',
      'H',
      'P',
      'A',
      'B',
      'C',
      'D',
      'E',
      'G',
      'I',
      'J',
      'K',
      'L',
      'M',
      'N',
      'O',
      'Q',
      'R',
      'S',
      'T',
      'U',
      'V',
      'W',
      'X',
      'Y',
      'Z'
    ],
    this.maxProductCodeDiff = 2,
    this.enableFuzzyMatch = true,
    this.enableBatchFormatValidation = true,
  });
}

/// 🔧 新增：智能后处理结果
class PostProcessedResult {
  final String originalText; // 原始识别文本
  final String? correctedProductCode; // 纠错后的牌号
  final String? correctedBatchNumber; // 纠错后的批号
  final bool productCodeCorrected; // 牌号是否被纠错
  final bool batchNumberCorrected; // 批号是否被纠错
  final Map<String, dynamic> correctionDetails; // 纠错详情

  PostProcessedResult({
    required this.originalText,
    this.correctedProductCode,
    this.correctedBatchNumber,
    this.productCodeCorrected = false,
    this.batchNumberCorrected = false,
    this.correctionDetails = const {},
  });
}

/// 🔧 新增：批号格式验证器
class BatchNumberValidator {
  static const String batchPattern = r'^(\d{6})([A-Z])(\d{5})$';
  static final RegExp batchRegex = RegExp(batchPattern);

  /// 验证批号格式
  static bool isValidFormat(String batchNumber) {
    return batchRegex.hasMatch(batchNumber);
  }

  /// 解析批号结构
  static Map<String, String>? parseBatchNumber(String batchNumber) {
    final match = batchRegex.firstMatch(batchNumber);
    if (match == null) return null;

    return {
      'date': match.group(1)!, // YYMMDD
      'letter': match.group(2)!, // 字母
      'sequence': match.group(3)!, // 5位数字
    };
  }

  /// 智能纠错批号
  static String? correctBatchNumber(
      String batchNumber, List<String> validLetters) {
    if (batchNumber.length != 12) return null;

    String corrected = batchNumber;
    bool hasCorrection = false;

    // 前6位必须是数字 - 如果识别出字母，转换为相似数字
    for (int i = 0; i < 6; i++) {
      if (!RegExp(r'[0-9]').hasMatch(batchNumber[i])) {
        // 将字母转换为相似数字
        corrected = corrected.replaceRange(
            i, i + 1, _letterToSimilarDigit(batchNumber[i]));
        hasCorrection = true;
      }
    }

    // 第7位必须是字母 - 如果识别出数字，转换为相似字母
    final letter = batchNumber[6];
    if (!validLetters.contains(letter.toUpperCase())) {
      // 第7位识别出数字，转换为相似字母
      final correctedLetter = _digitToSimilarLetter(letter);
      if (correctedLetter != letter) {
        corrected = corrected.replaceRange(6, 7, correctedLetter);
        hasCorrection = true;
      }
    }

    // 后5位必须是数字 - 如果识别出字母，转换为相似数字
    for (int i = 7; i < 12; i++) {
      if (!RegExp(r'[0-9]').hasMatch(batchNumber[i])) {
        // 将字母转换为相似数字
        corrected = corrected.replaceRange(
            i, i + 1, _letterToSimilarDigit(batchNumber[i]));
        hasCorrection = true;
      }
    }

    return hasCorrection ? corrected : null;
  }

  /// 字母转相似数字
  static String _letterToSimilarDigit(String letter) {
    final mappings = {
      'O': '0',
      'o': '0',
      'I': '1',
      'i': '1',
      'l': '1',
      'L': '1',
      'S': '5',
      's': '5',
      'G': '6',
      'g': '6',
      'B': '8',
      'b': '8',
      'Z': '2',
      'z': '2',
    };
    return mappings[letter] ?? letter;
  }

  /// 数字转相似字母
  static String _digitToSimilarLetter(String digit) {
    final mappings = {
      '0': 'O',
      '1': 'I',
      '5': 'S',
      '6': 'G',
      '8': 'B',
      '2': 'Z',
    };
    return mappings[digit] ?? digit;
  }
}

/// 🔧 新增：牌号模糊匹配器
class ProductCodeMatcher {
  /// 计算编辑距离
  static int levenshteinDistance(String s1, String s2) {
    if (s1.isEmpty) return s2.length;
    if (s2.isEmpty) return s1.length;

    List<int> v0 = List<int>.filled(s2.length + 1, 0);
    List<int> v1 = List<int>.filled(s2.length + 1, 0);

    for (int i = 0; i <= s2.length; i++) {
      v0[i] = i;
    }

    for (int i = 0; i < s1.length; i++) {
      v1[0] = i + 1;

      for (int j = 0; j < s2.length; j++) {
        int cost = s1[i] == s2[j] ? 0 : 1;
        v1[j + 1] = [v1[j] + 1, v0[j + 1] + 1, v0[j] + cost]
            .reduce((a, b) => a < b ? a : b);
      }

      List<int> temp = v0;
      v0 = v1;
      v1 = temp;
    }

    return v0[s2.length];
  }

  /// 查找最相似的预设牌号
  static String? findBestMatch(
      String recognizedCode, List<String> presetCodes, int maxDiff) {
    if (presetCodes.isEmpty) return null;

    String? bestMatch;
    int minDistance = maxDiff + 1;

    for (final presetCode in presetCodes) {
      final distance = levenshteinDistance(recognizedCode, presetCode);
      if (distance <= maxDiff && distance < minDistance) {
        minDistance = distance;
        bestMatch = presetCode;
      }
    }

    return bestMatch;
  }
}
