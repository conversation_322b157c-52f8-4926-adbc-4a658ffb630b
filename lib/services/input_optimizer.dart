import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:loadguard/services/logging_service.dart';

/// 🎯 输入优化服务
/// 优化输入法交互和滚动识别体验
class InputOptimizer {
  static final InputOptimizer _instance = InputOptimizer._internal();
  factory InputOptimizer() => _instance;
  InputOptimizer._internal();

  static const String _tag = 'Input';

  // 输入法优化
  static const Duration _inputDebounceDelay = Duration(milliseconds: 300);
  static const Duration _scrollDebounceDelay = Duration(milliseconds: 150);
  static const double _scrollThreshold = 50.0; // 滚动识别阈值

  // 防抖定时器
  Timer? _inputDebounceTimer;
  Timer? _scrollDebounceTimer;

  // 滚动状态
  bool _isScrolling = false;
  DateTime? _lastScrollTime;

  // 输入状态
  bool _isInputActive = false;
  DateTime? _lastInputTime;

  /// 初始化输入优化服务
  Future<void> initialize() async {
    LoggingService.info('🎯 初始化输入优化服务', tag: _tag);

    // 设置输入法监听
    _setupInputMethodListener();

    LoggingService.info('✅ 输入优化服务初始化完成', tag: _tag);
  }

  /// 优化输入法显示
  static Future<void> showInputMethod(
    BuildContext context, {
    bool animated = true,
    Duration? animationDuration,
  }) async {
    try {
      LoggingService.info('⌨️ 显示输入法', tag: _tag);

      // 使用防抖避免频繁调用
      _instance._debounceInput(() async {
        await SystemChannels.textInput.invokeMethod('TextInput.show');

        if (animated) {
          await Future.delayed(
              animationDuration ?? const Duration(milliseconds: 250));
        }

        _instance._isInputActive = true;
        _instance._lastInputTime = DateTime.now();

        LoggingService.info('✅ 输入法显示完成', tag: _tag);
      });
    } catch (e) {
      LoggingService.error('❌ 显示输入法失败', error: e, tag: _tag);
    }
  }

  /// 优化输入法隐藏
  static Future<void> hideInputMethod(
    BuildContext context, {
    bool animated = true,
    Duration? animationDuration,
  }) async {
    try {
      LoggingService.info('⌨️ 隐藏输入法', tag: _tag);

      // 使用防抖避免频繁调用
      _instance._debounceInput(() async {
        await SystemChannels.textInput.invokeMethod('TextInput.hide');

        if (animated) {
          await Future.delayed(
              animationDuration ?? const Duration(milliseconds: 250));
        }

        _instance._isInputActive = false;

        LoggingService.info('✅ 输入法隐藏完成', tag: _tag);
      });
    } catch (e) {
      LoggingService.error('❌ 隐藏输入法失败', error: e, tag: _tag);
    }
  }

  /// 优化滚动识别
  static bool shouldTriggerScrollRecognition(double delta) {
    // 检查滚动幅度是否超过阈值
    if (delta.abs() < _scrollThreshold) {
      return false;
    }

    // 检查是否正在滚动
    if (_instance._isScrolling) {
      return false;
    }

    // 检查滚动频率
    final now = DateTime.now();
    if (_instance._lastScrollTime != null) {
      final timeDiff = now.difference(_instance._lastScrollTime!);
      if (timeDiff < _scrollDebounceDelay) {
        return false;
      }
    }

    // 触发滚动识别
    _instance._isScrolling = true;
    _instance._lastScrollTime = now;

    // 设置滚动结束定时器
    _instance._scrollDebounceTimer?.cancel();
    _instance._scrollDebounceTimer = Timer(_scrollDebounceDelay, () {
      _instance._isScrolling = false;
    });

    LoggingService.info('📜 触发滚动识别: ${delta.toStringAsFixed(2)}', tag: _tag);
    return true;
  }

  /// 优化文本输入
  static void optimizeTextInput(
    TextEditingController controller,
    Function(String) onChanged, {
    Duration? debounceDelay,
  }) {
    String? _lastValue;
    Timer? _debounceTimer;

    controller.addListener(() {
      final currentValue = controller.text;

      // 避免重复处理相同值
      if (currentValue == _lastValue) return;

      _lastValue = currentValue;

      // 防抖处理
      _debounceTimer?.cancel();
      _debounceTimer = Timer(debounceDelay ?? _inputDebounceDelay, () {
        onChanged(currentValue);
      });
    });
  }

  /// 优化焦点管理
  static void optimizeFocusManagement(
    BuildContext context,
    FocusNode focusNode, {
    VoidCallback? onFocusGained,
    VoidCallback? onFocusLost,
  }) {
    focusNode.addListener(() {
      if (focusNode.hasFocus) {
        LoggingService.info('🎯 获得焦点', tag: _tag);
        onFocusGained?.call();
      } else {
        LoggingService.info('🎯 失去焦点', tag: _tag);
        onFocusLost?.call();
      }
    });
  }

  /// 设置输入法监听
  void _setupInputMethodListener() {
    // 🔧 修复：移除输入法监听器，避免干扰正常输入
    // 原来的监听器会拦截输入事件，导致无法输入新字符
    LoggingService.info('⌨️ 输入法监听器已禁用，避免干扰正常输入', tag: _tag);

    // 注释掉原来的监听器代码
    /*
    SystemChannels.textInput.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'TextInputClient.onConnectionClosed':
          _isInputActive = false;
          LoggingService.info('⌨️ 输入法连接关闭', tag: _tag);
          break;
        case 'TextInputClient.onTextInputAction':
          LoggingService.info('⌨️ 输入法动作: ${call.arguments}', tag: _tag);
          break;
      }
    });
    */
  }

  /// 输入防抖
  void _debounceInput(VoidCallback callback) {
    _inputDebounceTimer?.cancel();
    _inputDebounceTimer = Timer(_inputDebounceDelay, callback);
  }

  /// 获取输入状态
  static bool get isInputActive => _instance._isInputActive;
  static bool get isScrolling => _instance._isScrolling;

  /// 释放资源
  void dispose() {
    _inputDebounceTimer?.cancel();
    _scrollDebounceTimer?.cancel();
    LoggingService.info('🔄 输入优化服务已释放', tag: _tag);
  }
}

/// 优化的文本输入控制器
class OptimizedTextEditingController extends TextEditingController {
  final Function(String)? onOptimizedChanged;
  final Duration debounceDelay;

  String? _lastValue;
  Timer? _debounceTimer;

  OptimizedTextEditingController({
    super.text,
    this.onOptimizedChanged,
    this.debounceDelay = const Duration(milliseconds: 300),
  }) {
    addListener(_handleTextChange);
  }

  void _handleTextChange() {
    final currentValue = text;

    // 避免重复处理相同值
    if (currentValue == _lastValue) return;

    _lastValue = currentValue;

    // 防抖处理
    _debounceTimer?.cancel();
    _debounceTimer = Timer(debounceDelay, () {
      onOptimizedChanged?.call(currentValue);
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }
}

/// 优化的滚动控制器
class OptimizedScrollController extends ScrollController {
  final Function(double)? onScrollThresholdReached;
  final double scrollThreshold;

  OptimizedScrollController({
    super.initialScrollOffset,
    super.keepScrollOffset,
    super.debugLabel,
    this.onScrollThresholdReached,
    this.scrollThreshold = 50.0,
  });

  @override
  void addListener(VoidCallback listener) {
    super.addListener(() {
      // 检查是否触发滚动识别
      if (hasClients && position.pixels.abs() > scrollThreshold) {
        onScrollThresholdReached?.call(position.pixels);
      }
      listener();
    });
  }
}
