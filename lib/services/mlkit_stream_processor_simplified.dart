import 'dart:async';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:loadguard/services/mlkit_version_adapter.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/models/task_model.dart';

/// 📸 简化的 ML Kit 流式处理服务
/// 专注于核心业务需求，避免过度复杂化
class MLKitStreamProcessorSimplified {
  static final MLKitStreamProcessorSimplified _instance =
      MLKitStreamProcessorSimplified._internal();
  factory MLKitStreamProcessorSimplified() => _instance;
  MLKitStreamProcessorSimplified._internal();

  TextRecognizer? _textRecognizer;
  StreamController<RecognitionResult>? _resultController;
  bool _isInitialized = false;

  /// 🚀 初始化流式处理器
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info('🚀 初始化简化版 ML Kit 流式处理器...');

      // 🔧 使用适配器创建识别器
      _textRecognizer = MLKitVersionAdapter.createTextRecognizer();

      // 创建结果流
      _resultController = StreamController<RecognitionResult>.broadcast();

      _isInitialized = true;
      AppLogger.info('✅ 简化版 ML Kit 流式处理器初始化成功');
    } catch (e) {
      AppLogger.error('❌ 简化版 ML Kit 流式处理器初始化失败: $e');
      rethrow;
    }
  }

  /// 📱 处理文件图像（适配现有业务）
  Future<void> processFileImage(String imagePath) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final inputImage = InputImage.fromFilePath(imagePath);
      final recognizedText = await _textRecognizer!.processImage(inputImage);

      if (recognizedText.text.isNotEmpty) {
        final result = RecognitionResult(
          ocrText: recognizedText.text,
          isQrOcrConsistent: true,
          matchesPreset: false,
          recognitionTime: DateTime.now(),
          status: RecognitionStatus.completed,
        );

        _resultController?.add(result);
      }
    } catch (e) {
      AppLogger.error('❌ 文件图像处理失败: $e');
    }
  }

  /// 🔄 获取识别结果流
  Stream<RecognitionResult>? get resultStream => _resultController?.stream;

  /// 🗑️ 释放资源
  void dispose() {
    _textRecognizer?.close();
    _resultController?.close();
    _isInitialized = false;
    AppLogger.info('🗑️ 简化版 ML Kit 流式处理器资源已释放');
  }
}
