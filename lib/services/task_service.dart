import 'dart:convert';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/template_config.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/services/workload_assignment_service.dart';
import 'package:loadguard/services/personal_workload_history_service.dart';
import 'package:loadguard/services/hive_storage_service.dart';
import '../models/worker_info_data.dart';
import '../models/task_model_extensions.dart';
import '../repositories/task_repository.dart';
import '../services/shared_preferences_data_source.dart';

/// 任务管理服务 - 重构为使用Repository模式
/// 保持ChangeNotifier以确保向后兼容性，但主要逻辑迁移到Repository
class TaskService extends ChangeNotifier {
  // 🔧 重构：使用Repository模式替代直接数据管理
  late final TaskRepository _repository;
  late final StreamSubscription _tasksSubscription;

  TaskModel? _currentTask;
  List<TaskModel> _tasks = [];
  SharedPreferences? _prefs;

  // 🚀 批量更新机制（保留以确保兼容性）
  Timer? _updateTimer;
  bool _pendingUpdate = false;
  static const Duration _batchUpdateDelay = Duration(milliseconds: 100);

  // 🔧 移除旧的增量保存机制，由Repository处理
  bool _isInitialized = false;

  // 🔧 保留以确保兼容性
  final Set<String> _changedTaskIds = {};
  Timer? _saveTimer;
  bool _fullSaveRequired = false;

  TaskModel? get currentTask => _currentTask;
  List<TaskModel> get tasks => _tasks;

  /// 🔧 重构：初始化Repository
  TaskService() {
    _initializeRepository();
  }

  /// 🔧 重构：初始化Repository
  void _initializeRepository() {
    try {
      final hiveStorage = HiveStorageService();
      final backupStorage = SharedPreferencesDataSource();

      _repository = TaskRepositoryImpl(
        hiveStorage: hiveStorage,
        backupStorage: backupStorage,
      );

      // 监听Repository的数据变化
      _tasksSubscription = _repository.watchTasks().listen((tasks) {
        _tasks = tasks;
        _scheduleUpdate(); // 保持原有的批量更新机制
      });

      AppLogger.info('TaskService Repository初始化完成', tag: 'TaskService');
    } catch (e, stackTrace) {
      AppLogger.error('TaskService Repository初始化失败: $e',
          tag: 'TaskService', stackTrace: stackTrace);
    }
  }

  /// 公共的加载任务方法
  Future<void> loadTasks() async {
    if (!_isInitialized) {
      await _loadTasks();
      _isInitialized = true;
    }

    // 🔧 重构：从Repository获取任务
    try {
      _tasks = await _repository.getAllTasks();
      _currentTask = _repository.currentTask;
      _scheduleUpdate();
      AppLogger.info('从Repository加载${_tasks.length}个任务', tag: 'TaskService');
    } catch (e, stackTrace) {
      AppLogger.error('从Repository加载任务失败: $e', tag: 'TaskService', stackTrace: stackTrace);
      // 降级到原有方法
      await _loadTasks();
    }
  }

  /// 🚀 批量更新机制 - 防止频繁UI刷新
  void _scheduleUpdate() {
    if (_pendingUpdate) return;

    _pendingUpdate = true;
    _updateTimer?.cancel();
    _updateTimer = Timer(_batchUpdateDelay, () {
      _pendingUpdate = false;
      // 🔧 修复：显式调用notifyListeners通知状态更新
      notifyListeners();
    });
  }

  @override
  void dispose() {
    // 🔧 重构：清理Repository资源
    try {
      _tasksSubscription.cancel();
      AppLogger.info('TaskService Repository资源清理完成', tag: 'TaskService');
    } catch (e) {
      AppLogger.warning('TaskService Repository资源清理失败: $e', tag: 'TaskService');
    }

    // 🚀 清理批量更新Timer
    _updateTimer?.cancel();
    _updateTimer = null;

    // 🚀 清理增量保存Timer（保留以确保兼容性）
    _saveTimer?.cancel();
    _saveTimer = null;

    AppLogger.info('🧹 任务服务资源已清理');
    super.dispose();
  }

  /// 初始化服务，加载本地任务数据
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();

    // 初始化Hive存储
    await HiveStorageService.initialize();

    await _loadTasks();
  }

  /// 刷新任务数据
  Future<void> refreshData() async {
    try {
      await _loadTasks();
      AppLogger.info('任务数据刷新完成，共${_tasks.length}个任务');
    } catch (e) {
      AppLogger.error('刷新任务数据失败: $e');
      rethrow;
    }
  }

  /// 🔧 强制保存所有数据（用于PDF生成前确保数据一致性）
  Future<void> forceSaveAllData() async {
    try {
      // 标记为需要全量保存
      _fullSaveRequired = true;
      _changedTaskIds.clear();
      
      // 立即执行保存
      await _performSave();
      AppLogger.info('强制保存所有数据完成');
    } catch (e) {
      AppLogger.error('强制保存数据失败: $e');
      rethrow;
    }
  }

  /// 加载历史任务 - 🚀 优化：使用Hive存储
  Future<void> _loadTasks() async {
    try {
      // 优先从Hive加载
      _tasks = HiveStorageService.getAllTasks();

      // 如果Hive为空，尝试从SharedPreferences迁移
      if (_tasks.isEmpty && _prefs != null) {
        await _migrateFromSharedPreferences();
      }

      // 按创建时间倒序排序
      _tasks.sort((a, b) => b.createTime.compareTo(a.createTime));

      // 🚀 使用批量更新机制
      _scheduleUpdate();

      AppLogger.info('📚 任务加载完成: ${_tasks.length}个任务');
    } catch (e) {
      AppLogger.error('任务加载失败: $e');
      _tasks = [];
      _scheduleUpdate();
    }
  }
  
  /// 🔄 从SharedPreferences迁移数据到Hive
  Future<void> _migrateFromSharedPreferences() async {
    try {
      final tasksJson = _prefs!.getString('tasks');
      if (tasksJson != null) {
        final tasksList = jsonDecode(tasksJson) as List;
        final tasks = tasksList.map((json) => TaskModel.fromJson(json)).toList();

        // 批量保存到Hive
        await HiveStorageService.saveTasks(tasks);
        _tasks = tasks;

        AppLogger.info('🔄 数据迁移完成: ${tasks.length}个任务从SharedPreferences迁移到Hive');

        // 清理旧数据（可选）
        // await _prefs!.remove('tasks');
      }
    } catch (e) {
      AppLogger.error('❌ 数据迁移失败: $e');
    }
  }

  
  /// 🚀 新增：调度延迟保存
  void _scheduleSave([String? taskId]) {
    // 记录变更的任务ID
    if (taskId != null) {
      _changedTaskIds.add(taskId);
    } else {
      _fullSaveRequired = true;
    }
    
    // 如果变更任务数量超过阈值，标记为需要全量保存
    if (_changedTaskIds.length > _maxChangedTasks) {
      _fullSaveRequired = true;
      _changedTaskIds.clear();
    }
    
    // 取消之前的保存计划
    _saveTimer?.cancel();
    
    // 调度新的保存任务
    _saveTimer = Timer(_saveDelay, () {
      _performSave();
    });
  }
  
  /// 🔧 修复：执行实际保存操作
  Future<void> _performSave() async {
    if (_prefs == null) return;
    
    try {
      // 🔧 修复：总是执行全量保存，确保数据完整性
      await _saveTasksFull();
      _fullSaveRequired = false;
      _changedTaskIds.clear();
      AppLogger.debug('📦 执行全量任务保存: ${_tasks.length}个任务');
    } catch (e) {
      AppLogger.error('任务保存失败: $e');
      // 保存失败时，重新调度保存
      _scheduleSave();
    }
  }
  
  /// 🚀 新增：全量保存 - 使用Hive存储
  Future<void> _saveTasksFull() async {
    // 保存到Hive
    await HiveStorageService.saveTasks(_tasks);

    // 同时保存到SharedPreferences作为备份
    if (_prefs != null) {
      final tasksJson = jsonEncode(_tasks.map((task) => task.toJson()).toList());
      await _prefs!.setString('tasks', tasksJson);
    }
  }
  
  /// 🚀 已废弃：增量保存 - 现在使用Hive存储
  @Deprecated('使用Hive存储替代')
  Future<void> _saveTasksIncremental() async {
    // 现在直接使用Hive存储
    await _saveTasksFull();
  }
  
  /// 🚀 新增：同步保存（用于dispose时的紧急保存）
  void _saveTasksSync() {
    if (_prefs == null) return;
    
    try {
      final tasksJson = jsonEncode(_tasks.map((task) => task.toJson()).toList());
      _prefs!.setString('tasks', tasksJson);
      AppLogger.info('📦 同步保存任务完成: ${_tasks.length}个任务');
    } catch (e) {
      AppLogger.error('同步保存任务失败: $e');
    }
  }

  /// 保存任务（新增或更新）
  Future<void> saveTask(TaskModel task) async {
    try {
      // 🔧 重构：使用Repository保存任务
      await _repository.saveTask(task);

      // 更新当前任务（如果是同一个任务）
      if (_currentTask?.id == task.id) {
        _currentTask = task;
        await _repository.setCurrentTask(task);
      }

      AppLogger.info('✅ 任务保存成功: ${task.id}', tag: 'TaskService');

      // 保持向后兼容性：通知监听器
      notifyListeners();
    } catch (e, stackTrace) {
      AppLogger.error('任务保存失败: ${task.id}, 错误: $e',
          tag: 'TaskService', stackTrace: stackTrace);

      // 🔧 降级策略：使用原有方法
      final existingIndex = _tasks.indexWhere((t) => t.id == task.id);
      if (existingIndex >= 0) {
        _tasks[existingIndex] = task;
        AppLogger.info('📝 降级更新任务: ${task.id}');
      } else {
        _tasks.add(task);
        AppLogger.info('➕ 降级添加新任务: ${task.id}');
      }

      await _saveTasksFull();
      notifyListeners();
    }
  }

  /// 创建新任务
  Future<TaskModel> createTask({
    required String template,
    required String productCode,
    required String batchNumber,
    required int quantity,
    List<String> participants = const [],
  }) async {
    // 获取模板配置
    final photoConfigs = TemplateConfig.getPhotoConfigs(template);

    // 创建照片项列表
    final photos = photoConfigs
        .map((config) => PhotoItem(
              label: config.label,
              configId: config.id, // 使用配置ID进行识别匹配
              isRequired: config.isRequired,
              needRecognition: config.needRecognition,
            ))
        .toList();

    // 创建任务
    final task = TaskModel(
      template: template,
      productCode: productCode,
      batchNumber: batchNumber,
      quantity: quantity,
      photos: photos,
      participants: participants,
    );

    // 创建任务时附加工作量分配数据
    try {
      final assignment =
          await WorkloadAssignmentService.loadCurrentAssignment();
      if (assignment != null) {
        task.recognitionMetadata ??= {};
        task.recognitionMetadata!['workload'] = assignment.toMap();
        AppLogger.info('📋 工作量数据已附加到新任务: ${assignment.records.length}人');
      } else {
        AppLogger.warning('⚠️ 创建任务时没有找到工作量分配数据');
      }
    } catch (e) {
      AppLogger.error('❌ 附加工作量数据失败: $e');
    }

    // 设置为当前任务
    _currentTask = task;

    // 添加到任务列表
    _tasks.insert(0, task);
    _scheduleSave(task.id); // 🚀 优化：指定新创建的任务ID

    // 🔧 修复：立即通知状态更新，确保任务创建后立即可用
    notifyListeners();

    // 🚀 使用批量更新机制
    _scheduleUpdate();
    return task;
  }

  /// 创建混装任务
  Future<TaskModel> createMixedTask({
    required String template,
    required List<BatchInfo> batches,
    List<String> participants = const [],
  }) async {
    if (batches.isEmpty) {
      throw Exception('批次列表不能为空');
    }

    // 获取模板配置
    final photoConfigs = TemplateConfig.getPhotoConfigs(template);

    // 创建照片项 - 修复：不为每个批次重复创建照片
    final photos = <PhotoItem>[];

    // 每个模板照片配置只创建一个照片项，而不是每个批次都创建
    for (final config in photoConfigs) {
      photos.add(PhotoItem(
        label: config.label,
        configId: config.id, // 🔧 核心修复：使用config.id进行识别匹配
        isRequired: config.isRequired,
        needRecognition: config.needRecognition,
      ));
    }

    // 创建任务
    final task = TaskModel(
      template: template,
      batches: batches,
      photos: photos,
      participants: participants,
    );

    // 为混装任务附加工作量分配数据
    try {
      final assignment =
          await WorkloadAssignmentService.loadCurrentAssignment();
      if (assignment != null) {
        task.recognitionMetadata ??= {};
        task.recognitionMetadata!['workload'] = assignment.toMap();
        task.recognitionMetadata!['workloadAssignedAt'] =
            DateTime.now().toIso8601String();
        AppLogger.info(
            '📋 工作量数据已附加到混装任务: ${assignment.records.length}人, ${assignment.totalTonnage}吨');
      } else {
        AppLogger.warning('⚠️ 创建混装任务时没有找到工作量分配数据');
        // 如果没有工作量分配，创建默认分配
        if (participants.isNotEmpty) {
          final defaultAssignment = WorkloadAssignment(
            records: participants
                .map((id) => WorkloadRecord(
                      workerId: id,
                      workerName: id, // 临时使用ID作为姓名
                      role: 'worker',
                      warehouse: '1号库',
                      group: '默认组',
                      allocatedTonnage:
                          task.quantity * 1.5 / participants.length,
                      assignedAt: DateTime.now(),
                    ))
                .toList(),
            totalTonnage: task.quantity * 1.5,
            palletCount: task.quantity,
            assignedAt: DateTime.now(),
            assignedBy: 'system',
          );
          task.recognitionMetadata ??= {};
          task.recognitionMetadata!['workload'] = defaultAssignment.toMap();
          task.recognitionMetadata!['workloadAssignedAt'] =
              DateTime.now().toIso8601String();
          AppLogger.info(
              '📋 已创建默认工作量分配: ${participants.length}人, ${task.quantity * 1.5}吨');
        }
      }
    } catch (e) {
      AppLogger.error('❌ 附加工作量数据失败: $e');
    }

    // 设置为当前任务
    _currentTask = task;

    // 添加到任务列表
    _tasks.insert(0, task);
    _scheduleSave(task.id); // 🚀 优化：指定新创建的混装任务ID

    // 🔧 修复：立即通知状态更新，确保任务创建后立即可用
    notifyListeners();

    // 🚀 使用批量更新机制
    _scheduleUpdate();
    return task;
  }

  /// 更新照片
  Future<void> updatePhoto(String photoId, String imagePath) async {
    AppLogger.info('=== 更新照片开始 ===');
    AppLogger.info('photoId: $photoId');
    AppLogger.info('imagePath: $imagePath');
    AppLogger.info('_currentTask: ${_currentTask?.template}');
    
    if (_currentTask == null) {
      AppLogger.error('_currentTask为null，无法更新照片');
      return;
    }

    // 同时更新当前任务和任务列表中的照片数据
    final photoIndex = _currentTask!.photos.indexWhere((p) => p.id == photoId);
    AppLogger.info('找到照片索引: $photoIndex');
    
    if (photoIndex != -1) {
      final photo = _currentTask!.photos[photoIndex];
      AppLogger.info('更新前照片路径: ${photo.imagePath}');
      AppLogger.info('照片标签: ${photo.label}');
      AppLogger.info('照片ID: ${photo.id}');
      
      // 更新当前任务中的照片
      _currentTask!.photos[photoIndex].imagePath = imagePath;
      _currentTask!.photos[photoIndex].isVerified = false;
      _currentTask!.photos[photoIndex].recognitionResult = null;
      _currentTask!.photos[photoIndex].isRecognitionCompleted = false;
      // 🔧 修复：初始设置为processing状态，等待识别完成后再更新
      _currentTask!.photos[photoIndex].recognitionStatus = RecognitionStatus.processing;
      _currentTask!.photos[photoIndex].recognitionStartTime = DateTime.now();
      // 不立即设置结束时间，等识别完成后设置
      
      AppLogger.info('更新后照片路径: ${_currentTask!.photos[photoIndex].imagePath}');
      AppLogger.info('更新后照片状态: ${_currentTask!.photos[photoIndex].recognitionStatus}');

      // 🔧 关键修复：确保任务列表引用的是同一个对象
      final taskIndex = _tasks.indexWhere((task) => task.id == _currentTask!.id);
      AppLogger.info('找到任务索引: $taskIndex');
      
      if (taskIndex != -1) {
        // 🔧 修复：直接替换整个任务对象，确保引用一致性
        _tasks[taskIndex] = _currentTask!;
        AppLogger.info('已同步整个任务对象到任务列表');
      }

      // 🔧 修复：立即同步保存，不使用延迟
      try {
        final tasksJson = jsonEncode(_tasks.map((task) => task.toJson()).toList());
        await _prefs!.setString('tasks', tasksJson);
        AppLogger.info('照片路径已立即同步保存到SharedPreferences');
      } catch (e) {
        AppLogger.error('立即保存失败: $e');
      }

      // 🚀 使用批量更新机制
      _scheduleUpdate();
    } else {
      AppLogger.error('未找到照片ID: $photoId');
    }
  }

  /// 更新识别结果，支持多批次任务
  Future<void> updateRecognitionResult(
      String photoId, RecognitionResult result) async {
    if (_currentTask == null) return;

    final photoIndex = _currentTask!.photos.indexWhere((p) => p.id == photoId);
    if (photoIndex != -1) {
      final photo = _currentTask!.photos[photoIndex];

      // 使用枚举管理识别状态
      photo.recognitionResult = result;
      photo.isVerified = result.matchesPreset;
      photo.recognitionStatus = result.status; // 统一状态管理
      photo.recognitionEndTime = DateTime.now();
      photo.recognitionErrorMessage = result.errorMessage;
      photo.recognitionMetadata = result.metadata;
      photo.ocrText = result.ocrText;
      photo.matchedProductCode = result.extractedProductCode;
      photo.matchedBatchNumber = result.extractedBatchNumber;

      // 同步更新任务列表中的对应任务
      final taskIndex =
          _tasks.indexWhere((task) => task.id == _currentTask!.id);
      if (taskIndex != -1) {
        final taskPhotoIndex =
            _tasks[taskIndex].photos.indexWhere((p) => p.id == photoId);
        if (taskPhotoIndex != -1) {
          final taskPhoto = _tasks[taskIndex].photos[taskPhotoIndex];
          taskPhoto.recognitionResult = result;
          taskPhoto.isVerified = result.matchesPreset;
          taskPhoto.recognitionStatus = result.status;
          taskPhoto.recognitionEndTime = DateTime.now();
          taskPhoto.recognitionErrorMessage = result.errorMessage;
          taskPhoto.recognitionMetadata = result.metadata;
          taskPhoto.ocrText = result.ocrText;
          taskPhoto.matchedProductCode = result.extractedProductCode;
          taskPhoto.matchedBatchNumber = result.extractedBatchNumber;
          // 识别结果已同步更新到任务列表
        }
      }

      // 支持多批次匹配，更新批次统计
      if (result.matchesPreset &&
          result.extractedProductCode != null &&
          result.extractedBatchNumber != null) {
        // 查找匹配的批次
        final matchedBatch = _currentTask!.batches.firstWhere(
          (b) =>
              b.productCode == result.extractedProductCode &&
              b.batchNumber == result.extractedBatchNumber,
          orElse: () =>
              BatchInfo(productCode: '', batchNumber: '', plannedQuantity: 0),
        );

        if (matchedBatch.productCode.isNotEmpty) {
          // 更新批次识别统计
          _updateBatchRecognitionStats(matchedBatch, photo.label);

          // 更新照片的匹配批次ID
          photo.matchedBatchIds = [matchedBatch.id];

          // 同步更新任务列表中的批次统计
          if (taskIndex != -1) {
            final taskPhotoIndex =
                _tasks[taskIndex].photos.indexWhere((p) => p.id == photoId);
            if (taskPhotoIndex != -1) {
              final taskPhoto = _tasks[taskIndex].photos[taskPhotoIndex];
              taskPhoto.matchedBatchIds = [matchedBatch.id];
            }
          }

          AppLogger.info(
              '✅ 批次统计已更新: ${matchedBatch.productCode}-${matchedBatch.batchNumber}, 当前识别: ${matchedBatch.recognizedQuantity}/${matchedBatch.plannedQuantity}');
        }
      }

      // 保存到本地
      _scheduleSave(_currentTask!.id); // 🚀 优化：指定当前任务ID进行增量保存

      // Riverpod会自动处理状态更新
    }
  }

  /// 开始照片识别，设置状态为处理中
  Future<void> startRecognition(String photoId) async {
    if (_currentTask == null) return;

    final photoIndex = _currentTask!.photos.indexWhere((p) => p.id == photoId);
    if (photoIndex != -1) {
      final photo = _currentTask!.photos[photoIndex];

      // 重置识别状态
      photo.recognitionStatus = RecognitionStatus.processing;
      photo.recognitionStartTime = DateTime.now();
      photo.recognitionEndTime = null;
      photo.recognitionErrorMessage = null;
      photo.recognitionFailed = false;
      photo.isRecognitionCompleted = false;

      // 同步更新任务列表中的对应任务
      final taskIndex =
          _tasks.indexWhere((task) => task.id == _currentTask!.id);
      if (taskIndex != -1) {
        final taskPhotoIndex =
            _tasks[taskIndex].photos.indexWhere((p) => p.id == photoId);
        if (taskPhotoIndex != -1) {
          final taskPhoto = _tasks[taskIndex].photos[taskPhotoIndex];
          taskPhoto.recognitionStatus = RecognitionStatus.processing;
          taskPhoto.recognitionStartTime = DateTime.now();
          taskPhoto.recognitionEndTime = null;
          taskPhoto.recognitionErrorMessage = null;
          taskPhoto.recognitionFailed = false;
          taskPhoto.isRecognitionCompleted = false;
        }
      }

      // 保存状态
      _scheduleSave(_currentTask!.id); // 🚀 优化：指定当前任务ID进行增量保存

      // Riverpod会自动处理状态更新
    }
  }

  /// 取消照片识别
  Future<void> cancelRecognition(String photoId) async {
    if (_currentTask == null) return;

    final photoIndex = _currentTask!.photos.indexWhere((p) => p.id == photoId);
    if (photoIndex != -1) {
      final photo = _currentTask!.photos[photoIndex];

      photo.recognitionStatus = RecognitionStatus.cancelled;
      photo.recognitionEndTime = DateTime.now();
      photo.isRecognitionCompleted = true;

      // 同步更新任务列表中的对应任务
      final taskIndex =
          _tasks.indexWhere((task) => task.id == _currentTask!.id);
      if (taskIndex != -1) {
        final taskPhotoIndex =
            _tasks[taskIndex].photos.indexWhere((p) => p.id == photoId);
        if (taskPhotoIndex != -1) {
          final taskPhoto = _tasks[taskIndex].photos[taskPhotoIndex];
          taskPhoto.recognitionStatus = RecognitionStatus.cancelled;
          taskPhoto.recognitionEndTime = DateTime.now();
          taskPhoto.isRecognitionCompleted = true;
        }
      }

      _scheduleSave(_currentTask!.id); // 🚀 优化：指定当前任务ID进行增量保存

      // 🚀 使用批量更新机制
      _scheduleUpdate();
    }
  }

  /// 获取任务识别统计数据
  Map<String, dynamic> getRecognitionStatistics() {
    if (_currentTask == null) return {};

    final photos = _currentTask!.photos;
    final totalPhotos = photos.length;
    final needRecognitionPhotos = photos.where((p) => p.needRecognition).length;
    final completedPhotos =
        photos.where((p) => p.isRecognitionCompleted).length;
    final verifiedPhotos = photos.where((p) => p.isVerified).length;
    final failedPhotos = photos.where((p) => p.recognitionFailed).length;
    final processingPhotos = photos
        .where((p) => p.recognitionStatus == RecognitionStatus.processing)
        .length;

    return {
      'totalPhotos': totalPhotos,
      'needRecognitionPhotos': needRecognitionPhotos,
      'completedPhotos': completedPhotos,
      'verifiedPhotos': verifiedPhotos,
      'failedPhotos': failedPhotos,
      'processingPhotos': processingPhotos,
      'completionRate': needRecognitionPhotos > 0
          ? completedPhotos / needRecognitionPhotos
          : 0.0,
      'verificationRate': needRecognitionPhotos > 0
          ? verifiedPhotos / needRecognitionPhotos
          : 0.0,
    };
  }

  /// 获取批次识别统计数据
  Map<String, dynamic> getBatchRecognitionStatistics() {
    if (_currentTask == null || _currentTask!.batches.isEmpty) return {};

    final batchStats = <String, dynamic>{};

    for (final batch in _currentTask!.batches) {
      final batchPhotos = _currentTask!.photos
          .where((p) => p.matchedBatchIds?.contains(batch.id) == true)
          .toList();

      final verifiedPhotos = batchPhotos.where((p) => p.isVerified).length;
      final totalPhotos = batchPhotos.length;

      batchStats[batch.id] = {
        'productCode': batch.productCode,
        'batchNumber': batch.batchNumber,
        'totalPhotos': totalPhotos,
        'verifiedPhotos': verifiedPhotos,
        'verificationRate':
            totalPhotos > 0 ? verifiedPhotos / totalPhotos : 0.0,
      };
    }

    return batchStats;
  }

  /// 🔧 新增：重置照片识别状态
  Future<void> resetPhotoRecognition(String photoId) async {
    if (_currentTask == null) return;

    final photoIndex = _currentTask!.photos.indexWhere((p) => p.id == photoId);
    if (photoIndex != -1) {
      final photo = _currentTask!.photos[photoIndex];

      // 重置所有识别相关状态
      photo.recognitionResult = null;
      photo.isVerified = false;
      photo.isRecognitionCompleted = false;
      photo.recognitionFailed = false;
      photo.recognitionStatus = RecognitionStatus.pending;
      photo.recognitionStartTime = null;
      photo.recognitionEndTime = null;
      photo.recognitionErrorMessage = null;
      photo.recognitionMetadata = null;
      photo.matchedBatchIds = null;
      photo.ocrText = null;
      photo.matchedProductCode = null;
      photo.matchedBatchNumber = null;
      photo.lastRecognitionTime = null;

      // 同步更新任务列表中的对应任务
      final taskIndex =
          _tasks.indexWhere((task) => task.id == _currentTask!.id);
      if (taskIndex != -1) {
        final taskPhotoIndex =
            _tasks[taskIndex].photos.indexWhere((p) => p.id == photoId);
        if (taskPhotoIndex != -1) {
          final taskPhoto = _tasks[taskIndex].photos[taskPhotoIndex];
          taskPhoto.recognitionResult = null;
          taskPhoto.isVerified = false;
          taskPhoto.isRecognitionCompleted = false;
          taskPhoto.recognitionFailed = false;
          taskPhoto.recognitionStatus = RecognitionStatus.pending;
          taskPhoto.recognitionStartTime = null;
          taskPhoto.recognitionEndTime = null;
          taskPhoto.recognitionErrorMessage = null;
          taskPhoto.recognitionMetadata = null;
          taskPhoto.matchedBatchIds = null;
          taskPhoto.ocrText = null;
          taskPhoto.matchedProductCode = null;
          taskPhoto.matchedBatchNumber = null;
          taskPhoto.lastRecognitionTime = null;
        }
      }

      _scheduleSave(_currentTask!.id); // 🚀 优化：指定当前任务ID进行增量保存

      // 🚀 使用批量更新机制
      _scheduleUpdate();
    }
  }

  /// 🆕 **更新照片路径（按标签）**
  /// 提供更稳健的更新方式，尤其是在照片ID不方便获取时。
  Future<void> updatePhotoPath(
      String taskId, String photoLabel, String newPath) async {
    final task = getTaskById(taskId);
    if (task == null) return;

    final photoIndex = task.photos.indexWhere((p) => p.label == photoLabel);
    if (photoIndex != -1) {
      task.photos[photoIndex].imagePath = newPath;
      // 重置验证状态
      task.photos[photoIndex].isVerified = false;
      task.photos[photoIndex].recognitionResult = null;
      task.photos[photoIndex].isRecognitionCompleted = false;

      _scheduleSave(taskId); // 🚀 优化：指定任务ID进行增量保存

      // 🚀 使用批量更新机制
      _scheduleUpdate();
    }
  }

  /// 🆕 **更新照片识别结果（按标签）**
  Future<void> updatePhotoResult(
      String taskId, String photoId, RecognitionResult result) async {
    final task = getTaskById(taskId);
    if (task == null) {
      return;
    }
    final photoIndex = task.photos.indexWhere((p) => p.id == photoId);
    if (photoIndex == -1) {
      return;
    }

    final photo = task.photos[photoIndex];

    // 🔧 统一使用 recognitionStatus 枚举管理状态
    photo.recognitionResult = result;
    photo.isVerified = result.matchesPreset;
    photo.recognitionStatus = result.status; // 🔧 统一状态管理
    photo.recognitionEndTime = DateTime.now();
    photo.recognitionErrorMessage = result.errorMessage;
    photo.recognitionMetadata = result.metadata;
    photo.ocrText = result.ocrText;
    photo.matchedProductCode = result.extractedProductCode;
    photo.matchedBatchNumber = result.extractedBatchNumber;

    // 🔧 支持多批次匹配和单批次匹配
    List<String> matchedBatchIds = [];

    // 处理多批次匹配结果（数组形式）
    if (result.metadata != null &&
        result.metadata!['matchedBatchIds'] != null) {
      matchedBatchIds = List<String>.from(result.metadata!['matchedBatchIds']);
    }
    // 🔧 修复：处理单个批次匹配结果（字符串形式）
    else if (result.metadata != null &&
        result.metadata!['matchedBatchId'] != null) {
      matchedBatchIds = [result.metadata!['matchedBatchId'] as String];
    }

    if (matchedBatchIds.isNotEmpty) {
      photo.matchedBatchIds = matchedBatchIds;

      // 🔧 更新批次识别统计
      for (final batchId in matchedBatchIds) {
        final batchIndex = task.batches.indexWhere((b) => b.id == batchId);
        if (batchIndex != -1) {
          final batch = task.batches[batchIndex];
          _updateBatchRecognitionStats(batch, photo.label);
          AppLogger.info(
              '✅ 批次统计更新: ${batch.productCode} 识别数量: ${batch.recognizedQuantity}/${batch.plannedQuantity}');
        } else {
          AppLogger.warning('⚠️ 未找到匹配的批次ID: $batchId');
        }
      }
    }

    _scheduleSave(taskId); // 🚀 优化：指定任务ID进行增量保存

    // 🚀 使用批量更新机制
    _scheduleUpdate();

    // 🎯 新增：触发工作量统计更新
    _triggerWorkloadStatisticsUpdate(taskId, photoId, result);
  }

  /// 🎯 新增：触发工作量统计更新
  void _triggerWorkloadStatisticsUpdate(
      String taskId, String photoId, RecognitionResult result) {
    try {
      AppLogger.info(
          '🔄 触发工作量统计更新: taskId=$taskId, photoId=$photoId, matched=${result.matchesPreset}');

      // Riverpod会自动处理状态更新

      // 记录工作量统计更新事件
      if (result.matchesPreset) {
        AppLogger.info('✅ 工作量统计更新成功: 识别成功，工作量统计已更新');
      } else {
        AppLogger.info('⚠️ 工作量统计更新: 识别失败，但统计已更新');
      }
    } catch (e) {
      AppLogger.error('❌ 触发工作量统计更新失败: $e');
    }
  }

  /// 🎯 新增：强制刷新工作量统计
  Future<void> refreshWorkloadStatistics() async {
    try {
      AppLogger.info('🔄 强制刷新工作量统计...');

      // 重新加载任务数据
      await _loadTasks();

      // 通知所有监听者更新
      // 🚀 使用批量更新机制
      _scheduleUpdate();

      AppLogger.info('✅ 工作量统计刷新完成');
    } catch (e) {
      AppLogger.error('❌ 刷新工作量统计失败: $e');
    }
  }

  /// 完成任务
  Future<void> completeTask() async {
    if (_currentTask == null) return;

    // 🔧 修复：记录任务完成时间（isCompleted现在是计算属性，会根据完成状态自动计算）
    _currentTask!.completedAt = DateTime.now();

    // 🔧 修复：标记工作量记录为已完成
    if (_currentTask!.recognitionMetadata != null &&
        _currentTask!.recognitionMetadata!['workload'] != null) {
      try {
        final workloadMap = _currentTask!.recognitionMetadata!['workload']
            as Map<String, dynamic>;
        // 标记所有工作量记录为已完成
        if (workloadMap['records'] != null) {
          final records = workloadMap['records'] as List;
          final completedAt = DateTime.now().toIso8601String();

          for (var record in records) {
            record['isCompleted'] = true;
            record['completedAt'] = completedAt;
          }

          // 🔧 强制更新工作量分配记录到持久化存储
          workloadMap['completedAt'] = completedAt;
          workloadMap['status'] = 'completed';

          AppLogger.info(
              '📊 已更新工作量记录完成状态: ${records.length}条, 完成时间: $completedAt');

          // 🔧 尝试同步更新WorkloadAssignmentService中的当前分配状态
          try {
            final assignment = WorkloadAssignmentService.fromMap(workloadMap);
            await WorkloadAssignmentService.saveCurrentAssignment(assignment);
            AppLogger.info('📋 工作量分配状态已同步到持久化存储');
          } catch (syncError) {
            AppLogger.error('⚠️ 同步工作量分配状态失败: $syncError');
          }
        }
      } catch (e) {
        AppLogger.error('❌ 更新工作量记录失败: $e');
      }
    }

    // 平均分配工作量
    if ((_currentTask!.participants.isNotEmpty)) {
      final totalTonnage = _currentTask!.quantity * 1.5;
      final perTonnage = totalTonnage / _currentTask!.participants.length;
      final now = DateTime.now();
      final records = _currentTask!.participants.map((id) {
        final worker = allWorkers.firstWhere((w) => w.id == id,
            orElse: () => WorkerInfo(
                id: id, name: id, role: '', warehouse: '', group: ''));
        return WorkloadRecord(
          workerId: worker.id,
          workerName: worker.name,
          role: worker.role,
          warehouse: worker.warehouse,
          group: worker.group,
          allocatedTonnage: perTonnage,
          assignedAt: now,
          isCompleted: true,
          completedAt: now,
        );
      }).toList();
      final assignment = WorkloadAssignment(
        records: records,
        totalTonnage: totalTonnage,
        palletCount: _currentTask!.quantity,
        assignedAt: now,
        assignedBy: 'system',
      );
      _currentTask!.recognitionMetadata ??= {};
      _currentTask!.recognitionMetadata!['workload'] = assignment.toMap();
    }

    // 更新任务列表中的任务状态
    final index = _tasks.indexWhere((t) => t.id == _currentTask!.id);
    if (index != -1) {
      _tasks[index] = _currentTask!;
    }

    _scheduleSave(_currentTask!.id); // 🚀 优化：指定当前任务ID进行增量保存

    // 🎯 新增：保存个人工作量历史记录
    try {
      // 获取参与者的WorkerInfo对象
      final participants = _currentTask!.participants.map((id) {
        return allWorkers.firstWhere((w) => w.id == id,
            orElse: () => WorkerInfo(
                id: id, name: id, role: '未知', warehouse: '未知', group: ''));
      }).toList();

      if (participants.isNotEmpty) {
        await PersonalWorkloadHistoryService.saveTaskCompletionWorkload(
          task: _currentTask!,
          participants: participants,
        );
        AppLogger.info('📈 个人工作量历史记录已保存: ${participants.length}人参与');
      }
    } catch (e) {
      AppLogger.error('❌ 保存个人工作量历史记录失败: $e');
    }

    // 记录日志
    AppLogger.info(
        '✅ 任务已完成: ${_currentTask!.id}, 完成时间: ${_currentTask!.completedAt}');

    // 🚀 使用批量更新机制
    _scheduleUpdate();
  }

  /// 获取任务通过ID
  TaskModel? getTaskById(String taskId) {
    try {
      return _tasks.firstWhere((task) => task.id == taskId);
    } catch (e) {
      return null;
    }
  }

  /// 删除任务
  Future<void> deleteTask(String taskId) async {
    try {
      // 🔧 重构：使用Repository删除任务
      await _repository.deleteTask(taskId);

      // 如果删除的是当前任务，清除当前任务
      if (_currentTask?.id == taskId) {
        _currentTask = null;
        await _repository.setCurrentTask(null);
      }

      AppLogger.info('✅ 任务删除成功: $taskId', tag: 'TaskService');

      // 🚀 使用批量更新机制
      _scheduleUpdate();
    } catch (e, stackTrace) {
      AppLogger.error('任务删除失败: $taskId, 错误: $e',
          tag: 'TaskService', stackTrace: stackTrace);

      // 🔧 降级策略：使用原有方法
      _tasks.removeWhere((task) => task.id == taskId);
      _scheduleSave();
      _scheduleUpdate();
    }
  }

  /// 设置当前任务
  Future<void> setCurrentTask(TaskModel task) async {
    try {
      // 🔧 重构：使用Repository设置当前任务
      await _repository.setCurrentTask(task);
      _currentTask = task;

      AppLogger.info('✅ 设置当前任务: ${task.id}', tag: 'TaskService');

      // 🚀 使用批量更新机制
      _scheduleUpdate();
    } catch (e, stackTrace) {
      AppLogger.error('设置当前任务失败: ${task.id}, 错误: $e',
          tag: 'TaskService', stackTrace: stackTrace);

      // 🔧 降级策略：直接设置
      _currentTask = task;
      _scheduleUpdate();
    }
  }

  /// 更新任务
  Future<void> updateTask(TaskModel task) async {
    // 查找并更新任务列表中的任务
    final index = _tasks.indexWhere((t) => t.id == task.id);
    if (index != -1) {
      _tasks[index] = task;
    }

    // 如果是当前任务，也更新当前任务引用
    if (_currentTask?.id == task.id) {
      _currentTask = task;
    }

    // 保存到本地存储
    _scheduleSave(task.id); // 🚀 优化：指定更新的任务ID进行增量保存

    // 异步通知监听器
    // 🚀 使用批量更新机制
    _scheduleUpdate();
  }

  /// 清除当前任务
  void clearCurrentTask() {
    _currentTask = null;
    // 🚀 使用批量更新机制
    _scheduleUpdate();
  }

  /// 获取任务的必拍照片数量
  int getRequiredPhotosCount(TaskModel task) {
    return task.photos.where((p) => p.isRequired).length;
  }

  /// 获取任务的已拍必拍照片数量
  int getCompletedRequiredPhotosCount(TaskModel task) {
    return task.photos.where((p) => p.isRequired && p.imagePath != null).length;
  }

  /// 检查任务是否可以完成（所有必拍照片都已拍摄）
  bool canCompleteTask(TaskModel task) {
    return task.photos
        .where((p) => p.isRequired)
        .every((p) => p.imagePath != null);
  }

  /// 🔧 直接保存任务（用于批量操作）
  Future<void> saveTaskDirectly(TaskModel task) async {
    final index = _tasks.indexWhere((t) => t.id == task.id);
    if (index != -1) {
      _tasks[index] = task;
    } else {
      _tasks.insert(0, task);
    }
    _scheduleSave(task.id); // 🚀 优化：指定任务ID进行增量保存
  }

  /// 🔧 直接更新照片（用于批量操作）
  Future<void> updatePhotoDirectly(
      String taskId, String photoId, String imagePath) async {
    final task = _tasks.firstWhere((t) => t.id == taskId);
    final photoIndex = task.photos.indexWhere((p) => p.id == photoId);

    if (photoIndex != -1) {
      task.photos[photoIndex].imagePath = imagePath;
      task.photos[photoIndex].isVerified = false;
      task.photos[photoIndex].recognitionResult = null;
      task.photos[photoIndex].isRecognitionCompleted = false;
      // 🔧 修复：更新识别状态
      task.photos[photoIndex].recognitionStatus = RecognitionStatus.completed;
      task.photos[photoIndex].recognitionEndTime = DateTime.now();
      _scheduleSave(taskId); // 🚀 优化：指定任务ID进行增量保存
    }
  }

  /// 🔧 直接更新识别结果（用于批量操作）
  Future<void> updateRecognitionResultDirectly(
      String taskId, String photoId, RecognitionResult result) async {
    final task = _tasks.firstWhere((t) => t.id == taskId);
    final photoIndex = task.photos.indexWhere((p) => p.id == photoId);
    if (photoIndex != -1) {
      task.photos[photoIndex].recognitionResult = result;
      task.photos[photoIndex].isVerified = result.matchesPreset;
      _scheduleSave(taskId); // 🚀 优化：指定任务ID进行增量保存
    } else {}
  }

  /// 🔧 新增：更新批次识别统计
  void _updateBatchRecognitionStats(BatchInfo batch, String photoLabel) {
    // 更新批次的识别数量
    if (!batch.recognizedItems.contains(photoLabel)) {
      batch.recognizedItems.add(photoLabel);
      batch.recognizedQuantity = batch.recognizedItems.length;
      // 回滚：不在此处直接持久化和刷新UI，统一由业务流程末端控制
    }
  }

  void setTasks(List<TaskModel> tasks) {
    _tasks = tasks;
  }
}
