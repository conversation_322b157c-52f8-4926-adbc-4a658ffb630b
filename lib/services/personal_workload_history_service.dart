import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/worker_info_data.dart';
import '../models/task_model.dart';
import '../services/logging_service.dart';

/// 个人工作量历史记录数据模型
class PersonalWorkloadRecord {
  final String workerId;
  final String workerName;
  final String role;
  final String warehouse;
  final String group;
  final String taskId;
  final String taskTemplate;
  final double tonnage;
  final int palletCount;
  final DateTime completedAt;
  final Map<String, dynamic> taskDetails;

  PersonalWorkloadRecord({
    required this.workerId,
    required this.workerName,
    required this.role,
    required this.warehouse,
    required this.group,
    required this.taskId,
    required this.taskTemplate,
    required this.tonnage,
    required this.palletCount,
    required this.completedAt,
    required this.taskDetails,
  });

  Map<String, dynamic> toMap() {
    return {
      'workerId': workerId,
      'workerName': workerName,
      'role': role,
      'warehouse': warehouse,
      'group': group,
      'taskId': taskId,
      'taskTemplate': taskTemplate,
      'tonnage': tonnage,
      'palletCount': palletCount,
      'completedAt': completedAt.toIso8601String(),
      'taskDetails': taskDetails,
    };
  }

  factory PersonalWorkloadRecord.fromMap(Map<String, dynamic> map) {
    return PersonalWorkloadRecord(
      workerId: map['workerId'] ?? '',
      workerName: map['workerName'] ?? '',
      role: map['role'] ?? '',
      warehouse: map['warehouse'] ?? '',
      group: map['group'] ?? '',
      taskId: map['taskId'] ?? '',
      taskTemplate: map['taskTemplate'] ?? '',
      tonnage: (map['tonnage'] ?? 0.0).toDouble(),
      palletCount: map['palletCount'] ?? 0,
      completedAt: DateTime.parse(map['completedAt']),
      taskDetails: map['taskDetails'] ?? {},
    );
  }
}

/// 个人工作量历史服务
/// 负责保存、查询个人工作量历史记录，支持多维度统计
class PersonalWorkloadHistoryService {
  static const String _storageKey = 'personal_workload_history';
  static const String _tagName = 'PersonalWorkloadHistoryService';

  /// 保存任务完成后的个人工作量记录
  static Future<void> saveTaskCompletionWorkload({
    required TaskModel task,
    required List<WorkerInfo> participants,
  }) async {
    try {
      if (participants.isEmpty) {
        LoggingService.warning('No participants to save workload for task ${task.id}', tag: _tagName);
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      final existingRecords = await _loadAllRecords();

      // 计算每个人的工作量（每托1.5吨标准）
      final totalTonnage = task.quantity * 1.5; // 每托1.5吨（业务标准）
      final perPersonTonnage = totalTonnage / participants.length;
      final perPersonPallets = (task.quantity / participants.length).round();

      final newRecords = <PersonalWorkloadRecord>[];
      for (final worker in participants) {
        newRecords.add(PersonalWorkloadRecord(
          workerId: worker.id,
          workerName: worker.name,
          role: worker.role,
          warehouse: worker.warehouse,
          group: worker.group,
          taskId: task.id,
          taskTemplate: task.template,
          tonnage: perPersonTonnage,
          palletCount: perPersonPallets,
          completedAt: task.completedAt ?? DateTime.now(),
          taskDetails: {
            'taskType': task.template,
            'batchCount': task.batches.length,
            'photoCount': task.photos.length,
            'recognitionAccuracy': _calculateTaskAccuracy(task),
            'isCompleted': task.isCompleted,
          },
        ));
      }

      existingRecords.addAll(newRecords);
      await _saveAllRecords(existingRecords);

      LoggingService.info(
        'Saved workload records for ${participants.length} workers from task ${task.id}',
        tag: _tagName,
      );
    } catch (e) {
      LoggingService.error('Failed to save task completion workload: $e', tag: _tagName);
      rethrow;
    }
  }

  /// 获取指定工人的工作量历史记录
  static Future<List<PersonalWorkloadRecord>> getWorkerHistory({
    required String workerId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final allRecords = await _loadAllRecords();
      return allRecords.where((record) {
        if (record.workerId != workerId) return false;
        if (startDate != null && record.completedAt.isBefore(startDate)) return false;
        if (endDate != null && record.completedAt.isAfter(endDate)) return false;
        return true;
      }).toList()
        ..sort((a, b) => b.completedAt.compareTo(a.completedAt));
    } catch (e) {
      LoggingService.error('Failed to get worker history: $e', tag: _tagName);
      return [];
    }
  }

  /// 获取所有工人的每日工作量统计
  static Future<Map<String, Map<String, double>>> getDailyWorkloadStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final allRecords = await _loadAllRecords();
      final filteredRecords = allRecords.where((record) {
        if (startDate != null && record.completedAt.isBefore(startDate)) return false;
        if (endDate != null && record.completedAt.isAfter(endDate)) return false;
        return true;
      }).toList();

      final dailyStats = <String, Map<String, double>>{};

      for (final record in filteredRecords) {
        final dateKey = _formatDateKey(record.completedAt);
        final workerKey = record.workerId;

        dailyStats[dateKey] ??= {};
        dailyStats[dateKey]![workerKey] = (dailyStats[dateKey]![workerKey] ?? 0.0) + record.tonnage;
      }

      return dailyStats;
    } catch (e) {
      LoggingService.error('Failed to get daily workload stats: $e', tag: _tagName);
      return {};
    }
  }

  /// 获取个人工作量趋势数据（用于图表显示）
  static Future<Map<String, dynamic>> getPersonalWorkloadTrends({
    required String workerId,
    required String period, // 'daily', 'weekly', 'monthly'
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final records = await getWorkerHistory(
        workerId: workerId,
        startDate: startDate,
        endDate: endDate,
      );

      final trendData = <String, double>{};
      final taskCounts = <String, int>{};

      for (final record in records) {
        final key = _getPeriodKey(record.completedAt, period);
        trendData[key] = (trendData[key] ?? 0.0) + record.tonnage;
        taskCounts[key] = (taskCounts[key] ?? 0) + 1;
      }

      // 计算平均值和总计
      final totalTonnage = trendData.values.fold(0.0, (a, b) => a + b);
      final totalTasks = taskCounts.values.fold(0, (a, b) => a + b);
      final averageTonnagePerTask = totalTasks > 0 ? totalTonnage / totalTasks : 0.0;

      return {
        'trendData': trendData,
        'taskCounts': taskCounts,
        'totalTonnage': totalTonnage,
        'totalTasks': totalTasks,
        'averageTonnagePerTask': averageTonnagePerTask,
        'period': period,
      };
    } catch (e) {
      LoggingService.error('Failed to get personal workload trends: $e', tag: _tagName);
      return {};
    }
  }

  /// 获取团队工作量对比数据
  static Future<Map<String, dynamic>> getTeamWorkloadComparison({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final allRecords = await _loadAllRecords();
      final filteredRecords = allRecords.where((record) {
        if (startDate != null && record.completedAt.isBefore(startDate)) return false;
        if (endDate != null && record.completedAt.isAfter(endDate)) return false;
        return true;
      }).toList();

      final workerStats = <String, Map<String, dynamic>>{};

      for (final record in filteredRecords) {
        final workerId = record.workerId;
        workerStats[workerId] ??= {
          'workerName': record.workerName,
          'role': record.role,
          'warehouse': record.warehouse,
          'group': record.group,
          'totalTonnage': 0.0,
          'totalTasks': 0,
          'totalPallets': 0,
          'averageTonnagePerTask': 0.0,
        };

        workerStats[workerId]!['totalTonnage'] = 
          (workerStats[workerId]!['totalTonnage'] as double) + record.tonnage;
        workerStats[workerId]!['totalTasks'] = 
          (workerStats[workerId]!['totalTasks'] as int) + 1;
        workerStats[workerId]!['totalPallets'] = 
          (workerStats[workerId]!['totalPallets'] as int) + record.palletCount;
      }

      // 计算平均值
      for (final stats in workerStats.values) {
        final totalTonnage = stats['totalTonnage'] as double;
        final totalTasks = stats['totalTasks'] as int;
        stats['averageTonnagePerTask'] = totalTasks > 0 ? totalTonnage / totalTasks : 0.0;
      }

      return {
        'workerStats': workerStats,
        'recordCount': filteredRecords.length,
      };
    } catch (e) {
      LoggingService.error('Failed to get team workload comparison: $e', tag: _tagName);
      return {};
    }
  }

  /// 私有方法：加载所有记录
  static Future<List<PersonalWorkloadRecord>> _loadAllRecords() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = prefs.getString(_storageKey);
      
      if (recordsJson == null) {
        return [];
      }

      final recordsList = jsonDecode(recordsJson) as List;
      return recordsList.map((record) => PersonalWorkloadRecord.fromMap(record)).toList();
    } catch (e) {
      LoggingService.error('Failed to load workload records: $e', tag: _tagName);
      return [];
    }
  }

  /// 私有方法：保存所有记录
  static Future<void> _saveAllRecords(List<PersonalWorkloadRecord> records) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recordsJson = jsonEncode(records.map((r) => r.toMap()).toList());
      await prefs.setString(_storageKey, recordsJson);
    } catch (e) {
      LoggingService.error('Failed to save workload records: $e', tag: _tagName);
      rethrow;
    }
  }

  /// 私有方法：计算任务准确率
  static double _calculateTaskAccuracy(TaskModel task) {
    if (task.photos.isEmpty) return 0.0;
    final verifiedPhotos = task.photos.where((p) => p.isVerified).length;
    return verifiedPhotos / task.photos.length;
  }

  /// 私有方法：格式化日期键
  static String _formatDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 私有方法：根据周期获取键值
  static String _getPeriodKey(DateTime date, String period) {
    switch (period) {
      case 'daily':
        return _formatDateKey(date);
      case 'weekly':
        final weekStart = date.subtract(Duration(days: date.weekday - 1));
        return '${weekStart.year}-W${_getWeekOfYear(weekStart).toString().padLeft(2, '0')}';
      case 'monthly':
        return '${date.year}-${date.month.toString().padLeft(2, '0')}';
      default:
        return _formatDateKey(date);
    }
  }

  /// 私有方法：获取一年中的第几周
  static int _getWeekOfYear(DateTime date) {
    final firstDayOfYear = DateTime(date.year, 1, 1);
    final daysSinceFirstDay = date.difference(firstDayOfYear).inDays;
    return (daysSinceFirstDay / 7).floor() + 1;
  }

  /// 清除所有历史记录（仅用于测试）
  static Future<void> clearAllHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_storageKey);
      LoggingService.info('All workload history cleared', tag: _tagName);
    } catch (e) {
      LoggingService.error('Failed to clear workload history: $e', tag: _tagName);
      rethrow;
    }
  }
}