import 'dart:io';
import 'package:loadguard/services/smart_image_quality_assessor.dart';
import 'package:loadguard/services/adaptive_processing_strategy.dart';
import 'package:loadguard/services/enhanced_cache_service.dart';
import 'package:loadguard/services/async_upload_service.dart';
import 'package:loadguard/services/logging_service.dart';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';
import 'package:loadguard/models/task_model.dart';
import 'dart:math' as math;

/// 处理结果模型
class ProcessingResult {
  final bool success;
  final String photoId;
  final String originalPath;
  final String processedPath;
  final int processingTime;
  final String strategy;
  final String? uploadId;
  final String? error;
  final RecognitionResult? recognitionResult;
  final ImageQualityProfile? qualityProfile;

  ProcessingResult({
    required this.success,
    required this.photoId,
    required this.originalPath,
    required this.processedPath,
    required this.processingTime,
    required this.strategy,
    this.uploadId,
    this.error,
    this.recognitionResult,
    this.qualityProfile,
  });

  /// 获取处理摘要
  String get summary {
    if (!success) {
      return '处理失败: $error';
    }

    return '处理成功: $strategy策略, ${processingTime}ms';
  }
}

/// 批量照片处理请求
class BatchPhotoRequest {
  final String photoId;
  final String imagePath;
  final String taskId;
  final bool needRecognition;
  final String? presetProductCode;
  final String? presetBatchNumber;
  final Function(String, RecognitionResult)? onRecognitionSuccess;
  final Function(String, String)? onRecognitionFailure;

  BatchPhotoRequest({
    required this.photoId,
    required this.imagePath,
    required this.taskId,
    required this.needRecognition,
    this.presetProductCode,
    this.presetBatchNumber,
    this.onRecognitionSuccess,
    this.onRecognitionFailure,
  });
}

/// 集成处理服务
/// 整合所有优化功能，提供统一的高性能处理接口
class IntegratedProcessingService {
  static final IntegratedProcessingService _instance =
      IntegratedProcessingService._internal();
  factory IntegratedProcessingService() => _instance;
  IntegratedProcessingService._internal();

  final _qualityAssessor = SmartImageQualityAssessor();
  final _adaptiveStrategy = AdaptiveProcessingStrategy();
  final _fastTrackProcessor = FastTrackProcessor();
  final _cacheService = EnhancedCacheService();
  final _uploadService = AsyncUploadService();
  final _recognitionService = MLKitTextRecognitionService(); // 🔧 新增：MLKit服务

  bool _isInitialized = false;

  // 性能统计
  final Map<String, int> _processingStats = {
    'minimal': 0,
    'selective': 0,
    'standard': 0,
    'aggressive': 0,
    'fastTrack': 0,
    'cached': 0,
  };

  final Map<String, int> _timingStats = {
    'totalProcessed': 0,
    'totalTime': 0,
    'avgTime': 0,
    'fastestTime': 999999,
    'slowestTime': 0,
  };

  /// 初始化集成服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await Future.wait([
        _cacheService.initialize(),
        _uploadService.initialize(),
        _recognitionService.initialize(), // 🔧 初始化MLKit服务
      ]);

      _isInitialized = true;
      Log.i('集成处理服务初始化完成', tag: 'IntegratedProcessing');
    } catch (e) {
      Log.e('集成处理服务初始化失败: $e', tag: 'IntegratedProcessing');
    }
  }

  /// 智能处理照片
  /// 这是主要的处理入口点，会根据照片类型和质量自动选择最优处理策略
  Future<ProcessingResult> processPhotoIntelligently({
    required String photoId,
    required String imagePath,
    required String taskId,
    required bool needRecognition,
    String? presetProductCode,
    String? presetBatchNumber,
    List<BatchInfo>? allBatches, // 🔧 新增：支持混合任务的多批次信息
    Function(String, RecognitionResult)? onRecognitionSuccess,
    Function(String, String)? onRecognitionFailure,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final startTime = DateTime.now();

    try {
      Log.i('开始智能处理: $photoId (需识别: $needRecognition)',
          tag: 'IntegratedProcessing');

      if (needRecognition) {
        // 识别照片：使用完整的智能处理流程
        return await _processRecognitionPhoto(
          photoId: photoId,
          imagePath: imagePath,
          taskId: taskId,
          presetProductCode: presetProductCode,
          presetBatchNumber: presetBatchNumber,
          allBatches: allBatches, // 🔧 传递批次信息
          onRecognitionSuccess: onRecognitionSuccess,
          onRecognitionFailure: onRecognitionFailure,
          startTime: startTime,
        );
      } else {
        // 存证照片：使用快速通道
        return await _processArchivePhoto(
          photoId: photoId,
          imagePath: imagePath,
          taskId: taskId,
          startTime: startTime,
        );
      }
    } catch (e) {
      Log.e('智能处理失败: $photoId - $e', tag: 'IntegratedProcessing');
      return ProcessingResult(
        success: false,
        photoId: photoId,
        originalPath: imagePath,
        processedPath: imagePath,
        processingTime: DateTime.now().difference(startTime).inMilliseconds,
        strategy: 'error',
        error: e.toString(),
      );
    }
  }

  /// 处理识别照片
  Future<ProcessingResult> _processRecognitionPhoto({
    required String photoId,
    required String imagePath,
    required String taskId,
    String? presetProductCode,
    String? presetBatchNumber,
    List<BatchInfo>? allBatches, // 🔧 新增：支持多批次信息
    Function(String, RecognitionResult)? onRecognitionSuccess,
    Function(String, String)? onRecognitionFailure,
    required DateTime startTime,
  }) async {
    // 🔧 优化：跳过缓存检查，直接进行识别（提高速度）
    // 1. 检查识别结果缓存（可选，仅在特殊情况下启用）
    final bool skipCache = true; // 临时禁用缓存以提高性能
    RecognitionResult? cachedResult;

    if (!skipCache) {
      cachedResult = await _cacheService.getCachedRecognitionResult(
        imagePath,
        presetProductCode ?? '',
        presetBatchNumber ?? '',
      );
    }

    if (cachedResult != null) {
      Log.i('使用缓存的识别结果: $photoId', tag: 'IntegratedProcessing');

      _processingStats['cached'] = (_processingStats['cached'] ?? 0) + 1;

      // 🔧 确保缓存命中时也更新照片状态
      if (onRecognitionSuccess != null) {
        // 创建带有正确状态的缓存结果
        final updatedCachedResult = RecognitionResult(
          qrCode: cachedResult.qrCode,
          ocrText: cachedResult.ocrText,
          extractedProductCode: cachedResult.extractedProductCode,
          extractedBatchNumber: cachedResult.extractedBatchNumber,
          isQrOcrConsistent: cachedResult.isQrOcrConsistent,
          matchesPreset: cachedResult.matchesPreset,
          confidence: cachedResult.confidence,
          recognitionTime: DateTime.now(), // 🔧 使用当前时间
          boundingBox: cachedResult.boundingBox,
          batchMatches: cachedResult.batchMatches,
          status: cachedResult.matchesPreset
              ? RecognitionStatus.completed
              : RecognitionStatus.failed, // 🔧 确保状态正确
          errorMessage: cachedResult.errorMessage,
          metadata: {
            ...?cachedResult.metadata,
            'cached': true, // 🔧 标记为缓存结果
            'cacheHitTime': DateTime.now().toIso8601String(),
          },
        );

        onRecognitionSuccess(photoId, updatedCachedResult);
      }

      return ProcessingResult(
        success: cachedResult.matchesPreset,
        photoId: photoId,
        originalPath: imagePath,
        processedPath: imagePath,
        processingTime: DateTime.now().difference(startTime).inMilliseconds,
        strategy: 'cached',
        recognitionResult: cachedResult,
      );
    }

    // 2. 智能质量评估
    final qualityProfile =
        await SmartImageQualityAssessor.assessImageQuality(imagePath);
    Log.i(
        '图像质量评估: ${qualityProfile.qualityLevel}, 综合评分: ${qualityProfile.overallScore}',
        tag: 'IntegratedProcessing');

    // 3. 自适应处理策略选择
    final strategyName =
        qualityProfile.processingStrategy.toString().split('.').last;
    Log.i('选择处理策略: $strategyName', tag: 'IntegratedProcessing');

    // 4. 图像预处理
    String processedPath = imagePath;
    if (qualityProfile.qualityLevel == '较差' &&
        qualityProfile.overallScore < 70) {
      Log.i('开始图像预处理: $photoId', tag: 'IntegratedProcessing');

      try {
        processedPath =
            await _adaptiveStrategy.processImageIntelligently(imagePath);
        Log.i('图像预处理完成: $photoId', tag: 'IntegratedProcessing');

        // 缓存预处理结果
        if (!skipCache) {
          await _cacheService.cacheProcessedImage(
              imagePath, strategyName, processedPath);
        }
      } catch (e) {
        Log.w('图像预处理失败，使用原图: $photoId - $e', tag: 'IntegratedProcessing');
        processedPath = imagePath;
      }
    } else {
      Log.i('图像质量良好，跳过预处理: $photoId', tag: 'IntegratedProcessing');
    }

    // 6. 提交到优化上传服务进行识别
    // 🔧 如果是混合任务且没有预设值，使用新版多批次智能匹配流程
    Log.i(
        '【调试】混合任务分支判断: presetProductCode=$presetProductCode, presetBatchNumber=$presetBatchNumber, allBatches=${allBatches?.length}',
        tag: 'IntegratedProcessing');
    if (allBatches != null && allBatches.isNotEmpty) {
      Log.i(
          '【调试】allBatches详细内容: ${allBatches.map((b) => '${b.productCode}-${b.batchNumber}').join(', ')}',
          tag: 'IntegratedProcessing');
    }
    if (presetProductCode == null &&
        presetBatchNumber == null &&
        allBatches != null &&
        allBatches.isNotEmpty) {
      Log.i('【调试】进入混合任务智能匹配分支', tag: 'IntegratedProcessing');
      Log.i('【调试】当前调用堆栈: ${StackTrace.current}', tag: 'IntegratedProcessing');
      Log.i('使用新版多批次智能匹配流程: ${allBatches.length}个批次',
          tag: 'IntegratedProcessing');
      try {
        // 构建智能匹配配置
        final matchConfig = RecognitionMatchConfig(
          batches: allBatches,
          isMixedLoad: true,
        );
        Log.i(
            '【调试】构建的matchConfig: batches=${matchConfig.batches.length}, isMixedLoad=${matchConfig.isMixedLoad}',
            tag: 'IntegratedProcessing');
        // 调用新版多批次智能匹配
        final smartResult =
            await _recognitionService.processImageWithSmartMatching(
          imagePath: processedPath,
          matchConfig: matchConfig,
        );
        Log.i(
            '【调试】processImageWithSmartMatching调用完成，结果: matchesPreset=${smartResult.matchesPreset}',
            tag: 'IntegratedProcessing');
        if (onRecognitionSuccess != null) {
          onRecognitionSuccess(photoId, smartResult);
        }
        return ProcessingResult(
          success: smartResult.matchesPreset,
          photoId: photoId,
          originalPath: imagePath,
          processedPath: processedPath,
          processingTime: DateTime.now().difference(startTime).inMilliseconds,
          strategy: 'mixedTaskSmartMatching',
          recognitionResult: smartResult,
        );
      } catch (e) {
        Log.e('【调试】混合任务智能匹配异常: $e', tag: 'IntegratedProcessing');
        Log.e('【调试】异常堆栈: ${StackTrace.current}', tag: 'IntegratedProcessing');
        Log.e('混合任务智能匹配失败: $e', tag: 'IntegratedProcessing');
        if (onRecognitionFailure != null) {
          onRecognitionFailure(photoId, e.toString());
        }
        return ProcessingResult(
          success: false,
          photoId: photoId,
          originalPath: imagePath,
          processedPath: processedPath,
          processingTime: DateTime.now().difference(startTime).inMilliseconds,
          strategy: 'mixedTaskSmartMatching',
          error: e.toString(),
        );
      }
    } else {
      Log.i(
          '【调试】未进入混合任务分支，原因: presetProductCode=${presetProductCode != null}, presetBatchNumber=${presetBatchNumber != null}, allBatches=${allBatches != null && allBatches.isNotEmpty}',
          tag: 'IntegratedProcessing');
    }

    // 常规单批次识别流程
    final uploadId = await _uploadService.uploadPhotoAsync(
      photoId: photoId,
      imagePath: processedPath,
      taskId: taskId,
      presetProductCode: presetProductCode,
      presetBatchNumber: presetBatchNumber,
      needRecognition: true,
      onRecognitionSuccess: onRecognitionSuccess ?? (_, __) {},
      onRecognitionFailure: onRecognitionFailure ?? (_, __) {},
    );

    // 更新统计
    _processingStats[strategyName] = (_processingStats[strategyName] ?? 0) + 1;
    _updateTimingStats(DateTime.now().difference(startTime).inMilliseconds);

    Log.i('识别照片处理完成: $photoId, 上传ID: $uploadId', tag: 'IntegratedProcessing');

    return ProcessingResult(
      success: true,
      photoId: photoId,
      originalPath: imagePath,
      processedPath: processedPath,
      processingTime: DateTime.now().difference(startTime).inMilliseconds,
      strategy: strategyName,
      uploadId: uploadId,
      qualityProfile: qualityProfile,
    );
  }

  /// 处理存证照片
  Future<ProcessingResult> _processArchivePhoto({
    required String photoId,
    required String imagePath,
    required String taskId,
    required DateTime startTime,
  }) async {
    // 使用快速通道处理
    final processedPath = await _fastTrackProcessor.processFastTrack(imagePath);

    // 提交到优化上传服务
    final uploadId = await _uploadService.uploadPhotoAsync(
      photoId: photoId,
      imagePath: processedPath,
      taskId: taskId,
      needRecognition: false,
      onRecognitionSuccess: (_, __) {},
      onRecognitionFailure: (_, __) {},
    );

    // 更新统计
    _processingStats['fastTrack'] = (_processingStats['fastTrack'] ?? 0) + 1;
    _updateTimingStats(DateTime.now().difference(startTime).inMilliseconds);

    Log.i('存证照片处理完成: $photoId, 上传ID: $uploadId', tag: 'IntegratedProcessing');

    return ProcessingResult(
      success: true,
      photoId: photoId,
      originalPath: imagePath,
      processedPath: processedPath,
      processingTime: DateTime.now().difference(startTime).inMilliseconds,
      strategy: 'fastTrack',
      uploadId: uploadId,
    );
  }

  /// 批量处理照片
  Future<List<ProcessingResult>> processBatchPhotos(
      List<BatchPhotoRequest> requests) async {
    Log.i('开始批量处理: ${requests.length}张照片', tag: 'IntegratedProcessing');

    // 并行处理所有照片
    final futures = requests
        .map((request) => processPhotoIntelligently(
              photoId: request.photoId,
              imagePath: request.imagePath,
              taskId: request.taskId,
              needRecognition: request.needRecognition,
              presetProductCode: request.presetProductCode,
              presetBatchNumber: request.presetBatchNumber,
              onRecognitionSuccess: request.onRecognitionSuccess,
              onRecognitionFailure: request.onRecognitionFailure,
            ))
        .toList();

    final results = await Future.wait(futures);

    Log.i('批量处理完成: ${results.length}张照片', tag: 'IntegratedProcessing');

    return results;
  }

  /// 更新时间统计
  void _updateTimingStats(int processingTime) {
    _timingStats['totalProcessed'] = (_timingStats['totalProcessed'] ?? 0) + 1;
    _timingStats['totalTime'] =
        (_timingStats['totalTime'] ?? 0) + processingTime;
    _timingStats['avgTime'] =
        (_timingStats['totalTime']! / _timingStats['totalProcessed']!).round();
    _timingStats['fastestTime'] =
        math.min(_timingStats['fastestTime']!, processingTime);
    _timingStats['slowestTime'] =
        math.max(_timingStats['slowestTime']!, processingTime);
  }

  /// 获取性能统计
  Map<String, dynamic> getPerformanceStats() {
    final cacheStats = _cacheService.getCacheStats();
    final queueStats = _uploadService.getQueueStatus();

    return {
      'processing': _processingStats,
      'timing': _timingStats,
      'cache': cacheStats,
      'queue': queueStats,
    };
  }

  /// 获取上传事件流
  Stream<UploadEvent> get uploadEventStream => _uploadService.eventStream;

  /// 获取上传进度
  UploadProgress? getUploadProgress(String uploadId) {
    return _uploadService.getProgress(uploadId);
  }

  /// 智能缓存清理
  Future<void> performSmartCleanup() async {
    Log.i('开始智能缓存清理', tag: 'IntegratedProcessing');

    await Future.wait([
      _cacheService.smartCleanup(),
    ]);

    _uploadService.cleanupCompletedTasks();

    Log.i('智能缓存清理完成', tag: 'IntegratedProcessing');
  }

  /// 重置统计信息
  void resetStats() {
    _processingStats.clear();
    _timingStats.clear();
    _timingStats.addAll({
      'totalProcessed': 0,
      'totalTime': 0,
      'avgTime': 0,
      'fastestTime': 999999,
      'slowestTime': 0,
    });

    Log.i('统计信息已重置', tag: 'IntegratedProcessing');
  }

  /// 释放资源
  void dispose() {
    _cacheService.dispose();
    _uploadService.dispose();
    Log.i('集成处理服务已释放资源', tag: 'IntegratedProcessing');
  }
}
