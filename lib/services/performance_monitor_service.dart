import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:loadguard/services/async_upload_service.dart';
import 'package:loadguard/services/logging_service.dart';
import 'package:loadguard/services/memory_optimization_service.dart';

/// 📊 性能监控服务 - 建立性能基线
///
/// 实时监控应用性能指标，提供数据驱动的优化建议
/// 建立性能基线，追踪优化效果
class PerformanceMonitorService {
  static final PerformanceMonitorService _instance =
      PerformanceMonitorService._internal();
  factory PerformanceMonitorService() => _instance;
  PerformanceMonitorService._internal();

  // 依赖服务
  final _uploadService = AsyncUploadService();
  final _memoryService = MemoryOptimizationService();

  // 性能指标
  final Map<String, List<PerformanceMetric>> _metrics = {};
  final Map<String, double> _baselines = {};

  Timer? _monitorTimer;
  bool _isMonitoring = false;
  bool _isInitialized = false;

  // 监控配置
  static const Duration _monitorInterval = Duration(seconds: 10);
  static const int _maxMetricHistory = 100;

  /// 初始化性能监控
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _loadBaselines();
    _isInitialized = true;

    LoggingService.info('性能监控服务初始化完成', tag: 'PerformanceMonitor');
  }

  /// 🚀 开始性能监控
  Future<void> startMonitoring() async {
    if (!_isInitialized) await initialize();
    if (_isMonitoring) return;

    _isMonitoring = true;

    // 启动定时监控
    _monitorTimer = Timer.periodic(_monitorInterval, (_) => _collectMetrics());

    LoggingService.info('性能监控已启动，间隔: ${_monitorInterval.inSeconds}秒',
        tag: 'PerformanceMonitor');
  }

  /// ⏸️ 停止性能监控
  void stopMonitoring() {
    _monitorTimer?.cancel();
    _isMonitoring = false;

    LoggingService.info('性能监控已停止', tag: 'PerformanceMonitor');
  }

  /// 📊 收集性能指标
  Future<void> _collectMetrics() async {
    final timestamp = DateTime.now();

    try {
      // 收集各类性能指标
      await Future.wait([
        _collectMemoryMetrics(timestamp),
        _collectNetworkMetrics(timestamp),
        _collectUploadMetrics(timestamp),
        _collectSystemMetrics(timestamp),
      ]);
    } catch (e) {
      LoggingService.error('性能指标收集失败: $e', tag: 'PerformanceMonitor', error: e);
    }
  }

  /// 🧠 收集内存性能指标
  Future<void> _collectMemoryMetrics(DateTime timestamp) async {
    try {
      final memoryStats = _memoryService.getMemoryStats();

      _addMetric(
          'memory_usage',
          PerformanceMetric(
            name: 'memory_usage',
            value:
                double.parse(memoryStats['memoryUsagePercentage'].toString()),
            unit: '%',
            timestamp: timestamp,
            category: MetricCategory.memory,
          ));

      _addMetric(
          'cached_images',
          PerformanceMetric(
            name: 'cached_images',
            value: double.parse(memoryStats['cachedImages'].toString()),
            unit: 'count',
            timestamp: timestamp,
            category: MetricCategory.memory,
          ));
    } catch (e) {
      LoggingService.warning('内存指标收集失败: $e', tag: 'PerformanceMonitor');
    }
  }

  /// 🌐 收集网络性能指标
  Future<void> _collectNetworkMetrics(DateTime timestamp) async {
    try {
      // Simulated network metrics since connection service is not available
      final simulatedConnectionStats = {
        'totalConnections': 5,
        'activeConnections': 3,
        'efficiency': 85.0,
      };

      _addMetric(
          'active_connections',
          PerformanceMetric(
            name: 'active_connections',
            value: double.parse(
                simulatedConnectionStats['totalConnections'].toString()),
            unit: 'count',
            timestamp: timestamp,
            category: MetricCategory.network,
          ));

      _addMetric(
          'connection_efficiency',
          PerformanceMetric(
            name: 'connection_efficiency',
            value: simulatedConnectionStats['efficiency'] as double,
            unit: '%',
            timestamp: timestamp,
            category: MetricCategory.network,
          ));
    } catch (e) {
      LoggingService.warning('网络指标收集失败: $e', tag: 'PerformanceMonitor');
    }
  }

  /// 📤 收集上传性能指标
  Future<void> _collectUploadMetrics(DateTime timestamp) async {
    try {
      final activeUploads = _uploadService.getActiveUploads();

      _addMetric(
          'active_uploads',
          PerformanceMetric(
            name: 'active_uploads',
            value: activeUploads.length.toDouble(),
            unit: 'count',
            timestamp: timestamp,
            category: MetricCategory.upload,
          ));

      // 计算平均上传进度
      final avgProgress = activeUploads.isEmpty
          ? 100.0
          : activeUploads.map((u) => u.progress).reduce((a, b) => a + b) /
              activeUploads.length *
              100;

      _addMetric(
          'upload_progress',
          PerformanceMetric(
            name: 'upload_progress',
            value: avgProgress,
            unit: '%',
            timestamp: timestamp,
            category: MetricCategory.upload,
          ));
    } catch (e) {
      LoggingService.warning('上传指标收集失败: $e', tag: 'PerformanceMonitor');
    }
  }

  /// 🖥️ 收集系统性能指标
  Future<void> _collectSystemMetrics(DateTime timestamp) async {
    try {
      // CPU使用率（模拟）
      final cpuUsage = _simulateCpuUsage();
      _addMetric(
          'cpu_usage',
          PerformanceMetric(
            name: 'cpu_usage',
            value: cpuUsage,
            unit: '%',
            timestamp: timestamp,
            category: MetricCategory.system,
          ));

      // 存储空间
      final diskSpace = await _getDiskSpace();
      _addMetric(
          'disk_usage',
          PerformanceMetric(
            name: 'disk_usage',
            value: diskSpace,
            unit: '%',
            timestamp: timestamp,
            category: MetricCategory.system,
          ));
    } catch (e) {
      LoggingService.warning('系统指标收集失败: $e', tag: 'PerformanceMonitor');
    }
  }

  /// 📊 添加性能指标
  void _addMetric(String metricName, PerformanceMetric metric) {
    _metrics.putIfAbsent(metricName, () => []).add(metric);

    // 保持历史记录在限制范围内
    if (_metrics[metricName]!.length > _maxMetricHistory) {
      _metrics[metricName]!.removeAt(0);
    }

    // 检查性能告警
    _checkPerformanceAlert(metric);
  }

  /// ⚠️ 检查性能告警
  void _checkPerformanceAlert(PerformanceMetric metric) {
    final baseline = _baselines[metric.name];
    if (baseline == null) return;

    // 定义告警阈值
    double alertThreshold = baseline * 1.5; // 超过基线50%时告警

    if (metric.name == 'memory_usage' && metric.value > 80.0) {
      _triggerAlert('内存使用率过高', '当前: ${metric.value.toStringAsFixed(1)}%');
    } else if (metric.name == 'active_uploads' && metric.value > 10) {
      _triggerAlert('上传队列积压', '当前队列: ${metric.value.toInt()}个任务');
    } else if (metric.value > alertThreshold) {
      _triggerAlert('性能指标异常',
          '${metric.name}: ${metric.value.toStringAsFixed(2)}${metric.unit} (基线: ${baseline.toStringAsFixed(2)}${metric.unit})');
    }
  }

  /// 🚨 触发性能告警
  void _triggerAlert(String title, String message) {
    LoggingService.warning('性能告警: $title - $message', tag: 'PerformanceAlert');
    // TODO: 可以集成到UI通知系统
  }

  /// 📊 计算连接效率
  double _calculateConnectionEfficiency(Map<String, dynamic> stats) {
    final totalConnections = stats['totalConnections'] as int;
    final maxConnections = stats['maxConnections'] as int;

    if (maxConnections == 0) return 0.0;
    return (totalConnections / maxConnections) * 100;
  }

  /// 💻 模拟CPU使用率
  double _simulateCpuUsage() {
    // 基于当前活动生成模拟的CPU使用率
    final activeUploads = _uploadService.getActiveUploads().length;
    final baseUsage = 20.0 + (activeUploads * 10.0);
    final noise = (math.Random().nextDouble() - 0.5) * 10;

    return (baseUsage + noise).clamp(0.0, 100.0);
  }

  /// 💾 获取磁盘使用率
  Future<double> _getDiskSpace() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        // 移动平台的存储空间检查
        return 50.0; // 模拟值
      } else {
        // 桌面平台可以使用更精确的方法
        return 45.0; // 模拟值
      }
    } catch (e) {
      return 50.0; // 默认值
    }
  }

  /// 📈 获取性能趋势
  PerformanceTrend getPerformanceTrend(
    String metricName, {
    Duration? period,
  }) {
    final metrics = _metrics[metricName];
    if (metrics == null || metrics.isEmpty) {
      return PerformanceTrend.empty(metricName);
    }

    // 筛选时间范围内的指标
    final now = DateTime.now();
    final startTime =
        period != null ? now.subtract(period) : metrics.first.timestamp;

    final filteredMetrics =
        metrics.where((m) => m.timestamp.isAfter(startTime)).toList();

    if (filteredMetrics.isEmpty) {
      return PerformanceTrend.empty(metricName);
    }

    // 计算趋势统计
    final values = filteredMetrics.map((m) => m.value).toList();
    final average = values.reduce((a, b) => a + b) / values.length;
    final minimum = values.reduce(math.min);
    final maximum = values.reduce(math.max);

    // 计算趋势方向
    final trend = _calculateTrend(filteredMetrics);

    return PerformanceTrend(
      metricName: metricName,
      average: average,
      minimum: minimum,
      maximum: maximum,
      trend: trend,
      dataPoints: filteredMetrics.length,
      period: period ?? now.difference(metrics.first.timestamp),
    );
  }

  /// 📈 计算趋势方向
  TrendDirection _calculateTrend(List<PerformanceMetric> metrics) {
    if (metrics.length < 2) return TrendDirection.stable;

    final first = metrics.first.value;
    final last = metrics.last.value;
    final diff = (last - first) / first;

    if (diff > 0.1) return TrendDirection.increasing;
    if (diff < -0.1) return TrendDirection.decreasing;
    return TrendDirection.stable;
  }

  /// 💾 保存性能基线
  Future<void> saveBaseline(String metricName, double value) async {
    _baselines[metricName] = value;
    await _saveBaselines();

    LoggingService.info('保存性能基线: $metricName = $value',
        tag: 'PerformanceMonitor');
  }

  /// 📊 自动建立基线
  Future<void> establishBaselines() async {
    for (final entry in _metrics.entries) {
      final metricName = entry.key;
      final metrics = entry.value;

      if (metrics.length >= 10) {
        // 使用过去的平均值作为基线
        final average = metrics.map((m) => m.value).reduce((a, b) => a + b) /
            metrics.length;

        await saveBaseline(metricName, average);
      }
    }

    LoggingService.info('自动建立了 ${_baselines.length} 个性能基线',
        tag: 'PerformanceMonitor');
  }

  /// 📁 加载基线数据
  Future<void> _loadBaselines() async {
    // TODO: 从本地存储加载基线数据
    // 这里使用默认基线
    _baselines.addAll({
      'memory_usage': 60.0,
      'cached_images': 15.0,
      'active_connections': 3.0,
      'connection_efficiency': 30.0,
      'active_uploads': 2.0,
      'upload_progress': 85.0,
      'cpu_usage': 25.0,
      'disk_usage': 50.0,
    });
  }

  /// 💾 保存基线数据
  Future<void> _saveBaselines() async {
    // TODO: 保存基线数据到本地存储
  }

  /// 📊 生成性能报告
  PerformanceReport generateReport({Duration? period}) {
    final report = PerformanceReport(
      generatedAt: DateTime.now(),
      period: period ?? const Duration(hours: 1),
    );

    // 为每个指标生成趋势
    for (final metricName in _metrics.keys) {
      final trend = getPerformanceTrend(metricName, period: period);
      report.trends[metricName] = trend;
    }

    return report;
  }

  /// 📊 获取实时性能快照
  Map<String, dynamic> getPerformanceSnapshot() {
    final snapshot = <String, dynamic>{};

    for (final entry in _metrics.entries) {
      final metricName = entry.key;
      final metrics = entry.value;

      if (metrics.isNotEmpty) {
        final latest = metrics.last;
        snapshot[metricName] = {
          'value': latest.value,
          'unit': latest.unit,
          'timestamp': latest.timestamp.toIso8601String(),
          'baseline': _baselines[metricName],
        };
      }
    }

    return snapshot;
  }

  /// 🧹 销毁服务
  void dispose() {
    stopMonitoring();
    _metrics.clear();
    _baselines.clear();

    LoggingService.info('性能监控服务已清理', tag: 'PerformanceMonitor');
  }
}

/// 📊 性能指标
class PerformanceMetric {
  final String name;
  final double value;
  final String unit;
  final DateTime timestamp;
  final MetricCategory category;

  PerformanceMetric({
    required this.name,
    required this.value,
    required this.unit,
    required this.timestamp,
    required this.category,
  });
}

/// 📊 指标分类
enum MetricCategory {
  memory, // 内存相关
  network, // 网络相关
  upload, // 上传相关
  system, // 系统相关
}

/// 📈 性能趋势
class PerformanceTrend {
  final String metricName;
  final double average;
  final double minimum;
  final double maximum;
  final TrendDirection trend;
  final int dataPoints;
  final Duration period;

  PerformanceTrend({
    required this.metricName,
    required this.average,
    required this.minimum,
    required this.maximum,
    required this.trend,
    required this.dataPoints,
    required this.period,
  });

  factory PerformanceTrend.empty(String metricName) {
    return PerformanceTrend(
      metricName: metricName,
      average: 0.0,
      minimum: 0.0,
      maximum: 0.0,
      trend: TrendDirection.stable,
      dataPoints: 0,
      period: Duration.zero,
    );
  }
}

/// 📈 趋势方向
enum TrendDirection {
  increasing, // 上升
  decreasing, // 下降
  stable, // 稳定
}

/// 📊 性能报告
class PerformanceReport {
  final DateTime generatedAt;
  final Duration period;
  final Map<String, PerformanceTrend> trends = {};

  PerformanceReport({
    required this.generatedAt,
    required this.period,
  });
}
