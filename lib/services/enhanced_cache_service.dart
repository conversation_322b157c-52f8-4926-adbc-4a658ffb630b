import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;
import 'package:loadguard/services/smart_image_quality_assessor.dart';
import 'package:loadguard/services/logging_service.dart';
import 'package:loadguard/models/task_model.dart';

/// 增强缓存服务
/// 智能内存管理、缓存命中率监控、自动清理
class EnhancedCacheService {
  static final EnhancedCacheService _instance =
      EnhancedCacheService._internal();
  factory EnhancedCacheService() => _instance;
  EnhancedCacheService._internal();

  bool _isInitialized = false;
  late Directory _cacheDir;
  late Directory _qualityProfileDir;
  late Directory _processedImageDir;
  late Directory _recognitionResultDir;

  // 内存缓存
  final Map<String, ImageQualityProfile> _qualityProfileCache = {};
  final Map<String, RecognitionResult> _recognitionResultCache = {};
  final Map<String, String> _processedImageCache = {};

  // 缓存统计
  final Map<String, int> _cacheStats = {
    'qualityHits': 0,
    'qualityMisses': 0,
    'recognitionHits': 0,
    'recognitionMisses': 0,
    'processedImageHits': 0,
    'processedImageMisses': 0,
  };

  // 缓存配置
  static const int _maxMemoryCacheSize = 100; // 最大内存缓存数量
  static const int _maxDiskCacheSize = 500; // 最大磁盘缓存数量
  static const Duration _cacheExpiration = Duration(hours: 24); // 缓存过期时间

  /// 初始化缓存服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 创建缓存目录
      final appDir = Directory('/tmp/loadguard_cache'); // 临时目录
      await appDir.create(recursive: true);

      _cacheDir = appDir;
      _qualityProfileDir =
          Directory(path.join(_cacheDir.path, 'quality_profiles'));
      _processedImageDir =
          Directory(path.join(_cacheDir.path, 'processed_images'));
      _recognitionResultDir =
          Directory(path.join(_cacheDir.path, 'recognition_results'));

      await Future.wait([
        _qualityProfileDir.create(recursive: true),
        _processedImageDir.create(recursive: true),
        _recognitionResultDir.create(recursive: true),
      ]);

      _isInitialized = true;
      Log.i('增强缓存服务初始化完成', tag: 'EnhancedCache');
    } catch (e) {
      Log.e('增强缓存服务初始化失败: $e', tag: 'EnhancedCache');
    }
  }

  /// 缓存图像质量档案
  Future<void> cacheQualityProfile(
      String imagePath, ImageQualityProfile profile) async {
    if (!_isInitialized) await initialize();

    try {
      final key = _generateCacheKey(imagePath);

      // 内存缓存
      _qualityProfileCache[key] = profile;
      _maintainMemoryCacheSize(_qualityProfileCache);

      // 磁盘缓存
      final cacheFile = File(path.join(_qualityProfileDir.path, '$key.json'));
      final cacheData = {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'profile': _qualityProfileToJson(profile),
      };
      await cacheFile.writeAsString(jsonEncode(cacheData));

      Log.d('质量档案已缓存: $key', tag: 'EnhancedCache');
    } catch (e) {
      Log.e('质量档案缓存失败: $e', tag: 'EnhancedCache');
    }
  }

  /// 获取缓存的图像质量档案
  Future<ImageQualityProfile?> getCachedQualityProfile(String imagePath) async {
    if (!_isInitialized) await initialize();

    final key = _generateCacheKey(imagePath);

    // 检查内存缓存
    if (_qualityProfileCache.containsKey(key)) {
      _cacheStats['qualityHits'] = (_cacheStats['qualityHits'] ?? 0) + 1;
      Log.d('质量档案内存缓存命中: $key', tag: 'EnhancedCache');
      return _qualityProfileCache[key];
    }

    // 检查磁盘缓存
    try {
      final cacheFile = File(path.join(_qualityProfileDir.path, '$key.json'));
      if (await cacheFile.exists()) {
        final cacheData = jsonDecode(await cacheFile.readAsString());
        final timestamp =
            DateTime.fromMillisecondsSinceEpoch(cacheData['timestamp']);

        // 检查是否过期
        if (DateTime.now().difference(timestamp) < _cacheExpiration) {
          final profile = _qualityProfileFromJson(cacheData['profile']);

          // 加载到内存缓存
          _qualityProfileCache[key] = profile;
          _maintainMemoryCacheSize(_qualityProfileCache);

          _cacheStats['qualityHits'] = (_cacheStats['qualityHits'] ?? 0) + 1;
          Log.d('质量档案磁盘缓存命中: $key', tag: 'EnhancedCache');
          return profile;
        } else {
          // 过期删除
          await cacheFile.delete();
        }
      }
    } catch (e) {
      Log.e('质量档案缓存读取失败: $e', tag: 'EnhancedCache');
    }

    _cacheStats['qualityMisses'] = (_cacheStats['qualityMisses'] ?? 0) + 1;
    return null;
  }

  /// 缓存识别结果
  Future<void> cacheRecognitionResult(String imagePath, String productCode,
      String batchNumber, RecognitionResult result) async {
    if (!_isInitialized) await initialize();

    try {
      final key =
          _generateRecognitionCacheKey(imagePath, productCode, batchNumber);

      // 内存缓存
      _recognitionResultCache[key] = result;
      _maintainMemoryCacheSize(_recognitionResultCache);

      // 磁盘缓存
      final cacheFile =
          File(path.join(_recognitionResultDir.path, '$key.json'));
      final cacheData = {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'result': _recognitionResultToJson(result),
      };
      await cacheFile.writeAsString(jsonEncode(cacheData));

      Log.d('识别结果已缓存: $key', tag: 'EnhancedCache');
    } catch (e) {
      Log.e('识别结果缓存失败: $e', tag: 'EnhancedCache');
    }
  }

  /// 获取缓存的识别结果
  Future<RecognitionResult?> getCachedRecognitionResult(
      String imagePath, String productCode, String batchNumber) async {
    if (!_isInitialized) await initialize();

    final key =
        _generateRecognitionCacheKey(imagePath, productCode, batchNumber);

    // 检查内存缓存
    if (_recognitionResultCache.containsKey(key)) {
      _cacheStats['recognitionHits'] =
          (_cacheStats['recognitionHits'] ?? 0) + 1;
      Log.d('识别结果内存缓存命中: $key', tag: 'EnhancedCache');
      return _recognitionResultCache[key];
    }

    // 检查磁盘缓存
    try {
      final cacheFile =
          File(path.join(_recognitionResultDir.path, '$key.json'));
      if (await cacheFile.exists()) {
        final cacheData = jsonDecode(await cacheFile.readAsString());
        final timestamp =
            DateTime.fromMillisecondsSinceEpoch(cacheData['timestamp']);

        // 检查是否过期
        if (DateTime.now().difference(timestamp) < _cacheExpiration) {
          final result = _recognitionResultFromJson(cacheData['result']);

          // 加载到内存缓存
          _recognitionResultCache[key] = result;
          _maintainMemoryCacheSize(_recognitionResultCache);

          _cacheStats['recognitionHits'] =
              (_cacheStats['recognitionHits'] ?? 0) + 1;
          Log.d('识别结果磁盘缓存命中: $key', tag: 'EnhancedCache');
          return result;
        } else {
          // 过期删除
          await cacheFile.delete();
        }
      }
    } catch (e) {
      Log.e('识别结果缓存读取失败: $e', tag: 'EnhancedCache');
    }

    _cacheStats['recognitionMisses'] =
        (_cacheStats['recognitionMisses'] ?? 0) + 1;
    return null;
  }

  /// 缓存预处理图像
  Future<void> cacheProcessedImage(
      String originalPath, String strategy, String processedPath) async {
    if (!_isInitialized) await initialize();

    try {
      final key = _generateProcessedImageCacheKey(originalPath, strategy);

      // 内存缓存
      _processedImageCache[key] = processedPath;
      _maintainMemoryCacheSize(_processedImageCache);

      // 复制文件到缓存目录
      final originalFile = File(processedPath);
      if (await originalFile.exists()) {
        final cachedFile = File(path.join(_processedImageDir.path, '$key.jpg'));
        await originalFile.copy(cachedFile.path);

        Log.d('预处理图像已缓存: $key', tag: 'EnhancedCache');
      }
    } catch (e) {
      Log.e('预处理图像缓存失败: $e', tag: 'EnhancedCache');
    }
  }

  /// 获取缓存的预处理图像
  Future<String?> getCachedProcessedImage(
      String originalPath, String strategy) async {
    if (!_isInitialized) await initialize();

    final key = _generateProcessedImageCacheKey(originalPath, strategy);

    // 检查内存缓存
    if (_processedImageCache.containsKey(key)) {
      final cachedPath = _processedImageCache[key]!;
      if (await File(cachedPath).exists()) {
        _cacheStats['processedImageHits'] =
            (_cacheStats['processedImageHits'] ?? 0) + 1;
        Log.d('预处理图像内存缓存命中: $key', tag: 'EnhancedCache');
        return cachedPath;
      }
    }

    // 检查磁盘缓存
    try {
      final cachedFile = File(path.join(_processedImageDir.path, '$key.jpg'));
      if (await cachedFile.exists()) {
        final cachedPath = cachedFile.path;

        // 加载到内存缓存
        _processedImageCache[key] = cachedPath;
        _maintainMemoryCacheSize(_processedImageCache);

        _cacheStats['processedImageHits'] =
            (_cacheStats['processedImageHits'] ?? 0) + 1;
        Log.d('预处理图像磁盘缓存命中: $key', tag: 'EnhancedCache');
        return cachedPath;
      }
    } catch (e) {
      Log.e('预处理图像缓存读取失败: $e', tag: 'EnhancedCache');
    }

    _cacheStats['processedImageMisses'] =
        (_cacheStats['processedImageMisses'] ?? 0) + 1;
    return null;
  }

  /// 智能缓存清理
  Future<void> smartCleanup() async {
    if (!_isInitialized) return;

    try {
      Log.i('开始智能缓存清理', tag: 'EnhancedCache');

      // 清理过期的磁盘缓存
      await Future.wait([
        _cleanupExpiredFiles(_qualityProfileDir),
        _cleanupExpiredFiles(_recognitionResultDir),
        _cleanupExpiredFiles(_processedImageDir),
      ]);

      // 清理内存缓存
      _qualityProfileCache.clear();
      _recognitionResultCache.clear();
      _processedImageCache.clear();

      Log.i('智能缓存清理完成', tag: 'EnhancedCache');
    } catch (e) {
      Log.e('智能缓存清理失败: $e', tag: 'EnhancedCache');
    }
  }

  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    final totalHits = (_cacheStats['qualityHits'] ?? 0) +
        (_cacheStats['recognitionHits'] ?? 0) +
        (_cacheStats['processedImageHits'] ?? 0);

    final totalMisses = (_cacheStats['qualityMisses'] ?? 0) +
        (_cacheStats['recognitionMisses'] ?? 0) +
        (_cacheStats['processedImageMisses'] ?? 0);

    final totalRequests = totalHits + totalMisses;
    final hitRate = totalRequests > 0
        ? (totalHits / totalRequests * 100).toStringAsFixed(1)
        : '0.0';

    return {
      'hitRate': '$hitRate%',
      'totalHits': totalHits,
      'totalMisses': totalMisses,
      'memoryCache': {
        'qualityProfiles': _qualityProfileCache.length,
        'recognitionResults': _recognitionResultCache.length,
        'processedImages': _processedImageCache.length,
      },
      'detailed': _cacheStats,
    };
  }

  /// 释放资源
  void dispose() {
    _qualityProfileCache.clear();
    _recognitionResultCache.clear();
    _processedImageCache.clear();
    _cacheStats.clear();
    Log.i('增强缓存服务已释放资源', tag: 'EnhancedCache');
  }

  // 私有方法

  /// 生成缓存键
  String _generateCacheKey(String imagePath) {
    return path.basename(imagePath).replaceAll(RegExp(r'[^a-zA-Z0-9]'), '_');
  }

  /// 生成识别结果缓存键
  String _generateRecognitionCacheKey(
      String imagePath, String productCode, String batchNumber) {
    final baseKey = _generateCacheKey(imagePath);
    final productKey = productCode.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '_');
    final batchKey = batchNumber.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '_');
    return '${baseKey}_${productKey}_${batchKey}';
  }

  /// 生成预处理图像缓存键
  String _generateProcessedImageCacheKey(String originalPath, String strategy) {
    final baseKey = _generateCacheKey(originalPath);
    return '${baseKey}_$strategy';
  }

  /// 维护内存缓存大小
  void _maintainMemoryCacheSize<T>(Map<String, T> cache) {
    if (cache.length > _maxMemoryCacheSize) {
      final keysToRemove =
          cache.keys.take(cache.length - _maxMemoryCacheSize).toList();
      for (final key in keysToRemove) {
        cache.remove(key);
      }
    }
  }

  /// 清理过期文件
  Future<void> _cleanupExpiredFiles(Directory directory) async {
    try {
      final files = await directory.list().toList();
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          if (DateTime.now().difference(stat.modified) > _cacheExpiration) {
            await file.delete();
          }
        }
      }
    } catch (e) {
      Log.e('清理过期文件失败: $e', tag: 'EnhancedCache');
    }
  }

  /// 质量档案序列化
  Map<String, dynamic> _qualityProfileToJson(ImageQualityProfile profile) {
    return {
      'overallScore': profile.overallScore,
      'sharpnessScore': profile.sharpnessScore,
      'exposureScore': profile.exposureScore,
      'contrastScore': profile.contrastScore,
      'noiseScore': profile.noiseScore,
      'reflectionScore': profile.reflectionScore,
      'features': profile.features,
      'processingStrategy': profile.processingStrategy.toString(),
      'assessmentTime': profile.assessmentTime,
    };
  }

  /// 质量档案反序列化
  ImageQualityProfile _qualityProfileFromJson(Map<String, dynamic> json) {
    return ImageQualityProfile(
      overallScore: json['overallScore'],
      sharpnessScore: json['sharpnessScore'],
      exposureScore: json['exposureScore'],
      contrastScore: json['contrastScore'],
      noiseScore: json['noiseScore'],
      reflectionScore: json['reflectionScore'],
      features: Map<String, dynamic>.from(json['features']),
      processingStrategy: ProcessingStrategy.values.firstWhere(
        (e) => e.toString() == json['processingStrategy'],
        orElse: () => ProcessingStrategy.standard,
      ),
      assessmentTime: json['assessmentTime'],
    );
  }

  /// 识别结果序列化
  Map<String, dynamic> _recognitionResultToJson(RecognitionResult result) {
    return {
      'ocrText': result.ocrText,
      'matchesPreset': result.matchesPreset,
      'isQrOcrConsistent': result.isQrOcrConsistent,
      'extractedProductCode': result.extractedProductCode,
      'extractedBatchNumber': result.extractedBatchNumber,
      'status': result.status.toString(),
      'confidence': result.confidence,
      'errorMessage': result.errorMessage,
      'metadata': result.metadata,
    };
  }

  /// 识别结果反序列化
  RecognitionResult _recognitionResultFromJson(Map<String, dynamic> json) {
    return RecognitionResult(
      ocrText: json['ocrText'],
      matchesPreset: json['matchesPreset'],
      isQrOcrConsistent: json['isQrOcrConsistent'],
      extractedProductCode: json['extractedProductCode'],
      extractedBatchNumber: json['extractedBatchNumber'],
      status: RecognitionStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
        orElse: () => RecognitionStatus.failed,
      ),
      confidence: json['confidence'],
      errorMessage: json['errorMessage'],
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'])
          : null,
    );
  }
}
