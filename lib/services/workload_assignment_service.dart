import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/worker_info_data.dart';
import '../models/task_model_extensions.dart';
import '../models/task_model.dart';
import '../services/logging_service.dart';

/// 工作量分配服务
/// 负责管理人员分配、工作量计算和数据持久化
class WorkloadAssignmentService {
  static const String _storageKey = 'workload_assignments';
  static const String _tagName = 'WorkloadAssignmentService';

  /// 保存工作量分配
  /// palletCount: 托盘数量
  /// selectedForklifts: 选中的叉车操作员和其他角色
  /// selectedWarehouses: 选中的仓管人员
  static Future<void> saveAssignment({
    required int palletCount,
    required List<WorkerInfo> selectedForklifts,
    required List<WorkerInfo> selectedWarehouses,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // 计算总吨数（每托按1.5吨计算）
      final totalTonnage = palletCount * 1.5;
      // 合并所有参与人员，去重
      final allWorkers = <String, WorkerInfo>{};
      for (final w in selectedForklifts) {
        allWorkers[w.id] = w;
      }
      for (final w in selectedWarehouses) {
        allWorkers[w.id] = w;
      }
      // 平均分配
      final perPersonTonnage =
          allWorkers.isNotEmpty ? totalTonnage / allWorkers.length : 0.0;
      final records = <WorkloadRecord>[];
      for (final worker in allWorkers.values) {
        records.add(WorkloadRecord(
          workerId: worker.id,
          workerName: worker.name,
          role: worker.role,
          warehouse: worker.warehouse,
          group: worker.group,
          allocatedTonnage: perPersonTonnage,
          assignedAt: DateTime.now(),
        ));
      }
      final assignment = WorkloadAssignment(
        records: records,
        totalTonnage: totalTonnage,
        palletCount: palletCount,
        assignedAt: DateTime.now(),
        assignedBy: 'user',
      );
      await prefs.setString(_storageKey, jsonEncode(assignment.toMap()));
      LoggingService.info(
        'Workload assignment saved: ${records.length} workers, $totalTonnage tons',
        tag: _tagName,
      );
    } catch (e) {
      LoggingService.error('Failed to save workload assignment: $e',
          tag: _tagName);
      rethrow;
    }
  }

  /// 加载当前工作量分配
  static Future<WorkloadAssignment?> loadCurrentAssignment() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final assignmentJson = prefs.getString(_storageKey);

      if (assignmentJson == null) {
        return null;
      }

      final assignmentMap = jsonDecode(assignmentJson) as Map<String, dynamic>;
      return WorkloadAssignment.fromMap(assignmentMap);
    } catch (e) {
      LoggingService.error('Failed to load workload assignment: $e',
          tag: _tagName);
      return null;
    }
  }

  /// 清除当前工作量分配
  static Future<void> clearCurrentAssignment() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_storageKey);

      LoggingService.info('Workload assignment cleared', tag: _tagName);
    } catch (e) {
      LoggingService.error('Failed to clear workload assignment: $e',
          tag: _tagName);
      rethrow;
    }
  }

  /// 将工作量分配应用到任务
  /// 将分配信息写入任务的recognitionMetadata中，供统计服务使用
  static void applyToTask(TaskModel task) {
    try {
      // 异步加载分配信息并应用到任务
      loadCurrentAssignment().then((assignment) {
        if (assignment != null) {
          // 将工作量分配信息写入任务metadata
          task.recognitionMetadata ??= {};
          task.recognitionMetadata!['workload'] = assignment.toMap();

          LoggingService.info(
            'Applied workload assignment to task ${task.id}: ${assignment.records.length} workers',
            tag: _tagName,
          );
        }
      }).catchError((e) {
        LoggingService.error('Failed to apply workload to task: $e',
            tag: _tagName);
      });
    } catch (e) {
      LoggingService.error('Failed to apply workload to task: $e',
          tag: _tagName);
    }
  }

  /// 获取指定任务的工作量分配
  static WorkloadAssignment? getTaskWorkload(TaskModel task) {
    try {
      final workloadData = task.recognitionMetadata?['workload'];
      if (workloadData != null) {
        return WorkloadAssignment.fromMap(workloadData as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      LoggingService.error('Failed to get task workload: $e', tag: _tagName);
      return null;
    }
  }

  /// 标记工作量记录为完成
  static Future<void> markRecordCompleted(
      TaskModel task, String workerId) async {
    try {
      final workload = getTaskWorkload(task);
      if (workload == null) return;

      // 找到对应的记录并标记为完成
      final updatedRecords = workload.records.map((record) {
        if (record.workerId == workerId) {
          return record.markCompleted();
        }
        return record;
      }).toList();

      // 创建更新后的分配对象
      final updatedWorkload = WorkloadAssignment(
        records: updatedRecords,
        totalTonnage: workload.totalTonnage,
        palletCount: workload.palletCount,
        assignedAt: workload.assignedAt,
        assignedBy: workload.assignedBy,
      );

      // 更新任务metadata
      task.recognitionMetadata ??= {};
      task.recognitionMetadata!['workload'] = updatedWorkload.toMap();

      LoggingService.info(
          'Marked workload record completed for worker $workerId',
          tag: _tagName);
    } catch (e) {
      LoggingService.error('Failed to mark record completed: $e',
          tag: _tagName);
      rethrow;
    }
  }

  /// 计算工作量分配建议
  /// 根据历史数据和工人能力评估推荐分配方案
  static Map<String, dynamic> calculateAssignmentSuggestion({
    required int palletCount,
    required List<WorkerInfo> availableWorkers,
  }) {
    final totalTonnage = palletCount * 1.5;

    // 按角色分组
    final forklifts = availableWorkers.where((w) => w.role == '叉车').toList();
    final warehouses = availableWorkers.where((w) => w.role == '仓管').toList();
    final others = availableWorkers
        .where((w) => w.role != '叉车' && w.role != '仓管')
        .toList();

    // 计算建议的人员配置
    final suggestedForkliftCount =
        (palletCount / 10).ceil().clamp(1, forklifts.length);
    final suggestedWarehouseCount =
        (palletCount / 20).ceil().clamp(1, warehouses.length);

    return {
      'totalTonnage': totalTonnage,
      'suggestedForklifts': forklifts.take(suggestedForkliftCount).toList(),
      'suggestedWarehouses': warehouses.take(suggestedWarehouseCount).toList(),
      'availableOthers': others,
      'tonnagePerForklift': suggestedForkliftCount > 0
          ? totalTonnage / suggestedForkliftCount
          : 0.0,
      'efficiency': _calculateEfficiencyScore(
          palletCount, suggestedForkliftCount + suggestedWarehouseCount),
    };
  }

  /// 计算分配效率评分
  static double _calculateEfficiencyScore(int palletCount, int workerCount) {
    if (workerCount == 0) return 0.0;

    final ratio = palletCount / workerCount;

    // 理想比例：每人处理10-15托
    if (ratio >= 10 && ratio <= 15) {
      return 1.0; // 最佳效率
    } else if (ratio >= 8 && ratio <= 18) {
      return 0.8; // 良好效率
    } else if (ratio >= 5 && ratio <= 25) {
      return 0.6; // 一般效率
    } else {
      return 0.3; // 低效率
    }
  }

  /// 获取工作量分配历史记录
  static Future<List<WorkloadAssignment>> getAssignmentHistory({
    int limit = 50,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // 这里应该从数据库或持久化存储中获取历史记录
      // 当前实现返回空列表，实际项目中需要实现完整的历史记录存储
      return [];
    } catch (e) {
      LoggingService.error('Failed to get assignment history: $e',
          tag: _tagName);
      return [];
    }
  }

  /// 导出工作量分配数据为CSV格式
  static String exportToCsv(WorkloadAssignment assignment) {
    final buffer = StringBuffer();

    // CSV头部
    buffer.writeln('工人ID,姓名,角色,库区,小组,分配吨数,分配时间,是否完成,完成时间');

    // 数据行
    for (final record in assignment.records) {
      buffer.writeln([
        record.workerId,
        record.workerName,
        record.role,
        record.warehouse,
        record.group,
        record.allocatedTonnage.toStringAsFixed(2),
        record.assignedAt.toIso8601String(),
        record.isCompleted ? '是' : '否',
        record.completedAt?.toIso8601String() ?? '',
      ].join(','));
    }

    return buffer.toString();
  }

  /// 🔧 新增：从Map创建WorkloadAssignment（用于TaskService调用）
  static WorkloadAssignment fromMap(Map<String, dynamic> map) {
    return WorkloadAssignment(
      records: (map['records'] as List? ?? [])
          .map((record) =>
              WorkloadRecord.fromMap(record as Map<String, dynamic>))
          .toList(),
      totalTonnage: (map['totalTonnage'] as num? ?? 0).toDouble(),
      palletCount: map['palletCount'] as int? ?? 0,
      assignedAt: DateTime.tryParse(map['assignedAt'] as String? ?? '') ??
          DateTime.now(),
      assignedBy: map['assignedBy'] as String? ?? 'system',
    );
  }

  /// 🔧 新增：保存当前工作量分配（用于TaskService同步状态）
  static Future<void> saveCurrentAssignment(
      WorkloadAssignment assignment) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_storageKey, jsonEncode(assignment.toMap()));

      LoggingService.info(
        '📋 工作量分配状态已保存: ${assignment.records.length}条记录',
        tag: _tagName,
      );
    } catch (e) {
      LoggingService.error('保存工作量分配状态失败: $e', tag: _tagName);
      rethrow;
    }
  }
}
