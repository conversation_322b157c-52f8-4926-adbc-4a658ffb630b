import '../models/task_model.dart';
import '../models/worker_info_data.dart';
import '../models/task_model_extensions.dart';
import '../services/task_service.dart';
import '../services/workload_calculation_service.dart';
import '../services/logging_service.dart';

/// 工作量统计服务
/// 提供多维度的工作量数据分析和统计功能
class WorkloadStatisticsService {
  static const String _tagName = 'WorkloadStatisticsService';

  // 实例变量存储时间范围过滤条件
  DateTime? _startDate;
  DateTime? _endDate;

  /// 设置统计时间范围
  void setDateRange(DateTime? startDate, DateTime? endDate) {
    _startDate = startDate;
    _endDate = endDate;
    LoggingService.info('Date range set: $startDate to $endDate',
        tag: _tagName);
  }

  /// 获取过滤后的任务列表
  List<TaskModel> _getFilteredTasks(TaskService? taskService) {
    final tasks = taskService?.tasks ?? <TaskModel>[];

    if (_startDate == null && _endDate == null) {
      return tasks;
    }

    return tasks.where((task) {
      if (_startDate != null && task.createdAt.isBefore(_startDate!)) {
        return false;
      }
      if (_endDate != null && task.createdAt.isAfter(_endDate!)) {
        return false;
      }
      return true;
    }).toList();
  }

  /// 🔧 提取公共函数：初始化工人统计数据
  Map<String, Map<String, dynamic>> _initializeWorkerStats() {
    final stats = <String, Map<String, dynamic>>{};

    for (final worker in allWorkers) {
      stats[worker.id] = {
        'workerId': worker.id,
        'name': worker.name,
        'role': worker.role,
        'warehouse': worker.warehouse,
        'group': worker.group,
        'assignedTonnage': 0.0,
        'completedTonnage': 0.0,
        'totalTasks': 0,
        'completedTasks': 0,
        'completionRate': 0.0,
        'efficiency': 0.0,
        'averageTonnagePerTask': 0.0,
        'taskDetails': <Map<String, dynamic>>[],
      };
    }

    return stats;
  }

  /// 🔧 提取公共函数：计算衍生统计数据
  void _calculateDerivedStats(Map<String, Map<String, dynamic>> stats) {
    for (final stat in stats.values) {
      final total = stat['totalTasks'] as int;
      final completed = stat['completedTasks'] as int;
      final assignedTonnage = stat['assignedTonnage'] as double;
      final completedTonnage = stat['completedTonnage'] as double;

      stat['completionRate'] = total > 0 ? completed / total : 0.0;
      stat['efficiency'] = assignedTonnage > 0
          ? (completedTonnage / assignedTonnage) * 100
          : 0.0;
      stat['averageTonnagePerTask'] = total > 0 ? assignedTonnage / total : 0.0;

      // 添加更多统计字段
      stat['tonnage'] = assignedTonnage.round();
      stat['tasks'] = total;
      stat['quality'] =
          85 + ((stat['efficiency'] as double) / 100 * 10).round();
      stat['speed'] = 80 + ((stat['efficiency'] as double) / 100 * 15).round();
      stat['cooperation'] =
          90 + ((stat['efficiency'] as double) / 100 * 5).round();
      stat['stability'] =
          88 + ((stat['efficiency'] as double) / 100 * 7).round();
    }
  }

  /// 🔧 新增：判断是否为混装任务
  bool _isMixedTask(TaskModel task) {
    return task.batches.length > 1 ||
        task.template.contains('混合') ||
        task.template.contains('混装') ||
        task.template.contains('mixed');
  }

  /// 🔧 新增：获取混装任务统计
  Future<Map<String, dynamic>> getMixedTaskStatistics(
      TaskService taskService) async {
    final tasks = _getFilteredTasks(taskService);
    final mixedTasks = tasks.where(_isMixedTask).toList();
    final singleTasks = tasks.where((task) => !_isMixedTask(task)).toList();

    // 混装任务统计 - 修复重复计算问题
    int totalMixedBatches = 0;
    int completedMixedBatches = 0;
    double totalMixedTonnage = 0.0;
    double completedMixedTonnage = 0.0;

    for (final task in mixedTasks) {
      // 🔧 修复：按任务计算吨数，避免重复计算
      final taskTonnage = task.quantity * 1.5; // 使用任务总吨数
      totalMixedTonnage += taskTonnage;

      // 统计批次数量
      totalMixedBatches += task.batches.length;

      if (task.isCompleted) {
        completedMixedTonnage += taskTonnage;
        completedMixedBatches +=
            task.batches.where((b) => b.isCompleted).length;
      }
    }

    // 单批次任务统计
    double totalSingleTonnage = 0.0;
    double completedSingleTonnage = 0.0;

    for (final task in singleTasks) {
      final taskTonnage = task.quantity * 1.5;
      totalSingleTonnage += taskTonnage;

      if (task.isCompleted) {
        completedSingleTonnage += taskTonnage;
      }
    }

    return {
      'mixed': {
        'totalTasks': mixedTasks.length,
        'completedTasks': mixedTasks.where((t) => t.isCompleted).length,
        'totalBatches': totalMixedBatches,
        'completedBatches': completedMixedBatches,
        'totalTonnage': totalMixedTonnage,
        'completedTonnage': completedMixedTonnage,
        'batchCompletionRate': totalMixedBatches > 0
            ? completedMixedBatches / totalMixedBatches
            : 0.0,
        'tonnageCompletionRate': totalMixedTonnage > 0
            ? completedMixedTonnage / totalMixedTonnage
            : 0.0,
      },
      'single': {
        'totalTasks': singleTasks.length,
        'completedTasks': singleTasks.where((t) => t.isCompleted).length,
        'totalTonnage': totalSingleTonnage,
        'completedTonnage': completedSingleTonnage,
        'completionRate': singleTasks.isNotEmpty
            ? singleTasks.where((t) => t.isCompleted).length /
                singleTasks.length
            : 0.0,
        'tonnageCompletionRate': totalSingleTonnage > 0
            ? completedSingleTonnage / totalSingleTonnage
            : 0.0,
      },
      'overall': {
        'totalTasks': tasks.length,
        'completedTasks': tasks.where((t) => t.isCompleted).length,
        'totalTonnage': totalMixedTonnage + totalSingleTonnage,
        'completedTonnage': completedMixedTonnage + completedSingleTonnage,
      }
    };
  }

  /// 获取真实的装车数量统计
  Future<Map<String, double>> getActualTonnageStatistics(
      TaskService taskService) async {
    final tasks = _getFilteredTasks(taskService);
    double totalTonnage = 0.0;
    int totalTasks = 0;
    int completedTasks = 0;
    double completedTonnage = 0.0;

    for (final task in tasks) {
      final taskTonnage = task.quantity * 1.5; // 每托1.5吨
      totalTonnage += taskTonnage;
      totalTasks++;

      if (task.isCompleted) {
        completedTasks++;
        completedTonnage += taskTonnage;
      }
    }

    return {
      'totalTonnage': totalTonnage,
      'completedTonnage': completedTonnage,
      'totalTasks': totalTasks.toDouble(),
      'completedTasks': completedTasks.toDouble(),
      'completionRate': totalTasks > 0 ? completedTasks / totalTasks : 0.0,
      'averageTonnagePerTask': totalTasks > 0 ? totalTonnage / totalTasks : 0.0,
    };
  }

  /// 按人员统计工作量（基于真实任务数据）
  Future<Map<String, Map<String, dynamic>>> getWorkerStatistics(
    TaskService taskService, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (startDate != null || endDate != null) {
      setDateRange(startDate, endDate);
    }

    final tasks = _getFilteredTasks(taskService);

    // 🔧 修复：使用新的工作量计算服务
    LoggingService.info('🔧 使用新的工作量计算服务，任务数量: ${tasks.length}', tag: _tagName);

    final workerStatistics = WorkloadCalculationService.calculateWorkerStatistics(
      tasks,
      startDate: startDate,
      endDate: endDate,
    );

    // 转换为原有格式以保持向后兼容性
    final stats = <String, Map<String, dynamic>>{};
    for (final entry in workerStatistics.entries) {
      final workloadStats = entry.value;
      stats[entry.key] = {
        'workerId': workloadStats.workerId,
        'name': workloadStats.workerName,
        'role': workloadStats.role,
        'warehouse': workloadStats.warehouse,
        'group': workloadStats.group,
        'assignedTonnage': workloadStats.assignedTonnage,
        'completedTonnage': workloadStats.completedTonnage,
        'totalTasks': workloadStats.totalTasks,
        'completedTasks': workloadStats.completedTasks,
        'efficiency': workloadStats.efficiency,
        'taskDetails': workloadStats.taskDetails.map((detail) => detail.toMap()).toList(),
      };
    }

    LoggingService.info('🔧 工作量统计完成，活跃工人: ${stats.length}', tag: _tagName);
    return stats;

    // 🔧 旧的处理逻辑已移除，现在使用WorkloadCalculationService

    // 🔧 调试信息：统计完成情况
    int totalActiveWorkers = 0;
    int totalCompletedTasks = 0;
    int totalTasks = 0;

    for (final stat in stats.values) {
      final workerTotalTasks = stat['totalTasks'] as int;
      final workerCompletedTasks = stat['completedTasks'] as int;

      if (workerTotalTasks > 0) {
        totalActiveWorkers++;
        totalTasks += workerTotalTasks;
        totalCompletedTasks += workerCompletedTasks;

        LoggingService.info(
            '👷 工人${stat['name']}: 总任务${workerTotalTasks}, 完成${workerCompletedTasks}, 完成率${((workerCompletedTasks / workerTotalTasks) * 100).toStringAsFixed(1)}%',
            tag: _tagName);
      }
    }

    LoggingService.info(
        '📊 统计汇总: 活跃工人${totalActiveWorkers}人, 总任务${totalTasks}个, 完成${totalCompletedTasks}个, 整体完成率${totalTasks > 0 ? ((totalCompletedTasks / totalTasks) * 100).toStringAsFixed(1) : 0}%',
        tag: _tagName);

    return stats;
  }

  /// 获取工作量概览
  Future<Map<String, dynamic>> getWorkloadOverview(
    TaskService taskService, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (startDate != null || endDate != null) {
      setDateRange(startDate, endDate);
    }

    final tasks = _getFilteredTasks(taskService);

    // 🔧 修复：使用新的工作量计算服务
    LoggingService.info('🔧 使用新的工作量计算服务计算概览，任务数量: ${tasks.length}', tag: _tagName);

    final overview = WorkloadCalculationService.calculateWorkloadOverview(
      tasks,
      startDate: startDate,
      endDate: endDate,
    );

    // 转换为原有格式以保持向后兼容性
    final result = {
      'totalTasks': overview.totalTasks,
      'completedTasks': overview.completedTasks,
      'totalTonnage': overview.totalTonnage.round(),
      'completedTonnage': overview.completedTonnage.round(),
      'totalWorkers': overview.activeWorkers,
      'activeWorkers': overview.activeWorkers,
      'completionRate': (overview.completionRate * 100).round(),
      'averageEfficiency': overview.averageEfficiency.round(),
      // 添加新字段，保持向后兼容
      'workloadTotalTasks': overview.totalTasks,
      'workloadCompletedTasks': overview.completedTasks,
      'workloadTotalTonnage': overview.totalTonnage.round(),
      'workloadCompletedTonnage': overview.completedTonnage.round(),
      'workloadCompletionRate': (overview.completionRate * 100).round(),
    };

    LoggingService.info('🔧 工作量概览计算完成: ${result.toString()}', tag: _tagName);
    return result;
  }

  /// 获取效率排名
  Future<List<Map<String, dynamic>>> getEfficiencyRanking(
    TaskService taskService, {
    DateTime? startDate,
    DateTime? endDate,
    int limit = 10,
  }) async {
    final workerStats = await getWorkerStatistics(
      taskService,
      startDate: startDate,
      endDate: endDate,
    );

    // 按效率排序
    final ranking = workerStats.entries.map((entry) {
      final stats = entry.value;
      return {
        'id': entry.key,
        'name': stats['name'],
        'role': stats['role'],
        'group': stats['group'],
        'warehouse': stats['warehouse'],
        'efficiency': (stats['efficiency'] as double).round(),
        'tonnage': stats['tonnage'],
        'tasks': stats['tasks'],
      };
    }).toList();

    ranking.sort(
        (a, b) => (b['efficiency'] as int).compareTo(a['efficiency'] as int));

    return ranking.take(limit).toList();
  }

  /// 获取工作量趋势数据
  Future<List<Map<String, dynamic>>> getWorkloadTrends(
    TaskService taskService, {
    required DateTime startDate,
    required DateTime endDate,
    String groupBy = 'day',
  }) async {
    final tasks = _getFilteredTasks(taskService);
    final trends = <Map<String, dynamic>>[];

    // 根据groupBy参数生成时间段
    final periods = _generateTimePeriods(startDate, endDate, groupBy);

    for (final period in periods) {
      final periodTasks = tasks
          .where((task) =>
              task.createdAt.isAfter(period['start']) &&
              task.createdAt.isBefore(period['end']))
          .toList();

      double totalTonnage = 0.0;
      double completedTonnage = 0.0;
      int totalAssignments = 0;
      int completedAssignments = 0;

      for (final task in periodTasks) {
        final taskTonnage = task.quantity * 1.5;
        totalTonnage += taskTonnage;

        if (task.isCompleted) {
          completedTonnage += taskTonnage;
        }

        final workload = task.recognitionMetadata?['workload'];
        if (workload != null) {
          try {
            final assignment =
                WorkloadAssignment.fromMap(workload as Map<String, dynamic>);
            totalAssignments += assignment.records.length;
            completedAssignments +=
                assignment.records.where((r) => r.isCompleted).length;
          } catch (e) {
            LoggingService.error('Error processing trend data: $e',
                tag: _tagName);
          }
        }
      }

      trends.add({
        'period': period['label'],
        'startDate': period['start'],
        'endDate': period['end'],
        'totalTonnage': totalTonnage,
        'completedTonnage': completedTonnage,
        'totalAssignments': totalAssignments,
        'completedAssignments': completedAssignments,
        'completionRate': totalAssignments > 0
            ? completedAssignments / totalAssignments
            : 0.0,
        'taskCount': periodTasks.length,
        'efficiency': totalTonnage > 0 ? completedTonnage / totalTonnage : 0.0,
      });
    }

    return trends;
  }

  /// 生成时间段列表
  List<Map<String, dynamic>> _generateTimePeriods(
    DateTime startDate,
    DateTime endDate,
    String groupBy,
  ) {
    final periods = <Map<String, dynamic>>[];
    var current = startDate;

    while (current.isBefore(endDate)) {
      DateTime periodEnd;
      String label;

      switch (groupBy) {
        case 'day':
          periodEnd = DateTime(current.year, current.month, current.day + 1);
          label = '${current.month}/${current.day}';
          break;
        case 'week':
          periodEnd = current.add(const Duration(days: 7));
          label = '第${_getWeekOfYear(current)}周';
          break;
        case 'month':
          periodEnd = DateTime(current.year, current.month + 1, 1);
          label = '${current.year}年${current.month}月';
          break;
        default:
          periodEnd = current.add(const Duration(days: 1));
          label = '${current.month}/${current.day}';
      }

      if (periodEnd.isAfter(endDate)) {
        periodEnd = endDate;
      }

      periods.add({
        'start': current,
        'end': periodEnd,
        'label': label,
      });

      current = periodEnd;
    }

    return periods;
  }

  /// 获取年度第几周
  int _getWeekOfYear(DateTime date) {
    final firstDayOfYear = DateTime(date.year, 1, 1);
    final daysSinceFirstDay = date.difference(firstDayOfYear).inDays;
    return (daysSinceFirstDay / 7).ceil() + 1;
  }

  /// 导出统计数据为CSV格式
  Future<String> exportStatisticsToCsv(TaskService taskService) async {
    final workerStats = await getWorkerStatistics(taskService);
    final buffer = StringBuffer();

    // CSV头部
    buffer.writeln('工人ID,姓名,角色,库区,小组,分配吨数,完成任务数,总任务数,完成率');

    // 数据行
    for (final stat in workerStats.values) {
      if ((stat['totalTasks'] as int) > 0) {
        // 只导出有任务的工人
        buffer.writeln([
          stat['workerId'],
          stat['name'],
          stat['role'],
          stat['warehouse'],
          stat['group'],
          (stat['assignedTonnage'] as double).toStringAsFixed(2),
          stat['completedTasks'],
          stat['totalTasks'],
          ((stat['completionRate'] as double) * 100).toStringAsFixed(1) + '%',
        ].join(','));
      }
    }

    return buffer.toString();
  }

  /// 获取识别效率分析数据（基于真实任务数据）
  Future<Map<String, dynamic>> getRecognitionEfficiencyAnalysis(
      TaskService taskService) async {
    final tasks = _getFilteredTasks(taskService);

    // 识别效率统计
    int totalRecognitionPhotos = 0;
    int completedRecognitionPhotos = 0;
    int verifiedRecognitionPhotos = 0;
    int failedRecognitionPhotos = 0;
    List<double> recognitionTimes = [];
    Map<String, int> errorTypeCount = {};
    Map<String, List<double>> hourlyRecognitionTimes = {};

    for (final task in tasks) {
      for (final photo in task.photos) {
        if (photo.needRecognition) {
          totalRecognitionPhotos++;

          if (photo.isRecognitionCompleted) {
            completedRecognitionPhotos++;

            // 计算识别时间
            if (photo.recognitionStartTime != null &&
                photo.recognitionEndTime != null) {
              final duration = photo.recognitionEndTime!
                  .difference(photo.recognitionStartTime!);
              recognitionTimes.add(duration.inMilliseconds.toDouble());

              // 按小时分组识别时间
              final hour = photo.recognitionStartTime!.hour.toString();
              hourlyRecognitionTimes.putIfAbsent(hour, () => []);
              hourlyRecognitionTimes[hour]!
                  .add(duration.inMilliseconds.toDouble());
            }

            if (photo.isVerified) {
              verifiedRecognitionPhotos++;
            } else if (photo.recognitionFailed) {
              failedRecognitionPhotos++;

              // 统计错误类型
              final errorType = photo.recognitionErrorMessage ?? '未知错误';
              errorTypeCount[errorType] = (errorTypeCount[errorType] ?? 0) + 1;
            }
          }
        }
      }
    }

    // 计算统计指标
    final avgRecognitionTime = recognitionTimes.isNotEmpty
        ? recognitionTimes.reduce((a, b) => a + b) / recognitionTimes.length
        : 0.0;

    final recognitionSuccessRate = totalRecognitionPhotos > 0
        ? verifiedRecognitionPhotos / totalRecognitionPhotos
        : 0.0;

    final recognitionSpeed = avgRecognitionTime > 0
        ? 1000 / avgRecognitionTime // 每秒处理数量
        : 0.0;

    return {
      'totalRecognitionPhotos': totalRecognitionPhotos,
      'completedRecognitionPhotos': completedRecognitionPhotos,
      'verifiedRecognitionPhotos': verifiedRecognitionPhotos,
      'failedRecognitionPhotos': failedRecognitionPhotos,
      'recognitionSuccessRate': recognitionSuccessRate,
      'averageRecognitionTime': avgRecognitionTime,
      'recognitionSpeed': recognitionSpeed,
      'errorTypeDistribution': errorTypeCount,
      'hourlyRecognitionTimes': hourlyRecognitionTimes,
      'recognitionTimes': recognitionTimes,
    };
  }

  /// 获取货物流转分析数据（基于真实批次数据）
  Future<Map<String, dynamic>> getCargoFlowAnalysis(
      TaskService taskService) async {
    final tasks = _getFilteredTasks(taskService);

    Map<String, int> productCodeFlow = {};
    Map<String, int> warehouseFlow = {};
    Map<String, Map<String, int>> warehouseToWarehouseFlow = {};
    List<Map<String, dynamic>> batchTimeline = [];
    Map<String, int> cargoStatusCount = {};

    for (final task in tasks) {
      // 产品流转统计
      for (final batch in task.batches) {
        productCodeFlow[batch.productCode] =
            (productCodeFlow[batch.productCode] ?? 0) + batch.plannedQuantity;
      }

      // 仓库流转统计（基于工作量分配）
      final workload = task.recognitionMetadata?['workload'];
      if (workload != null) {
        try {
          final assignment =
              WorkloadAssignment.fromMap(workload as Map<String, dynamic>);
          for (final record in assignment.records) {
            warehouseFlow[record.warehouse] =
                (warehouseFlow[record.warehouse] ?? 0) + 1;
          }
        } catch (e) {
          LoggingService.error('Error processing cargo flow: $e',
              tag: _tagName);
        }
      }

      // 批次时间线
      for (final batch in task.batches) {
        batchTimeline.add({
          'taskId': task.id,
          'productCode': batch.productCode,
          'batchNumber': batch.batchNumber,
          'plannedQuantity': batch.plannedQuantity,
          'recognizedQuantity': batch.recognizedQuantity,
          'createTime': task.createTime,
          'isCompleted': batch.isCompleted,
        });
      }

      // 货物状态统计
      final status = task.isCompleted ? '已完成' : '进行中';
      cargoStatusCount[status] = (cargoStatusCount[status] ?? 0) + 1;
    }

    // 按时间排序批次时间线
    batchTimeline.sort((a, b) =>
        (a['createTime'] as DateTime).compareTo(b['createTime'] as DateTime));

    return {
      'productCodeFlow': productCodeFlow,
      'warehouseFlow': warehouseFlow,
      'warehouseToWarehouseFlow': warehouseToWarehouseFlow,
      'batchTimeline': batchTimeline,
      'cargoStatusDistribution': cargoStatusCount,
    };
  }

  /// 获取质量控制分析数据（基于真实识别结果）
  Future<Map<String, dynamic>> getQualityControlAnalysis(
      TaskService taskService) async {
    final tasks = _getFilteredTasks(taskService);

    Map<String, int> qualityMetrics = {
      'totalPhotos': 0,
      'verifiedPhotos': 0,
      'failedPhotos': 0,
      'manualVerifiedPhotos': 0,
    };

    Map<String, int> defectTypes = {};
    List<Map<String, dynamic>> qualityTrends = [];
    Map<String, double> inspectionPassRates = {};

    for (final task in tasks) {
      int taskVerifiedPhotos = 0;
      int taskTotalPhotos = 0;

      for (final photo in task.photos) {
        taskTotalPhotos++;
        qualityMetrics['totalPhotos'] = qualityMetrics['totalPhotos']! + 1;

        if (photo.isVerified) {
          taskVerifiedPhotos++;
          qualityMetrics['verifiedPhotos'] =
              qualityMetrics['verifiedPhotos']! + 1;
        } else if (photo.recognitionFailed) {
          qualityMetrics['failedPhotos'] = qualityMetrics['failedPhotos']! + 1;

          // 统计缺陷类型
          final defectType = photo.recognitionErrorMessage ?? '识别失败';
          defectTypes[defectType] = (defectTypes[defectType] ?? 0) + 1;
        }

        if (photo.manualVerified) {
          qualityMetrics['manualVerifiedPhotos'] =
              qualityMetrics['manualVerifiedPhotos']! + 1;
        }
      }

      // 质量趋势数据
      final passRate =
          taskTotalPhotos > 0 ? taskVerifiedPhotos / taskTotalPhotos : 0.0;
      qualityTrends.add({
        'taskId': task.id,
        'createTime': task.createTime,
        'passRate': passRate,
        'verifiedPhotos': taskVerifiedPhotos,
        'totalPhotos': taskTotalPhotos,
      });
    }

    // 按模板分组检验通过率
    final templateGroups = <String, List<double>>{};
    for (final task in tasks) {
      final template = task.template;
      final taskPhotos = task.photos.length;
      final verifiedPhotos = task.photos.where((p) => p.isVerified).length;
      final passRate = taskPhotos > 0 ? verifiedPhotos / taskPhotos : 0.0;

      templateGroups.putIfAbsent(template, () => []);
      templateGroups[template]!.add(passRate);
    }

    // 计算各模板平均通过率
    for (final entry in templateGroups.entries) {
      final rates = entry.value;
      final avgRate =
          rates.isNotEmpty ? rates.reduce((a, b) => a + b) / rates.length : 0.0;
      inspectionPassRates[entry.key] = avgRate;
    }

    // 按时间排序质量趋势
    qualityTrends.sort((a, b) =>
        (a['createTime'] as DateTime).compareTo(b['createTime'] as DateTime));

    return {
      'qualityMetrics': qualityMetrics,
      'defectTypeDistribution': defectTypes,
      'qualityTrends': qualityTrends,
      'inspectionPassRates': inspectionPassRates,
    };
  }

  /// 获取实时作业监控数据
  Future<Map<String, dynamic>> getRealTimeOperationData(
      TaskService taskService) async {
    final tasks = _getFilteredTasks(taskService);
    final currentTasks = tasks.where((t) => !t.isCompleted).toList();

    Map<String, dynamic> taskStatusBoard = {
      'inProgress': 0,
      'completed': 0,
      'failed': 0,
    };

    List<Map<String, dynamic>> activeTasksDetails = [];
    Map<String, int> deviceStatusCount = {
      'active': 0,
      'idle': 0,
      'error': 0,
    };

    List<Map<String, dynamic>> alarmList = [];

    for (final task in tasks) {
      // 任务状态统计
      if (task.isCompleted) {
        taskStatusBoard['completed'] = taskStatusBoard['completed']! + 1;
      } else {
        taskStatusBoard['inProgress'] = taskStatusBoard['inProgress']! + 1;

        // 🔧 修复：活跃任务进度计算 - 包含已完成和失败的照片
        final progress = task.photos.where((p) => 
          p.imagePath != null && 
          p.imagePath!.isNotEmpty && 
          (p.recognitionStatus == RecognitionStatus.completed || 
           p.recognitionStatus == RecognitionStatus.failed)
        ).length / task.photos.length;
        activeTasksDetails.add({
          'taskId': task.id,
          'template': task.template,
          'progress': progress,
          'startTime': task.createTime,
          'estimatedCompletion':
              task.createTime.add(Duration(hours: 2)), // 预估完成时间
        });

        // 检查是否有异常需要报警
        final failedPhotos =
            task.photos.where((p) => p.recognitionFailed).length;
        if (failedPhotos > 0) {
          alarmList.add({
            'taskId': task.id,
            'type': 'recognition_failure',
            'message': '任务 ${task.id} 有 $failedPhotos 张照片识别失败',
            'severity': 'warning',
            'timestamp': DateTime.now(),
          });
        }
      }
    }

    // 设备状态模拟（基于任务活跃度）
    final activeTaskCount = currentTasks.length;
    deviceStatusCount['active'] = activeTaskCount;
    deviceStatusCount['idle'] = (5 - activeTaskCount).clamp(0, 5); // 假设最多5个设备

    return {
      'taskStatusBoard': taskStatusBoard,
      'activeTasksDetails': activeTasksDetails,
      'deviceStatusCount': deviceStatusCount,
      'alarmList': alarmList,
      'lastUpdateTime': DateTime.now(),
    };
  }

  /// 获取成本效益分析数据
  Future<Map<String, dynamic>> getCostBenefitAnalysis(
      TaskService taskService) async {
    final tasks = _getFilteredTasks(taskService);
    final workerStats = await getWorkerStatistics(taskService);

    // 人工成本分析（基于工作量和时间）
    double totalLaborCost = 0.0;
    Map<String, double> laborCostByRole = {};

    // 假设工资标准（元/小时）
    const Map<String, double> hourlyRates = {
      '叉车': 25.0,
      '仓管': 20.0,
      '装卸工': 18.0,
      '司机': 22.0,
      '质检员': 24.0,
    };

    for (final workerStat in workerStats.values) {
      final role = workerStat['role'] as String;
      final totalTasks = workerStat['totalTasks'] as int;
      final hourlyRate = hourlyRates[role] ?? 20.0;

      // 假设每个任务需要2小时
      final hoursWorked = totalTasks * 2.0;
      final cost = hoursWorked * hourlyRate;

      totalLaborCost += cost;
      laborCostByRole[role] = (laborCostByRole[role] ?? 0.0) + cost;
    }

    // 时间成本分析
    final totalTasks = tasks.length;
    final completedTasks = tasks.where((t) => t.isCompleted).length;
    final avgTaskDuration = tasks.isNotEmpty
        ? tasks
                .map((t) => t.isCompleted
                    ? (t.completedAt?.difference(t.createTime).inHours ?? 2)
                    : 2)
                .reduce((a, b) => a + b) /
            tasks.length
        : 0.0;

    // 效率提升ROI计算
    final totalTonnage =
        tasks.fold(0.0, (sum, task) => sum + (task.quantity * 1.5));
    final costPerTon = totalTonnage > 0 ? totalLaborCost / totalTonnage : 0.0;

    return {
      'totalLaborCost': totalLaborCost,
      'laborCostByRole': laborCostByRole,
      'avgTaskDuration': avgTaskDuration,
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'totalTonnage': totalTonnage,
      'costPerTon': costPerTon,
      'efficiencyROI': {
        'beforeAutomation': costPerTon * 1.5, // 假设自动化前成本高50%
        'afterAutomation': costPerTon,
        'savings': costPerTon * 0.5,
        'roiPercentage': 33.3,
      },
    };
  }
}
