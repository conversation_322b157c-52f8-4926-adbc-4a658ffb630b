import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:async';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/worker_info_data.dart';
import 'package:loadguard/services/workload_statistics_service.dart';
import 'package:image/image.dart' as img;
import 'package:http/http.dart' as http;
import 'package:printing/printing.dart';
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/services/confidence_evaluation_service.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 装运卫士专业版PDF报告生成服务
/// 基于ML Kit识别引擎，集成12种专业算法和动态置信度评估
/// 支持完整的中文显示、图片处理和详细报告生成
class PdfService {
  static final PdfService _instance = PdfService._internal();
  factory PdfService() => _instance;
  PdfService._internal();

  pw.Font? _chineseFont;
  pw.Font? _boldChineseFont;
  bool _fontInitialized = false;

  /// 预加载中文字体（建议在应用启动时调用）
  static Future<void> preloadFonts() async {
    final instance = PdfService();
    await instance._initializeFonts();
  }

  /// 初始化中文字体 - 按照官方推荐的字体加载策略
  Future<void> _initializeFonts() async {
    if (_fontInitialized) return;
    
    try {
      // 初始化PDF中文字体
      
      // 策略1：使用pubspec.yaml中声明的字体（官方推荐方式）
      try {
        // 加载pubspec.yaml中声明的字体
        final fontData = await rootBundle.load('assets/fonts/NotoSansSC-Regular.ttf');
        final boldFontData = await rootBundle.load('assets/fonts/NotoSansSC-Bold.ttf');
        
        // 确保ByteData不为空
        if (fontData.lengthInBytes == 0 || boldFontData.lengthInBytes == 0) {
          throw Exception('字体文件为空');
        }
        
        _chineseFont = pw.Font.ttf(fontData);
        _boldChineseFont = pw.Font.ttf(boldFontData);
        // 本地中文字体加载成功
        _fontInitialized = true;
        return;
    } catch (e) {
        AppLogger.warning('本地字体加载失败: $e，请检查字体文件和pubspec.yaml配置');
      }
      
      // 策略2：使用printing包的Google字体（网络备选，仅用于开发测试）
      try {
        // 尝试使用Google字体
        _chineseFont = await PdfGoogleFonts.notoSansSCRegular();
        _boldChineseFont = await PdfGoogleFonts.notoSansSCBold();
        AppLogger.info('Google中文字体加载成功');
        _fontInitialized = true;
        return;
      } catch (e) {
        AppLogger.warning('Google字体加载失败: $e，继续使用备用方案');
      }
      
      // 策略3：最后的备用方案
      AppLogger.warning('使用备用字体方案，中文可能显示异常');
      _chineseFont = pw.Font.helvetica();
      _boldChineseFont = pw.Font.helveticaBold();
      _fontInitialized = true;
      
    } catch (e) {
      AppLogger.error('字体初始化完全失败: $e');
      _chineseFont = pw.Font.helvetica();
      _boldChineseFont = pw.Font.helveticaBold();
      _fontInitialized = true;
    }
  }



  /// 生成完整的装运卫士任务报告PDF
  Future<Uint8List> generateTaskReport(
    List<TaskModel> tasks,
    String title, {
    bool includePhotos = true,
    bool includeStatistics = true,
    bool includeWorkerStatistics = true,
  }) async {
    await _initializeFonts();
    final pdf = pw.Document();
    final now = DateTime.now();
    await _addCoverPage(pdf, title, now, tasks);
    if (includeStatistics) await _addStatisticsPage(pdf, tasks);
    // 禁用工作量统计页面直到修复类型错误
    // if (includeWorkerStatistics) await _addWorkerStatisticsPage(pdf, tasks);
    await _addTaskDetailsPages(pdf, tasks);
    if (includePhotos) await _addPhotoPages(pdf, tasks);
    return await pdf.save();
  }

  /// 添加封面页
  Future<void> _addCoverPage(pw.Document pdf, String title, DateTime now, [List<TaskModel>? tasks]) async {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Container(
            decoration: pw.BoxDecoration(
              gradient: pw.LinearGradient(
                colors: [PdfColors.blue700, PdfColors.blue400],
                begin: pw.Alignment.topLeft,
                end: pw.Alignment.bottomRight,
              ),
            ),
            child: pw.Padding(
              padding: const pw.EdgeInsets.all(20),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // 顶部标题区域 - 紧凑设计
                  pw.Container(
                    padding: const pw.EdgeInsets.symmetric(vertical: 15),
                    child: pw.Row(
                      children: [
                        pw.Expanded(
                          child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              _chineseText(
                                '装运卫士专业版',
                                fontSize: 28,
                                fontWeight: pw.FontWeight.bold,
                                color: PdfColors.white,
                              ),
                              pw.SizedBox(height: 5),
                              _chineseText(
                                '任务识别报告',
                                fontSize: 18,
                                color: PdfColors.white,
                              ),
                            ],
                          ),
                        ),
                        pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.end,
                          children: [
                            _chineseText('生成时间:', fontSize: 12, fontWeight: pw.FontWeight.bold, color: PdfColors.white),
                            _chineseText('${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}'),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // 首页内容顺序调整：基本信息卡片 > 任务详情卡片 > 分配表格
                  if (tasks != null && tasks.isNotEmpty) ...[
                    _buildTaskBasicInfoCard(tasks.first),
                    pw.SizedBox(height: 15),
                    _buildTaskStatusCard(tasks.first),
                    pw.SizedBox(height: 15),
                    _buildWorkloadTable(tasks.first),
                    pw.SizedBox(height: 15),
                  ] else ...[
                    // 无任务时显示
                    pw.Expanded(
                      child: pw.Center(
                        child: pw.Column(
                          mainAxisAlignment: pw.MainAxisAlignment.center,
                          children: [
                            _chineseText('📂', fontSize: 48, color: PdfColors.grey400),
                            pw.SizedBox(height: 15),
                            _chineseText('暂无任务数据', fontSize: 16, color: PdfColors.grey600),
                            pw.SizedBox(height: 8),
                            _chineseText('请创建任务后重新生成报告', fontSize: 12, color: PdfColors.grey500),
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }


  /// 添加统计信息页
  Future<void> _addStatisticsPage(pw.Document pdf, List<TaskModel> tasks) async {
    // 只统计首页显示的当前任务
    final TaskModel? mainTask = tasks.isNotEmpty ? tasks.first : null;
    if (mainTask == null) return; // 如果没有任务，跳过统计页面
    
    final allPhotos = mainTask.photos;
    final recognitionPhotos = allPhotos.where((photo) => photo.needRecognition == true).toList();
    final archivePhotos = allPhotos.where((photo) => photo.needRecognition != true).toList();
    
    // 更准确的数据统计计算
    final completedPhotos = allPhotos.where((photo) => photo.imagePath?.isNotEmpty == true).length;
    
    // 更准确的验证状态判断
    final verifiedPhotos = allPhotos.where((photo) => 
      photo.isVerified || // 直接验证通过
      (photo.recognitionResult != null && photo.recognitionResult!.matchesPreset) || // 识别结果匹配
      (photo.recognitionStatus == RecognitionStatus.completed) // 状态为完成
    ).length;
    
    // 更准确的失败状态判断
    final failedPhotos = allPhotos.where((photo) => 
      photo.recognitionFailed || // 直接标记失败
      (photo.recognitionResult != null && !photo.recognitionResult!.matchesPreset) || // 识别结果不匹配
      (photo.recognitionStatus == RecognitionStatus.failed) // 状态为失败
    ).length;
    
    // 识别中的照片数量
    final processingPhotos = allPhotos.where((photo) => 
      photo.recognitionStatus == RecognitionStatus.processing
    ).length;
    
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // 页面标题 - 根据任务类型区分
              _chineseText(
                _isMixedTask(mainTask) ? '混装任务统计分析' : '单批次任务统计分析',
                fontSize: 24, 
                fontWeight: pw.FontWeight.bold
              ),
              pw.SizedBox(height: 20),
              
              // 实时照片统计概览
              pw.Row(
                children: [
                  pw.Expanded(
                    child: _buildStatCard('总照片数', allPhotos.length.toString(), PdfColors.blue500),
                  ),
                  pw.SizedBox(width: 10),
                  pw.Expanded(
                    child: _buildStatCard('已完成', completedPhotos.toString(), PdfColors.green500),
                  ),
                  pw.SizedBox(width: 10),
                  pw.Expanded(
                    child: _buildStatCard('验证通过', verifiedPhotos.toString(), PdfColors.purple500),
                  ),
                  pw.SizedBox(width: 10),
                  pw.Expanded(
                    child: _buildStatCard('识别失败', failedPhotos.toString(), PdfColors.red500),
                  ),
                ],
              ),
              
              pw.SizedBox(height: 30),
              
              // 根据任务类型定制内容
              _chineseText(
                _isMixedTask(mainTask) ? '混装任务执行详情' : '单批次任务执行详情',
                fontSize: 18, 
                fontWeight: pw.FontWeight.bold
              ),
              pw.SizedBox(height: 15),
              
              // 任务完成度可视化
              _buildTaskExecutionDetails(mainTask, completedPhotos, verifiedPhotos, recognitionPhotos),
              
              pw.SizedBox(height: 20),
              
              // 存证照片统计
              _chineseText('存证照片统计', fontSize: 18, fontWeight: pw.FontWeight.bold),
              pw.SizedBox(height: 15),
              
              pw.Container(
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColors.green50,
                  borderRadius: pw.BorderRadius.circular(8),
                  border: pw.Border.all(color: PdfColor.fromInt(0x4D000000)),
                ),
                child: pw.Column(
                  children: [
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        _chineseText('存证已拍摄:', fontSize: 14),
                        _chineseText('${archivePhotos.where((p) => p.imagePath != null).length}张', fontSize: 14, fontWeight: pw.FontWeight.bold),
                      ],
                    ),
                    pw.SizedBox(height: 10),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        _chineseText('存储状态:', fontSize: 14),
                        _chineseText('安全本地存储', fontSize: 14, fontWeight: pw.FontWeight.bold, color: PdfColors.green600),
                      ],
                    ),
                  ],
                ),
              ),
              
              pw.SizedBox(height: 30),
              
              // 页脚
              pw.Container(
                alignment: pw.Alignment.center,
                child: _chineseText('第 2 页 | 统计分析', fontSize: 10, color: PdfColors.grey600),
              ),
            ],
          );
        },
      ),
    );
  }

  /// 添加任务详情页（支持照片表格跨页显示）
  Future<void> _addTaskDetailsPages(pw.Document pdf, List<TaskModel> tasks) async {
    for (int i = 0; i < tasks.length; i++) {
      final task = tasks[i];
      
      // 为混装任务添加批次分组详情页
      if (_isMixedTask(task)) {
        await _addMixedTaskBatchDetailsPage(pdf, task, i + 1);
      }
      
      // 支持照片表格跨多页显示
      await _addPhotoTablePages(pdf, task.photos, i + 1);
    }
  }

  /// 添加照片表格页面，支持自动分页
  Future<void> _addPhotoTablePages(pw.Document pdf, List<PhotoItem> photos, int taskNumber) async {
    const int photosPerPage = 15; // 每页最多显示15张照片（保证表格可读性）
    final int totalPages = (photos.length / photosPerPage).ceil();
    
    for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
      final int startIndex = pageIndex * photosPerPage;
      final int endIndex = (startIndex + photosPerPage).clamp(0, photos.length);
      final List<PhotoItem> pagePhotos = photos.sublist(startIndex, endIndex);
      
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // 页面标题
                _chineseText(
                  totalPages > 1 
                    ? '任务详情 $taskNumber - 第${pageIndex + 1}页'
                    : '任务详情 $taskNumber', 
                  fontSize: 24, 
                  fontWeight: pw.FontWeight.bold
                ),
                pw.SizedBox(height: 20),
                
                // 照片详情标题和统计
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    _chineseText('照片详情', fontSize: 18, fontWeight: pw.FontWeight.bold),
                    _chineseText(
                      totalPages > 1 
                        ? '显示 ${startIndex + 1}-$endIndex 项，共 ${photos.length} 项'
                        : '共 ${photos.length} 项',
                      fontSize: 12, 
                      color: PdfColors.grey600
                    ),
                  ],
                ),
                pw.SizedBox(height: 15),
                
                // 照片表格（使用固定高度避免Expanded问题）
                _buildPhotoTable(pagePhotos, startIndex),
                
                pw.SizedBox(height: 20),
                
                // 页脚
                pw.Container(
                  alignment: pw.Alignment.center,
                  child: _chineseText(
                    totalPages > 1 
                      ? '第 ${4 + taskNumber + pageIndex} 页 | 任务详情 $taskNumber (${pageIndex + 1}/$totalPages)'
                      : '第 ${4 + taskNumber} 页 | 任务详情 $taskNumber',
                    fontSize: 10, 
                    color: PdfColors.grey600
                  ),
                ),
              ],
            );
          },
        ),
      );
    }
  }

  /// 添加照片展示页
  Future<void> _addPhotoPages(pw.Document pdf, List<TaskModel> tasks) async {
    AppLogger.info('PDF生成 - 任务数量: ${tasks.length}');
    
    // 🚀 性能优化：直接筛选有效照片，跳过路径修复和搜索
    final allPhotos = <PhotoItem>[];
    
    for (int i = 0; i < tasks.length; i++) {
      final task = tasks[i];
      AppLogger.info('任务${i + 1}: ${task.template} - 照片总数: ${task.photos.length}');
      
      // 🚀 优化：只处理有有效路径的照片，跳过null/空路径的检查
      final validPhotos = task.photos.where((photo) {
        final hasValidPath = photo.imagePath != null && photo.imagePath!.isNotEmpty;
        if (hasValidPath) {
          AppLogger.info('有效照片: ${photo.label} - 路径: ${photo.imagePath}');
        }
        return hasValidPath;
      }).toList();
      
      allPhotos.addAll(validPhotos);
      AppLogger.info('任务${i + 1}: 有效照片数: ${validPhotos.length}张');
    }
    
    AppLogger.info('PDF生成 - 待处理照片总数: ${allPhotos.length}张');
    
    // 🚀 性能优化：设置最大处理照片数量
    final maxPhotos = 50; // 恢复到50张上限，保证完整性
    final photosToProcess = allPhotos.take(maxPhotos).toList();
    
    if (allPhotos.length > maxPhotos) {
      AppLogger.warning('照片数量过多(${allPhotos.length}张)，仅处理前${maxPhotos}张');
    }
    
    for (int i = 0; i < photosToProcess.length; i++) {
      final photo = photosToProcess[i];
      AppLogger.info('正在处理照片 ${i + 1}/${photosToProcess.length}: ${photo.label}');
      AppLogger.info('照片路径: ${photo.imagePath}');
      
      // 🚀 性能优化：简化图片加载，只处理确认存在的文件
      pw.ImageProvider? imageProvider;
      try {
        // 直接尝试加载，不进行额外的路径搜索
        imageProvider = await _loadAndResizeImage(photo.imagePath!, maxWidth: 300, maxHeight: 400);
        if (imageProvider != null) {
          AppLogger.info('照片 ${photo.label} 加载成功');
        }
      } catch (e) {
        AppLogger.warning('照片 ${photo.label} 加载失败: $e');
        imageProvider = null;
      }
      
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // 页面标题
                _chineseText('照片展示 ${i + 1}', fontSize: 24, fontWeight: pw.FontWeight.bold),
                pw.SizedBox(height: 20),
                
                // 照片信息卡片
                pw.Expanded(
                  child: pw.Container(
                    decoration: pw.BoxDecoration(
                      border: pw.Border.all(color: PdfColors.grey300),
                      borderRadius: pw.BorderRadius.circular(8),
                    ),
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        // 照片标题栏
                        pw.Container(
                          padding: const pw.EdgeInsets.all(15),
                          decoration: pw.BoxDecoration(
                            color: photo.needRecognition == true ? PdfColors.purple50 : PdfColors.green50,
                            borderRadius: pw.BorderRadius.only(
                              topLeft: pw.Radius.circular(8),
                              topRight: pw.Radius.circular(8),
                            ),
                          ),
                          child: pw.Row(
                            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                            children: [
                              _chineseText(photo.label, fontSize: 16, fontWeight: pw.FontWeight.bold),
                              pw.Container(
                                padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: pw.BoxDecoration(
                                  color: photo.needRecognition == true ? PdfColors.purple500 : PdfColors.green500,
                                  borderRadius: pw.BorderRadius.circular(15),
                                ),
                                child: _chineseText(
                                  photo.needRecognition == true ? 'AI识别' : '存证',
                                  fontSize: 12,
                                  color: PdfColors.white,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        // 照片内容区域
                        pw.Expanded(
                          child: pw.Container(
                            padding: const pw.EdgeInsets.all(20),
                            child: imageProvider != null
                              ? pw.Center(
                                  child: pw.Container(
                                    decoration: pw.BoxDecoration(
                                      boxShadow: [
                                        pw.BoxShadow(
                                          color: PdfColor(0, 0, 0, 0.1),
                                          blurRadius: 10,
                                          offset: const PdfPoint(0, 5),
                                        ),
                                      ],
                                    ),
                                    child: pw.Image(
                                      imageProvider,
                                      fit: pw.BoxFit.contain,
                                    ),
                                  ),
                                )
                              : pw.Center(
                                  child: pw.Container(
                                    padding: const pw.EdgeInsets.all(30),
                                    decoration: pw.BoxDecoration(
                                      color: PdfColors.grey100,
                                      borderRadius: pw.BorderRadius.circular(8),
                                    ),
                                    child: _chineseText('照片加载失败', fontSize: 16, color: PdfColors.grey600),
                                  ),
                                ),
                          ),
                        ),
                        
                        // 照片信息栏
                        pw.Container(
                          padding: const pw.EdgeInsets.all(15),
                          decoration: pw.BoxDecoration(
                            color: PdfColors.grey50,
                            borderRadius: pw.BorderRadius.only(
                              bottomLeft: pw.Radius.circular(8),
                              bottomRight: pw.Radius.circular(8),
                            ),
                          ),
                          child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Row(
                                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                children: [
                                  _chineseText('验证状态:', fontSize: 14, fontWeight: pw.FontWeight.bold),
                                  _chineseText(_getVerificationStatusText(photo), fontSize: 14),
                                ],
                              ),
                              
                              if (photo.recognitionResult != null) ...[
                                pw.SizedBox(height: 10),
                                pw.Row(
                                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                  children: [
                                    _chineseText('识别结果:', fontSize: 14, fontWeight: pw.FontWeight.bold),
                                    _chineseText(photo.recognitionResult?.extractedProductCode ?? '无', fontSize: 14),
                                  ],
                                ),
                                pw.SizedBox(height: 5),
                                pw.Row(
                                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                  children: [
                                    _chineseText('置信度:', fontSize: 14, fontWeight: pw.FontWeight.bold),
                                    _chineseText(_getRealConfidenceText(photo), fontSize: 14),
                                  ],
                                ),
                              ],
                              
                              pw.SizedBox(height: 10),
                              pw.Row(
                                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                                children: [
                                  _chineseText('验证方式:', fontSize: 14, fontWeight: pw.FontWeight.bold),
                                  _chineseText(_getVerificationMethodText(photo), fontSize: 14),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                pw.SizedBox(height: 10),
                
                // 页脚
                pw.Container(
                  alignment: pw.Alignment.center,
                  child: _chineseText('第 ${4 + tasks.length + i + 1} 页 | 照片展示 ${i + 1}/${photosToProcess.length}', fontSize: 10, color: PdfColors.grey600),
                ),
              ],
            );
          },
        ),
      );
    }
  }

  /// 构建统计卡片
  pw.Widget _buildStatCard(String title, String value, PdfColor color) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromInt(0x1A000000),
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColor.fromInt(0x4D000000)),
      ),
      child: pw.Column(
        children: [
          _chineseText(title, fontSize: 12, color: color),
          pw.SizedBox(height: 5),
          _chineseText(value, fontSize: 24, fontWeight: pw.FontWeight.bold, color: color),
        ],
      ),
    );
  }

  /// 构建封面页统计项
  pw.Widget _buildCoverStatItem(String title, String value, PdfColor color) {
    return pw.Column(
      children: [
        _chineseText(value, fontSize: 18, fontWeight: pw.FontWeight.bold, color: color),
        pw.SizedBox(height: 4),
        _chineseText(title, fontSize: 12, color: PdfColors.grey600),
      ],
    );
  }

  /// 构建详细任务状态卡片（类似手机端任务状态显示）
  pw.Widget _buildDetailedTaskCard(TaskModel task) {
    // 计算拍照进度
    final totalRequired = task.photos.length; // 应拍总数
    final completed = task.photos.where((p) => p.imagePath != null && p.imagePath!.isNotEmpty).length; // 已完成数
    final verified = task.photos.where((p) => p.isVerified).length; // 验证通过数
    
    // 获取识别精度 - 使用真实评估的置信度
    final accuracyText = _getTaskAccuracyText(task);
    
    // 获取批次信息
    final batchInfo = task.batches.isNotEmpty ? task.batches : [];
    
    return pw.Container(
      padding: const pw.EdgeInsets.all(14),
      decoration: pw.BoxDecoration(
        color: task.isCompleted ? PdfColors.green50 : PdfColors.orange50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(
          color: task.isCompleted ? PdfColors.green200 : PdfColors.orange200,
          width: 1,
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // 标题行
          pw.Row(
            children: [
              pw.Container(
                width: 6,
                height: 6,
                decoration: pw.BoxDecoration(
                  color: task.isCompleted ? PdfColors.green500 : PdfColors.orange500,
                  shape: pw.BoxShape.circle,
                ),
              ),
              pw.SizedBox(width: 8),
              pw.Expanded(
                child: _chineseText(
                  '${_getTaskTypeText(task.template, task: task)} - ${task.id.length > 15 ? task.id.substring(0, 15) + '...' : task.id}',
                  fontSize: 11,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.grey800,
                ),
              ),
              _chineseText(
                task.isCompleted ? '已完成' : '进行中',
                fontSize: 9,
                color: task.isCompleted ? PdfColors.green600 : PdfColors.orange600,
                fontWeight: pw.FontWeight.bold,
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          
          // 详细信息网格
          pw.Row(
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    _buildDetailInfoRow('模板类型', _getTaskTypeText(task.template, task: task)),
                    pw.SizedBox(height: 4),
                    _buildDetailInfoRow('任务状态', task.isCompleted ? '✓ 已完成' : '⏳ 进行中'),
                    pw.SizedBox(height: 4),
                    _buildDetailInfoRow('总计数量', '${task.batches.fold(0, (sum, b) => sum + b.plannedQuantity)}托'),
                  ],
                ),
              ),
              pw.SizedBox(width: 16),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    _buildDetailInfoRow('拍照进度', '$completed/$totalRequired张'),
                    pw.SizedBox(height: 4),
                    _buildDetailInfoRow('验证通过', '${verified}张'),
                    pw.SizedBox(height: 4),
                    _buildDetailInfoRow('识别精度', '$accuracyText (MLKit引擎)'),
                  ],
                ),
              ),
            ],
          ),
          
          // 批次信息（如果有）
          if (batchInfo.isNotEmpty) ...[
            pw.SizedBox(height: 8),
            pw.Divider(color: PdfColors.grey300, height: 1),
            pw.SizedBox(height: 6),
            ...batchInfo.take(2).map((batch) => pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 3),
              child: _buildDetailInfoRow(
                '批次${batchInfo.indexOf(batch) + 1}',
                '${batch.productCode} 批号${batch.batchNumber} (${batch.plannedQuantity}托)',
              ),
            )).toList(),
            if (batchInfo.length > 2)
              _chineseText(
                '还有${batchInfo.length - 2}个批次',
                fontSize: 8,
                color: PdfColors.grey500,
              ),
          ],
        ],
      ),
    );
  }

  /// 构建任务基本信息卡片（类似APP任务详情页面的基本信息）
  pw.Widget _buildTaskBasicInfoCard(TaskModel task) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(18),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(10),
        border: pw.Border.all(color: PdfColors.blue200, width: 1.5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // 标题
          pw.Row(
            children: [
              pw.Container(
                padding: const pw.EdgeInsets.all(4),
                decoration: pw.BoxDecoration(
                  color: PdfColors.blue600,
                  borderRadius: pw.BorderRadius.circular(4),
                ),
                child: _chineseText('ℹ️', fontSize: 12, color: PdfColors.white),
              ),
              pw.SizedBox(width: 10),
              _chineseText('基本信息', fontSize: 16, fontWeight: pw.FontWeight.bold, color: PdfColors.blue800),
            ],
          ),
          pw.SizedBox(height: 15),
          
          // 基本信息网格（2列布局）
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // 左列
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    _buildBasicInfoRow('任务编号:', task.id),
                    pw.SizedBox(height: 8),
                    _buildBasicInfoRow('创建时间:', '${task.createdAt.year}-${task.createdAt.month.toString().padLeft(2, '0')}-${task.createdAt.day.toString().padLeft(2, '0')} ${task.createdAt.hour.toString().padLeft(2, '0')}:${task.createdAt.minute.toString().padLeft(2, '0')}'),
                  ],
                ),
              ),
              pw.SizedBox(width: 20),
              // 右列
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    _buildBasicInfoRow('任务类型:', _getTaskTypeText(task.template, task: task)),
                    pw.SizedBox(height: 8),
                    _buildBasicInfoRow('状态:', task.isCompleted ? '已完成' : '进行中'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建基本信息行
  pw.Widget _buildBasicInfoRow(String label, String value) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _chineseText(label, fontSize: 10, color: PdfColors.grey600),
        pw.SizedBox(height: 2),
        _chineseText(value, fontSize: 11, fontWeight: pw.FontWeight.normal, color: PdfColors.grey800),
      ],
    );
  }

  /// 构建首页任务状态卡片（含参与人员，风格与APP一致）
  pw.Widget _buildTaskStatusCard(TaskModel task) {
    final totalRequired = task.photos.length;
    final completed = task.photos.where((p) => p.imagePath != null && p.imagePath!.isNotEmpty).length;
    final verified = task.photos.where((p) => p.isVerified).length;
    final accuracyText = _getTaskAccuracyText(task);
    final batchInfo = task.batches.isNotEmpty ? task.batches : [];
    // 参与人员数据同步检查
    final List<String> participantIds = task.participants ?? [];
    // 从任务识别元数据中获取工作量分配信息
    final List<WorkerInfo> participants = [];
    if (task.recognitionMetadata != null && task.recognitionMetadata!['workload'] != null) {
      final workloadData = task.recognitionMetadata!['workload'] as Map<String, dynamic>;
      final records = (workloadData['records'] as List? ?? []);
      for (final record in records) {
        final recordMap = record as Map<String, dynamic>;
        participants.add(WorkerInfo(
          id: recordMap['workerId'] ?? '',
          name: recordMap['workerName'] ?? '未知',
          role: recordMap['role'] ?? '',
          warehouse: recordMap['warehouse'] ?? '',
          group: recordMap['group'] ?? '',
        ));
      }
    }
    return pw.Container(
      padding: const pw.EdgeInsets.all(14),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.blue200, width: 1),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            children: [
              pw.Container(
                padding: const pw.EdgeInsets.all(3),
                decoration: pw.BoxDecoration(
                  color: PdfColors.blue500,
                  borderRadius: pw.BorderRadius.circular(3),
                ),
                child: _chineseText('📋', fontSize: 10, color: PdfColors.white),
              ),
              pw.SizedBox(width: 8),
              _chineseText(_getTaskTypeText(task.template, task: task), fontSize: 12, fontWeight: pw.FontWeight.bold),
              pw.Spacer(),
              _chineseText(task.isCompleted ? '已完成' : '进行中', fontSize: 12, color: task.isCompleted ? PdfColors.green600 : PdfColors.orange600),
            ],
          ),
          pw.SizedBox(height: 8),
          _buildDetailInfoRow('创建时间', '${task.createdAt.year}-${task.createdAt.month.toString().padLeft(2, '0')}-${task.createdAt.day.toString().padLeft(2, '0')} ${task.createdAt.hour.toString().padLeft(2, '0')}:${task.createdAt.minute.toString().padLeft(2, '0')}'),
          _buildDetailInfoRow('计划数量', '${task.batches.fold(0, (sum, b) => sum + b.plannedQuantity)}托'),
          _buildDetailInfoRow('拍照进度', '$completed/$totalRequired张'),
          _buildDetailInfoRow('验证通过', '${verified}张'),
          _buildDetailInfoRow('识别精度', '$accuracyText (MLKit引擎)'),
          if (task.productCode.isNotEmpty)
            _buildDetailInfoRow('产品牌号', task.productCode),
          if (task.batchNumber.isNotEmpty)
            _buildDetailInfoRow('产品批号', task.batchNumber),
          if (participants.isNotEmpty) ...[
            pw.SizedBox(height: 8),
            _chineseText('参与人员', fontSize: 10, fontWeight: pw.FontWeight.bold),
            pw.Wrap(
              spacing: 6,
              runSpacing: 4,
              children: participants.map((w) => pw.Container(
                padding: const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                decoration: pw.BoxDecoration(
                  color: PdfColors.blue100,
                  borderRadius: pw.BorderRadius.circular(12),
                ),
                child: _chineseText(w.name, fontSize: 9, color: PdfColors.blue900),
              )).toList(),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建详细信息行
  pw.Widget _buildDetailInfoRow(String label, String value) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Container(
          width: 50,
          child: _chineseText(
            label,
            fontSize: 8,
            color: PdfColors.grey600,
          ),
        ),
        pw.Expanded(
          child: _chineseText(
            value,
            fontSize: 8,
            color: PdfColors.grey800,
            fontWeight: pw.FontWeight.normal,
          ),
        ),
      ],
    );
  }

  /// 构建任务表格
  pw.Widget _buildTaskTable(List<TaskModel> tasks) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      children: [
        // 表头
        pw.TableRow(
          decoration: pw.BoxDecoration(color: PdfColors.blue50),
          children: [
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('任务编号', fontWeight: pw.FontWeight.bold)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('类型', fontWeight: pw.FontWeight.bold)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('照片数', fontWeight: pw.FontWeight.bold)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('状态', fontWeight: pw.FontWeight.bold)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('完成进度', fontWeight: pw.FontWeight.bold)),
          ],
        ),
        // 数据行
        ...tasks.map((task) {
      final completedPhotos = task.photos.where((p) => p.imagePath != null).length;
      final totalPhotos = task.photos.length;
          final progress = totalPhotos > 0 ? ((completedPhotos / totalPhotos) * 100).toStringAsFixed(0) : '0';
          
          return pw.TableRow(
            children: [
              pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(task.id.length > 8 ? task.id.substring(0, 8) + '...' : task.id, fontSize: 10)),
              pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(_getTaskTypeText(task.template, task: task), fontSize: 10)),
              pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('$completedPhotos/$totalPhotos', fontSize: 10)),
              pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(task.isCompleted ? '已完成' : '进行中', fontSize: 10, color: task.isCompleted ? PdfColors.green600 : PdfColors.orange600)),
              pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('$progress%', fontSize: 10)),
            ],
          );
        }),
      ],
    );
  }

  /// 构建照片表格（支持分页序号）
  pw.Widget _buildPhotoTable(List<PhotoItem> photos, [int startIndex = 0]) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      children: [
        // 表头
        pw.TableRow(
          decoration: pw.BoxDecoration(color: PdfColors.orange50),
          children: [
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('序号', fontWeight: pw.FontWeight.bold)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('照片名称', fontWeight: pw.FontWeight.bold)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('类型', fontWeight: pw.FontWeight.bold)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('状态', fontWeight: pw.FontWeight.bold)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('验证方式', fontWeight: pw.FontWeight.bold)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('识别结果', fontWeight: pw.FontWeight.bold)),
          ],
        ),
        // 数据行（使用全局序号）
        ...photos.asMap().entries.map((entry) {
          final index = startIndex + entry.key + 1;
        final photo = entry.value;
          final photoType = photo.needRecognition == true ? 'AI识别' : '存证';
          
          // 更准确的状态判断逻辑
          String status;
          if (photo.imagePath?.isEmpty ?? true) {
            status = '待拍摄';
          } else if (photo.needRecognition == true) {
            // AI识别照片的状态判断
            if (photo.recognitionFailed) {
              status = '识别失败';
            } else if (photo.isVerified) {
              status = '验证通过';
            } else if (photo.isRecognitionCompleted) {
              status = '识别完成';
            } else {
              status = '待识别';
            }
          } else {
            // 存证照片直接显示已完成
            status = '已完成';
          }
          
          // 优化识别结果显示，支持多种数据源
          String recognitionResult = '无';
          if (photo.recognitionResult != null) {
            final result = photo.recognitionResult!;
            final List<String> resultParts = [];
            
            // 优先显示提取的产品代码和批号
            if (result.extractedProductCode?.isNotEmpty == true) {
              resultParts.add(result.extractedProductCode!);
            }
            if (result.extractedBatchNumber?.isNotEmpty == true) {
              resultParts.add(result.extractedBatchNumber!);
            }
            
            // 如果没有提取结果，尝试显示匹配的产品代码和批号
            if (resultParts.isEmpty) {
              if (photo.matchedProductCode?.isNotEmpty == true) {
                resultParts.add(photo.matchedProductCode!);
              }
              if (photo.matchedBatchNumber?.isNotEmpty == true) {
                resultParts.add(photo.matchedBatchNumber!);
              }
            }
            
            // 如果仍然没有结果，显示QR码内容或OCR文本的一部分
            if (resultParts.isEmpty) {
              if (result.qrCode?.isNotEmpty == true) {
                final qrText = result.qrCode!.length > 20 ? '${result.qrCode!.substring(0, 20)}...' : result.qrCode!;
                resultParts.add('QR:$qrText');
              } else if (result.ocrText?.isNotEmpty == true) {
                final ocrText = result.ocrText!.length > 20 ? '${result.ocrText!.substring(0, 20)}...' : result.ocrText!;
                resultParts.add('OCR:$ocrText');
              } else if (photo.ocrText?.isNotEmpty == true) {
                final ocrText = photo.ocrText!.length > 20 ? '${photo.ocrText!.substring(0, 20)}...' : photo.ocrText!;
                resultParts.add('OCR:$ocrText');
              }
            }
            
            if (resultParts.isNotEmpty) {
              recognitionResult = resultParts.join(' ');
            }
          }
          // 如果照片有图像但没有识别结果，显示状态信息
          else if (photo.imagePath?.isNotEmpty == true) {
            if (photo.needRecognition) {
              if (photo.recognitionFailed) {
                recognitionResult = '识别失败';
              } else if (photo.isRecognitionCompleted) {
                recognitionResult = '识别完成(无结果)';
              } else {
                recognitionResult = '等待识别';
              }
            } else {
              recognitionResult = '存证照片';
            }
          }
          
          return pw.TableRow(
            children: [
              pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('$index', fontSize: 10)),
              pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(photo.label, fontSize: 10)),
              pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(photoType, fontSize: 10)),
              pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(status, fontSize: 10)),
              pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(_getVerificationMethodText(photo), fontSize: 10)),
              pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(recognitionResult, fontSize: 10)),
            ],
          );
        }),
      ],
    );
  }

  /// 🔧 新增：异步检查文件是否存在
  Future<bool> _fileExists(String path) async {
    try {
      final file = File(path);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// 加载并调整图片大小 - 优化版本，移除路径搜索
  Future<pw.ImageProvider?> _loadAndResizeImage(String imagePath, {int maxWidth = 800, int maxHeight = 1200}) async {
    try {
      AppLogger.info('尝试加载图片: $imagePath');
      
      // 🚀 性能优化：直接使用提供的路径，不进行搜索
      final file = File(imagePath);
      if (!await file.exists()) {
        AppLogger.warning('图片文件不存在: $imagePath');
        return null;
      }
      
      AppLogger.info('图片文件存在，大小: ${await file.length()}字节');
      return await _processImageFile(file, maxWidth, maxHeight);
    } catch (e) {
      AppLogger.error('加载图片失败: $imagePath, 错误: $e');
      return null;
    }
  }
  
  /// 🚀 优化：处理图片文件的高效方法
  Future<pw.ImageProvider?> _processImageFile(File file, int maxWidth, int maxHeight) async {
    try {
      // 检查文件大小，限制为5MB
      final fileSize = await file.length();
      if (fileSize > 5 * 1024 * 1024) { // 5MB限制
        AppLogger.warning('图片文件过大: ${file.path} (${fileSize}字节)');
        return null;
      }
      
      if (fileSize == 0) {
        AppLogger.warning('图片文件为空: ${file.path}');
        return null;
      }

      // 🚀 性能优化：缩短超时时间，优先直接加载
      final bytes = await file.readAsBytes().timeout(
        Duration(seconds: 5), // 减少超时时间
        onTimeout: () {
          AppLogger.warning('图片读取超时: ${file.path}');
          throw TimeoutException('图片读取超时', Duration(seconds: 5));
        },
      );
      
      // 🚀 优先直接使用原始字节数据，避免重新编码
      try {
        AppLogger.info('直接加载图片: ${file.path}');
        return pw.MemoryImage(bytes);
      } catch (e) {
        AppLogger.warning('直接加载失败，尝试重新编码: $e');
        
        // 只在必要时进行图片解码和缩放
        final image = await Future.microtask(() => img.decodeImage(bytes)).timeout(
          Duration(seconds: 8), // 减少解码超时
          onTimeout: () {
            AppLogger.warning('图片解码超时: ${file.path}');
            return null;
          },
        );
        
        if (image == null) {
          AppLogger.warning('无法解码图片: ${file.path}');
          return null;
        }

        AppLogger.info('图片解码成功，原始尺寸: ${image.width}x${image.height}');

        // 🚀 性能优化：只在图片过大时才缩放
        var processedImage = image;
        if (image.width > maxWidth || image.height > maxHeight) {
          processedImage = await Future.microtask(() => img.copyResize(
            image,
            width: maxWidth,
            height: maxHeight,
            interpolation: img.Interpolation.linear,
          )).timeout(
            Duration(seconds: 5), // 减少缩放超时
            onTimeout: () {
              AppLogger.warning('图片缩放超时: ${file.path}');
              return image; // 超时时返回原图
            },
          );
          AppLogger.info('图片缩放完成，新尺寸: ${processedImage.width}x${processedImage.height}');
        }

        final resizedBytes = await Future.microtask(() => img.encodeJpg(processedImage, quality: 75)).timeout( // 降低质量提升速度
          Duration(seconds: 5),
          onTimeout: () {
            AppLogger.warning('图片编码超时: ${file.path}');
            return Uint8List(0);
          },
        );
        
        if (resizedBytes.isEmpty) {
          AppLogger.warning('图片编码失败: ${file.path}');
          return null;
        }
        
        AppLogger.info('图片处理完成: ${file.path}，最终大小: ${resizedBytes.length}字节');
        return pw.MemoryImage(resizedBytes is Uint8List ? resizedBytes : Uint8List.fromList(resizedBytes));
      }
    } catch (e) {
      AppLogger.error('处理图片失败: ${file.path}, 错误: $e');
      return null;
    }
  }

  /// 创建中文文本组件 - 修复乱码问题
  pw.Widget _chineseText(String text, {double fontSize = 12, pw.FontWeight fontWeight = pw.FontWeight.normal, PdfColor color = PdfColors.black}) {
    // 🔧 修复：清理文本中的特殊字符，防止乱码
    String cleanedText = text.replaceAll(RegExp(r'[^\u4e00-\u9fa5\u0030-\u0039\u0041-\u005a\u0061-\u007a\u0020-\u007e\u00a0-\u00ff\u3000-\u303f\uff00-\uffef]'), '');
    
    // 确保字体已初始化
    if (!_fontInitialized || _chineseFont == null || _boldChineseFont == null) {
      AppLogger.warning('字体未初始化，使用默认字体');
      // 🔧 修复：字体加载失败时，尝试重新初始化
      _initializeFonts().then((_) {
        AppLogger.info('字体重新初始化完成');
      });
      
      return pw.Text(
        cleanedText,
        style: pw.TextStyle(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
          font: fontWeight == pw.FontWeight.bold ? pw.Font.helveticaBold() : pw.Font.helvetica(),
        ),
      );
    }
    
    return pw.Text(
      cleanedText,
      style: pw.TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight,
        color: color,
        font: fontWeight == pw.FontWeight.bold ? _boldChineseFont : _chineseFont,
      ),
    );
  }

  /// 获取任务类型文本 - 智能判断混合任务
  String _getTaskTypeText(String template, {TaskModel? task}) {
    // 🔧 优先根据任务实际批次数量判断
    if (task != null && task.batches.length > 1) {
      if (template.contains('平板车')) {
        return '平板车混合任务';
      } else if (template.contains('集装箱')) {
        return '集装箱混合任务';
      } else {
        return '混合装运任务';
      }
    }
    
    // 处理中文模板名称和任务类型
    switch (template) {
      // 英文模板名称（向后兼容）
      case 'flatbed':
        return '平板车运输';
      case 'container':
        return '集装箱运输';
      case 'mixed':
        return '混合装运';
      
      // 中文模板名称（当前使用的）
      case '平板车':
        return '平板车-单批次任务';
      case '集装箱':
        return '集装箱-单批次任务';
      case '平板车混合':
        return '平板车混合任务';
      case '集装箱混合':
        return '集装箱混合任务';
      case '混合':
      case '混合装运':
        return '混合装运任务';
      
      // 其他可能的变体
      case '平板车运输':
        return '平板车-单批次任务';
      case '集装箱运输':
        return '集装箱-单批次任务';
        
      default:
        // 智能匹配：如果包含关键词，进行模糊匹配
        if (template.contains('平板车')) {
          if (template.contains('混合')) {
            return '平板车混合任务';
          }
          return '平板车-单批次任务';
        }
        if (template.contains('集装箱')) {
          if (template.contains('混合')) {
            return '集装箱混合任务';
          }
          return '集装箱-单批次任务';
        }
        if (template.contains('混合')) {
          return '混合装运任务';
        }
        
        // 如果完全无法识别，返回原始模板名称而不是"未知类型"
        return template.isNotEmpty ? template : '未知类型';
    }
  }
  
  /// 🔧 新增：判断是否为混装任务
  bool _isMixedTask(TaskModel task) {
    return task.batches.length > 1 || 
           task.template.contains('混合') || 
           task.template.contains('混装') ||
           task.template.contains('mixed');
  }
  
  /// 🔧 新增：添加混装任务批次详情页
  Future<void> _addMixedTaskBatchDetailsPage(pw.Document pdf, TaskModel task, int taskNumber) async {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // 页面标题
              pw.Container(
                padding: const pw.EdgeInsets.symmetric(vertical: 20),
                child: pw.Row(
                  children: [
                    pw.Container(
                      padding: const pw.EdgeInsets.all(8),
                      decoration: pw.BoxDecoration(
                        color: PdfColors.orange600,
                        borderRadius: pw.BorderRadius.circular(8),
                      ),
                      child: pw.Icon(
                        pw.IconData(0xe8b9), // inventory icon
                        color: PdfColors.white,
                        size: 16,
                      ),
                    ),
                    pw.SizedBox(width: 12),
                    pw.Expanded(
                      child: _chineseText(
                        '混装任务批次详情分析',
                        fontSize: 18,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColors.grey800,
                      ),
                    ),
                  ],
                ),
              ),
              
              // 任务概览卡片
              pw.Container(
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  color: PdfColors.orange50,
                  borderRadius: pw.BorderRadius.circular(8),
                  border: pw.Border.all(color: PdfColors.orange200),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    _chineseText('任务概览', fontSize: 16, fontWeight: pw.FontWeight.bold),
                    pw.SizedBox(height: 12),
                    pw.Row(
                      children: [
                        pw.Expanded(
                          child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              _buildMixedTaskInfoRow('任务类型', _getTaskTypeText(task.template, task: task)),
                              _buildMixedTaskInfoRow('批次总数', '${task.batches.length}个'),
                              _buildMixedTaskInfoRow('总计数量', '${task.quantity}托'),
                            ],
                          ),
                        ),
                        pw.Expanded(
                          child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              _buildMixedTaskInfoRow('总计重量', '${(task.quantity * _getStandardPalletWeight()).toStringAsFixed(1)}吨'),
                              _buildMixedTaskInfoRow('任务状态', task.isCompleted ? '✅ 已完成' : '🔄 进行中'),
                              _buildMixedTaskInfoRow('完成时间', task.completedAt != null 
                                ? '${task.completedAt!.year}-${task.completedAt!.month.toString().padLeft(2, '0')}-${task.completedAt!.day.toString().padLeft(2, '0')} ${task.completedAt!.hour.toString().padLeft(2, '0')}:${task.completedAt!.minute.toString().padLeft(2, '0')}'
                                : '进行中'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              pw.SizedBox(height: 20),
              
              // 批次分组表格
              _chineseText('各批次详细信息', fontSize: 16, fontWeight: pw.FontWeight.bold),
              pw.SizedBox(height: 12),
              _buildMixedTaskBatchTable(task),
              
              pw.SizedBox(height: 20),
              
              // 批次统计分析
              _buildMixedTaskStatistics(task),
              
              pw.Spacer(),
              
              // 页脚
              pw.Container(
                alignment: pw.Alignment.center,
                child: _chineseText(
                  '混装任务分析 | 任务${taskNumber} | ${DateTime.now().year}年${DateTime.now().month}月${DateTime.now().day}日',
                  fontSize: 10,
                  color: PdfColors.grey600,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
  
  /// 🔧 新增：构建混装任务信息行
  pw.Widget _buildMixedTaskInfoRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Container(
            width: 60,
            child: _chineseText(
              '$label:',
              fontSize: 12,
              color: PdfColors.grey700,
              fontWeight: pw.FontWeight.normal,
            ),
          ),
          pw.Expanded(
            child: _chineseText(
              value,
              fontSize: 12,
              color: PdfColors.grey900,
              fontWeight: pw.FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 🔧 新增：构建混装任务批次表格
  pw.Widget _buildMixedTaskBatchTable(TaskModel task) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FixedColumnWidth(30),
        1: const pw.FlexColumnWidth(2),
        2: const pw.FlexColumnWidth(2),
        3: const pw.FixedColumnWidth(60),
        4: const pw.FixedColumnWidth(60),
        5: const pw.FixedColumnWidth(80),
      },
      children: [
        // 表头
        pw.TableRow(
          decoration: pw.BoxDecoration(color: PdfColors.orange100),
          children: [
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('#', fontWeight: pw.FontWeight.bold, fontSize: 10)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('产品牌号', fontWeight: pw.FontWeight.bold, fontSize: 10)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('批次号', fontWeight: pw.FontWeight.bold, fontSize: 10)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('计划(托)', fontWeight: pw.FontWeight.bold, fontSize: 10)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('识别(托)', fontWeight: pw.FontWeight.bold, fontSize: 10)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('完成状态', fontWeight: pw.FontWeight.bold, fontSize: 10)),
          ],
        ),
        // 数据行
        ...List.generate(task.batches.length, (index) {
          final batch = task.batches[index];
          final completionRate = batch.completionRate * 100;
          final isCompleted = batch.isCompleted;
          
          return pw.TableRow(
            decoration: pw.BoxDecoration(
              color: index % 2 == 0 ? PdfColors.white : PdfColors.grey50,
            ),
            children: [
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: _chineseText('${index + 1}', fontSize: 9),
              ),
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: _chineseText(
                  _getDisplayProductCode(batch.productCode),
                  fontSize: 9,
                  fontWeight: pw.FontWeight.normal,
                ),
              ),
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: _chineseText(batch.batchNumber, fontSize: 9),
              ),
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: _chineseText('${batch.plannedQuantity}', fontSize: 9),
              ),
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: _chineseText(
                  '${batch.recognizedQuantity}',
                  fontSize: 9,
                  color: isCompleted ? PdfColors.green600 : PdfColors.orange600,
                  fontWeight: pw.FontWeight.normal,
                ),
              ),
              pw.Container(
                padding: const pw.EdgeInsets.all(8),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  children: [
                    _chineseText(
                      isCompleted ? '✅' : '⏳',
                      fontSize: 10,
                    ),
                    pw.SizedBox(width: 4),
                    _chineseText(
                      '${completionRate.toStringAsFixed(0)}%',
                      fontSize: 8,
                      color: isCompleted ? PdfColors.green600 : PdfColors.orange600,
                    ),
                  ],
                ),
              ),
            ],
          );
        }),
      ],
    );
  }
  
  /// 🔧 新增：构建混装任务统计分析
  pw.Widget _buildMixedTaskStatistics(TaskModel task) {
    final totalPlanned = task.batches.fold(0, (sum, batch) => sum + batch.plannedQuantity);
    final totalRecognized = task.batches.fold(0, (sum, batch) => sum + batch.recognizedQuantity);
    final completedBatches = task.batches.where((batch) => batch.isCompleted).length;
    final overallCompletionRate = totalPlanned > 0 ? (totalRecognized / totalPlanned) * 100 : 0;
    
    // 按产品牌号分组统计
    final Map<String, Map<String, int>> productStats = {};
    for (final batch in task.batches) {
      final code = _getDisplayProductCode(batch.productCode);
      productStats[code] ??= {'planned': 0, 'recognized': 0, 'batches': 0};
      productStats[code]!['planned'] = productStats[code]!['planned']! + batch.plannedQuantity;
      productStats[code]!['recognized'] = productStats[code]!['recognized']! + batch.recognizedQuantity;
      productStats[code]!['batches'] = productStats[code]!['batches']! + 1;
    }
    
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _chineseText('批次执行统计分析', fontSize: 16, fontWeight: pw.FontWeight.bold),
        pw.SizedBox(height: 12),
        
        // 整体统计卡片
        pw.Container(
          padding: const pw.EdgeInsets.all(16),
          decoration: pw.BoxDecoration(
            color: PdfColors.blue50,
            borderRadius: pw.BorderRadius.circular(8),
            border: pw.Border.all(color: PdfColors.blue200),
          ),
          child: pw.Row(
            children: [
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    _chineseText('批次完成度', fontSize: 12, fontWeight: pw.FontWeight.bold),
                    pw.SizedBox(height: 4),
                    _chineseText(
                      '$completedBatches/${task.batches.length}',
                      fontSize: 20,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.blue600,
                    ),
                    _chineseText('个批次', fontSize: 10, color: PdfColors.grey600),
                  ],
                ),
              ),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    _chineseText('数量完成度', fontSize: 12, fontWeight: pw.FontWeight.bold),
                    pw.SizedBox(height: 4),
                    _chineseText(
                      '$totalRecognized/$totalPlanned',
                      fontSize: 20,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.green600,
                    ),
                    _chineseText('托盘', fontSize: 10, color: PdfColors.grey600),
                  ],
                ),
              ),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.center,
                  children: [
                    _chineseText('整体进度', fontSize: 12, fontWeight: pw.FontWeight.bold),
                    pw.SizedBox(height: 4),
                    _chineseText(
                      '${overallCompletionRate.toStringAsFixed(1)}%',
                      fontSize: 20,
                      fontWeight: pw.FontWeight.bold,
                      color: overallCompletionRate >= 90 ? PdfColors.green600 : PdfColors.orange600,
                    ),
                    _chineseText('完成率', fontSize: 10, color: PdfColors.grey600),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        pw.SizedBox(height: 16),
        
        // 按产品分组统计
        if (productStats.isNotEmpty) ...[
          _chineseText('按产品牌号分组统计', fontSize: 14, fontWeight: pw.FontWeight.bold),
          pw.SizedBox(height: 8),
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey300),
            children: [
              // 表头
              pw.TableRow(
                decoration: pw.BoxDecoration(color: PdfColors.green100),
                children: [
                  pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('产品牌号', fontWeight: pw.FontWeight.bold, fontSize: 10)),
                  pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('批次数', fontWeight: pw.FontWeight.bold, fontSize: 10)),
                  pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('计划托数', fontWeight: pw.FontWeight.bold, fontSize: 10)),
                  pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('识别托数', fontWeight: pw.FontWeight.bold, fontSize: 10)),
                  pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('完成率', fontWeight: pw.FontWeight.bold, fontSize: 10)),
                ],
              ),
              // 数据行
              ...productStats.entries.map((entry) {
                final productCode = entry.key;
                final stats = entry.value;
                final productCompletionRate = stats['planned']! > 0 
                  ? (stats['recognized']! / stats['planned']!) * 100 
                  : 0;
                
                return pw.TableRow(
                  children: [
                    pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(productCode, fontSize: 9, fontWeight: pw.FontWeight.normal)),
                    pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('${stats['batches']}', fontSize: 9)),
                    pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('${stats['planned']}', fontSize: 9)),
                    pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('${stats['recognized']}', fontSize: 9)),
                    pw.Container(
                      padding: const pw.EdgeInsets.all(8),
                      child: _chineseText(
                        '${productCompletionRate.toStringAsFixed(1)}%',
                        fontSize: 9,
                        color: productCompletionRate >= 90 ? PdfColors.green600 : PdfColors.orange600,
                        fontWeight: pw.FontWeight.normal,
                      ),
                    ),
                  ],
                );
              }),
            ],
          ),
        ],
      ],
    );
  }
  
  /// 🔧 新增：获取显示用的产品牌号（去除前缀）
  String _getDisplayProductCode(String productCode) {
    // 去除常见的产品牌号前缀以便于显示
    if (productCode.startsWith('SAN-')) return productCode.replaceFirst('SAN-', '');
    if (productCode.startsWith('PS-')) return productCode.replaceFirst('PS-', '');
    return productCode;
  }

  /// 构建任务执行详情 - 根据任务类型定制
  pw.Widget _buildTaskExecutionDetails(TaskModel task, int completedPhotos, int verifiedPhotos, List<PhotoItem> recognitionPhotos) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: pw.Column(
        children: [
          // 任务基本信息
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              _chineseText('任务类型:', fontSize: 14, fontWeight: pw.FontWeight.bold),
              _chineseText(_getTaskTypeText(task.template, task: task), fontSize: 14),
            ],
          ),
          pw.SizedBox(height: 8),
          
          // 根据任务类型显示不同的信息
          if (_isMixedTask(task)) ...[
            // 混装任务特有信息
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                _chineseText('批次总数:', fontSize: 14, fontWeight: pw.FontWeight.bold),
                _chineseText('${task.batches.length}个批次', fontSize: 14, color: PdfColors.orange600),
              ],
            ),
            pw.SizedBox(height: 8),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                _chineseText('批次完成:', fontSize: 14, fontWeight: pw.FontWeight.bold),
                _chineseText('${task.batches.where((b) => b.isCompleted).length}/${task.batches.length}个', fontSize: 14, color: PdfColors.green600),
              ],
            ),
            pw.SizedBox(height: 8),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                _chineseText('总计重量:', fontSize: 14, fontWeight: pw.FontWeight.bold),
                _chineseText('${task.batches.fold<double>(0, (sum, b) => sum + (b.plannedQuantity * _getStandardPalletWeight())).toStringAsFixed(1)}吨', fontSize: 14, color: PdfColors.blue600),
              ],
            ),
          ] else ...[
            // 单批次任务特有信息
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                _chineseText('产品牌号:', fontSize: 14, fontWeight: pw.FontWeight.bold),
                _chineseText(task.productCode, fontSize: 14, color: PdfColors.purple600),
              ],
            ),
            pw.SizedBox(height: 8),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                _chineseText('批次号:', fontSize: 14, fontWeight: pw.FontWeight.bold),
                _chineseText(task.batchNumber, fontSize: 14, color: PdfColors.teal600),
              ],
            ),
            pw.SizedBox(height: 8),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                _chineseText('预估重量:', fontSize: 14, fontWeight: pw.FontWeight.bold),
                _chineseText('${(task.quantity * _getStandardPalletWeight()).toStringAsFixed(1)}吨', fontSize: 14, color: PdfColors.blue600),
              ],
            ),
          ],
          
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              _chineseText('计划数量:', fontSize: 14, fontWeight: pw.FontWeight.bold),
              _chineseText('${task.batches.fold(0, (sum, b) => sum + b.plannedQuantity)}托', fontSize: 14),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              _chineseText('任务状态:', fontSize: 14, fontWeight: pw.FontWeight.bold),
              _chineseText(task.isCompleted ? '✅ 已完成' : '🔄 进行中', fontSize: 14),
            ],
          ),
          
          pw.SizedBox(height: 15),
          pw.Divider(color: PdfColors.blue300),
          pw.SizedBox(height: 15),
          
          // 照片执行情况
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              _chineseText('照片已拍摄:', fontSize: 14),
              _chineseText('$completedPhotos/${task.photos.length}张', fontSize: 14, fontWeight: pw.FontWeight.bold),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              _chineseText('验证通过:', fontSize: 14),
              _chineseText('${verifiedPhotos}张', fontSize: 14, fontWeight: pw.FontWeight.bold, color: PdfColors.green600),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              _chineseText('识别准确率:', fontSize: 14),
              _chineseText('${recognitionPhotos.isNotEmpty ? ((verifiedPhotos / recognitionPhotos.length) * 100).toStringAsFixed(1) : "0.0"}%', fontSize: 14, fontWeight: pw.FontWeight.bold, color: PdfColors.purple600),
            ],
          ),
        ],
      ),
    );
  }
  /// 获取标准托盘重量（每托1.5吨 - 业务标准）
  double _getStandardPalletWeight() {
    return 1.5; // 每托货物1.5吨（业务固定标准）
  }
  String _getTaskAccuracyText(TaskModel task) {
    final recognitionPhotos = task.photos.where((p) => p.needRecognition && p.recognitionResult != null).toList();
    
    if (recognitionPhotos.isEmpty) {
      return '0.0%';
    }

    try {
      double totalConfidence = 0.0;
      int validPhotos = 0;

      for (final photo in recognitionPhotos) {
        try {
          final confidenceScore = ConfidenceEvaluationService.evaluateConfidence(
            result: photo.recognitionResult!,
            presetProductCode: photo.matchedProductCode,
            presetBatchNumber: photo.matchedBatchNumber,
          );
          totalConfidence += confidenceScore.finalScore;
          validPhotos++;
        } catch (e) {
          // 如果单个照片评估失败，跳过
          continue;
        }
      }

      if (validPhotos == 0) {
        return '0.0%';
      }

      final averageConfidence = totalConfidence / validPhotos;
      return '${(averageConfidence * 100).toStringAsFixed(1)}%';
    } catch (e) {
      // 如果全部评估失败，返回基于验证通过率的估算
      final verifiedPhotos = recognitionPhotos.where((p) => p.isVerified).length;
      final accuracy = recognitionPhotos.isNotEmpty ? (verifiedPhotos / recognitionPhotos.length) : 0.0;
      return '${(accuracy * 100).toStringAsFixed(1)}% (估算)';
    }
  }
  String _getRealConfidenceText(PhotoItem photo) {
    if (photo.recognitionResult == null || !photo.needRecognition) {
      return '不适用';
    }

    try {
      // 使用ConfidenceEvaluationService获取真实评估的置信度
      final confidenceScore = ConfidenceEvaluationService.evaluateConfidence(
        result: photo.recognitionResult!,
        presetProductCode: photo.matchedProductCode,
        presetBatchNumber: photo.matchedBatchNumber,
      );
      
      return '${(confidenceScore.finalScore * 100).toStringAsFixed(1)}%';
    } catch (e) {
      // 如果评估失败，返回原始置信度（但标注为原始值）
      final rawConfidence = photo.recognitionResult?.confidence ?? 0.0;
      final displayConfidence = rawConfidence > 1 ? rawConfidence : rawConfidence * 100;
      return '${displayConfidence.toStringAsFixed(1)}% (原始)';
    }
  }

  /// 获取验证状态文本
  String _getVerificationStatusText(PhotoItem photo) {
    if (photo.manualVerified) {
      return '人工确认通过';
    } else if (photo.isVerified) {
      return 'AI识别通过';
    } else if (photo.recognitionFailed) {
      return '识别失败';
    } else {
      return '待处理';
    }
  }

  /// 获取验证方式文本（更专业的显示逻辑）
  String _getVerificationMethodText(PhotoItem photo) {
    // 如果照片未拍摄
    if (photo.imagePath?.isEmpty ?? true) {
      return '未拍摄';
    }
    
    // 存证照片不需要验证
    if (photo.needRecognition != true) {
      return '存证照片';
    }
    
    // AI识别照片的验证方式
    if (photo.manualVerified) {
      return '人工确认';
    } else if (photo.isVerified) {
      return 'AI自动识别';
    } else if (photo.recognitionFailed) {
      return '识别失败';
    } else if (photo.isRecognitionCompleted) {
      return 'AI识别完成';
    } else {
      return '等待AI识别';
    }
  }

  /// 分享PDF文件
  Future<void> sharePdf(String filePath, {String? subject}) async {
    try {
      await Share.shareXFiles(
        [XFile(filePath)],
        subject: subject ?? '装运卫士任务报告',
        text: '装运卫士专业版任务识别报告',
      );
    } catch (e) {
      throw Exception('分享PDF失败: $e');
    }
  }

  /// 保存PDF到文件
  Future<String> savePdfToFile(Uint8List pdfData, String filename) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$filename');
      await file.writeAsBytes(pdfData);
      AppLogger.info('PDF保存成功: ${file.path}');
      return file.path;
    } catch (e) {
      AppLogger.error('保存PDF失败: $e');
      throw Exception('保存PDF失败: $e');
    }
  }

  /// 生成包含参与人员和工作量统计的完整PDF报告
  Future<Uint8List> generateFullReportWithWorkerStats(
    List<TaskModel> tasks, 
    String title, {
    bool includePhotos = true,
    bool includeStatistics = true,
    bool includeWorkerStatistics = true,
  }) async {
    return await generateTaskReport(
      tasks, 
      title,
      includePhotos: includePhotos,
      includeStatistics: includeStatistics,
      includeWorkerStatistics: includeWorkerStatistics,
    );
  }

  /// 生成识别报告（JSON格式）
  Future<String> generateRecognitionReport(List<TaskModel> tasks, String title) async {
    try {
      final report = {
        'title': title,
        'generatedAt': DateTime.now().toIso8601String(),
        'tasks': tasks.map((task) => {
          'id': task.id,
          'template': task.template,
          'imagePath': task.imagePath,
          'status': task.status.toString(),
          'result': task.result?.toMap(),
          'createdAt': task.createdAt.toIso8601String(),
          'completedAt': task.completedAt?.toIso8601String(),
          'photos': task.photos.map((photo) => {
            'label': photo.label,
            'imagePath': photo.imagePath,
            'needRecognition': photo.needRecognition,
            'isVerified': photo.isVerified,
            'manualVerified': photo.manualVerified,
            'recognitionFailed': photo.recognitionFailed,
            'recognitionResult': photo.recognitionResult?.toMap(),
          }).toList(),
        }).toList(),
      };

      final json = JsonEncoder.withIndent('  ').convert(report);
    final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/recognition_report_${DateTime.now().millisecondsSinceEpoch}.json');
      
      await file.writeAsString(json);
      return file.path;
    } catch (e) {
      throw Exception('生成识别报告失败: $e');
    }
  }

  /// 生成统计报告（文本格式）
  Future<String> generateStatisticsReport(List<TaskModel> tasks) async {
    try {
      final totalTasks = tasks.length;
      final completedTasks = tasks.where((t) => t.isCompleted).length;
      final allPhotos = tasks.expand((task) => task.photos).toList();
      final totalPhotos = allPhotos.length;
      final completedPhotos = allPhotos.where((p) => p.imagePath != null).length;
      final recognitionPhotos = allPhotos.where((p) => p.needRecognition == true).length;
      final verifiedPhotos = allPhotos.where((p) => p.isVerified).length;
      final accuracyRate = recognitionPhotos > 0 ? (verifiedPhotos / recognitionPhotos * 100) : 0;

      final report = '''
装运卫士专业版 - 识别统计报告
============================

生成时间: ${DateTime.now().toString()}

任务统计:
- 总任务数: $totalTasks
- 已完成任务: $completedTasks
- 完成率: ${totalTasks > 0 ? (completedTasks / totalTasks * 100).toStringAsFixed(1) : 0}%

照片统计:
- 总照片数: $totalPhotos
- 已拍摄照片: $completedPhotos
- AI识别照片: $recognitionPhotos
- 识别成功照片: $verifiedPhotos
- 识别准确率: ${accuracyRate.toStringAsFixed(1)}%

技术信息:
- 识别算法: 12种专业算法(4种基础+8种高级)
- 处理方式: 完全本地处理
- 数据安全: 本地存储，无需网络
- 质量保证: 动态置信度评估 + 人工确认

报告生成: 装运卫士专业版 ML Kit V2
''';

      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/statistics_report_${DateTime.now().millisecondsSinceEpoch}.txt');
      
      await file.writeAsString(report);
      return file.path;
    } catch (e) {
      throw Exception('生成统计报告失败: $e');
    }
  }

  /// 构建本车参与人员工作量分配表格 - 修复数据显示问题
  pw.Widget _buildWorkloadTable(TaskModel task) {
    // 🔧 修复：从多个数据源获取分配数据
    List<Map<String, dynamic>> records = [];
    
    // 数据源1：从recognitionMetadata['workload']获取分配数据
    if (task.recognitionMetadata != null && task.recognitionMetadata!['workload'] != null) {
      final workloadData = task.recognitionMetadata!['workload'];
      if (workloadData['records'] != null) {
        records = List<Map<String, dynamic>>.from(workloadData['records']);
      }
    }
    
    // 🔧 修复：如果没有工作量数据，生成基于参与人员的默认数据
    if (records.isEmpty && task.participants.isNotEmpty) {
      final totalTonnage = task.batches.fold<double>(0, (sum, batch) => sum + (batch.plannedQuantity * _getStandardPalletWeight()));
      final perPersonTonnage = totalTonnage / task.participants.length;
      
      records = task.participants.map((participantId) {
        return {
          'workerName': participantId,
          'warehouse': '1号库',
          'role': '装运员',
          'group': '默认组',
          'allocatedTonnage': perPersonTonnage,
        };
      }).toList();
    }
    
    // 🔧 修复：如果仍然没有数据，显示批次信息
    if (records.isEmpty && task.batches.isNotEmpty) {
      return pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          _chineseText('批次信息', fontSize: 12, fontWeight: pw.FontWeight.bold),
          pw.SizedBox(height: 8),
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey300),
            children: [
              // 表头
              pw.TableRow(
                decoration: pw.BoxDecoration(color: PdfColors.blue50),
                children: [
                  pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('产品牌号', fontWeight: pw.FontWeight.bold)),
                  pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('批次号', fontWeight: pw.FontWeight.bold)),
                  pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('计划数量', fontWeight: pw.FontWeight.bold)),
                  pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('预估吨数', fontWeight: pw.FontWeight.bold)),
                ],
              ),
              // 数据行
              ...task.batches.map((batch) => pw.TableRow(
                children: [
                  pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(batch.productCode, fontSize: 10)),
                  pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(batch.batchNumber, fontSize: 10)),
                  pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('${batch.plannedQuantity}托', fontSize: 10)),
                  pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('${(batch.plannedQuantity * _getStandardPalletWeight()).toStringAsFixed(1)}吨', fontSize: 10)),
                ],
              )),
            ],
          ),
        ],
      );
    }
    
    if (records.isEmpty) {
      return _chineseText('暂无工作量分配数据', fontSize: 12, color: PdfColors.grey600);
    }
    
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      children: [
        // 表头
        pw.TableRow(
          decoration: pw.BoxDecoration(color: PdfColors.blue50),
          children: [
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('姓名', fontWeight: pw.FontWeight.bold)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('库区', fontWeight: pw.FontWeight.bold)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('角色', fontWeight: pw.FontWeight.bold)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('小组', fontWeight: pw.FontWeight.bold)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText('吨数', fontWeight: pw.FontWeight.bold)),
          ],
        ),
        // 数据行
        ...records.map((record) => pw.TableRow(
          children: [
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(record['workerName'] ?? '', fontSize: 10)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(record['warehouse'] ?? '', fontSize: 10)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(record['role'] ?? '', fontSize: 10)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText(record['group'] ?? '', fontSize: 10)),
            pw.Container(padding: const pw.EdgeInsets.all(8), child: _chineseText((record['allocatedTonnage'] ?? 0).toStringAsFixed(1), fontSize: 10)),
          ],
        )),
      ],
    );
  }

  Future<Uint8List> generatePdfReport(TaskModel task) async {
    try {
      AppLogger.info('🔧 开始生成PDF报告');
      
      // 🔧 修复：直接从TaskService获取最新的当前任务数据
      final taskService = TaskService();
      await taskService.initialize();
      
      // 获取最新的任务数据
      final latestTask = taskService.currentTask ?? task;
      AppLogger.info('PDF生成使用数据源: ${latestTask.template}');
      AppLogger.info('任务照片总数: ${latestTask.photos.length}');
      
      // 详细验证照片数据
      for (int i = 0; i < latestTask.photos.length; i++) {
        final photo = latestTask.photos[i];
        AppLogger.info('PDF验证照片${i + 1}: ${photo.label} - 路径: ${photo.imagePath ?? "null"}');
      }
      
      // 使用最新数据生成PDF
      return await generateTaskReport([latestTask], '任务报告');
    } catch (e) {
      AppLogger.error('生成PDF报告失败: $e');
      // 返回空的PDF以避免编译错误
      final pdf = pw.Document();
      pdf.addPage(pw.Page(
        build: (pw.Context context) => pw.Center(
          child: _chineseText('PDF生成失败: $e'),
        ),
      ));
      return await pdf.save();
    }
  }
}

 