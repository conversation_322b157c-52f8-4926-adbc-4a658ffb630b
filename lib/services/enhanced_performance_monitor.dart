import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/services/hive_storage_service.dart';

/// 🚀 增强性能监控服务 - 升级后的性能监控
/// 
/// 监控应用性能指标，包括内存使用、CPU占用、网络请求等
/// 使用Hive存储性能数据，支持历史数据分析
class EnhancedPerformanceMonitor {
  static final EnhancedPerformanceMonitor _instance = EnhancedPerformanceMonitor._internal();
  factory EnhancedPerformanceMonitor() => _instance;
  EnhancedPerformanceMonitor._internal();

  Timer? _monitoringTimer;
  bool _isMonitoring = false;
  
  // 性能指标
  final List<PerformanceMetric> _metrics = [];
  static const int _maxMetrics = 1000; // 最多保存1000个指标
  
  // 监控间隔
  static const Duration _monitoringInterval = Duration(seconds: 30);
  
  /// 开始性能监控
  Future<void> startMonitoring() async {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    AppLogger.info('🚀 开始性能监控');
    
    _monitoringTimer = Timer.periodic(_monitoringInterval, (timer) {
      _collectMetrics();
    });
    
    // 立即收集一次指标
    await _collectMetrics();
  }
  
  /// 停止性能监控
  void stopMonitoring() {
    if (!_isMonitoring) return;
    
    _isMonitoring = false;
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    
    AppLogger.info('⏹️ 停止性能监控');
  }
  
  /// 收集性能指标
  Future<void> _collectMetrics() async {
    try {
      final metric = PerformanceMetric(
        timestamp: DateTime.now(),
        memoryUsage: await _getMemoryUsage(),
        cpuUsage: await _getCpuUsage(),
        batteryLevel: await _getBatteryLevel(),
        networkLatency: await _getNetworkLatency(),
        frameRate: await _getFrameRate(),
      );
      
      _metrics.add(metric);
      
      // 限制指标数量
      if (_metrics.length > _maxMetrics) {
        _metrics.removeAt(0);
      }
      
      // 保存到Hive
      await _saveMetricToHive(metric);
      
      // 检查性能警告
      _checkPerformanceWarnings(metric);
      
    } catch (e) {
      AppLogger.error('收集性能指标失败: $e');
    }
  }
  
  /// 获取内存使用情况
  Future<double> _getMemoryUsage() async {
    try {
      if (Platform.isAndroid) {
        // Android内存监控
        final result = await const MethodChannel('loadguard/performance')
            .invokeMethod('getMemoryUsage');
        return (result as num).toDouble();
      } else {
        // iOS或其他平台的内存监控
        return 0.0;
      }
    } catch (e) {
      AppLogger.warning('获取内存使用失败: $e');
      return 0.0;
    }
  }
  
  /// 获取CPU使用率
  Future<double> _getCpuUsage() async {
    try {
      if (Platform.isAndroid) {
        final result = await const MethodChannel('loadguard/performance')
            .invokeMethod('getCpuUsage');
        return (result as num).toDouble();
      } else {
        return 0.0;
      }
    } catch (e) {
      AppLogger.warning('获取CPU使用率失败: $e');
      return 0.0;
    }
  }
  
  /// 获取电池电量
  Future<double> _getBatteryLevel() async {
    try {
      final result = await const MethodChannel('loadguard/performance')
          .invokeMethod('getBatteryLevel');
      return (result as num).toDouble();
    } catch (e) {
      AppLogger.warning('获取电池电量失败: $e');
      return 100.0;
    }
  }
  
  /// 获取网络延迟
  Future<double> _getNetworkLatency() async {
    try {
      final stopwatch = Stopwatch()..start();
      
      // 简单的网络延迟测试
      await InternetAddress.lookup('google.com');
      
      stopwatch.stop();
      return stopwatch.elapsedMilliseconds.toDouble();
    } catch (e) {
      AppLogger.warning('获取网络延迟失败: $e');
      return -1.0; // -1表示网络不可用
    }
  }
  
  /// 获取帧率
  Future<double> _getFrameRate() async {
    try {
      // 这里可以集成更复杂的帧率监控
      // 目前返回固定值
      return 60.0;
    } catch (e) {
      AppLogger.warning('获取帧率失败: $e');
      return 0.0;
    }
  }
  
  /// 保存指标到Hive
  Future<void> _saveMetricToHive(PerformanceMetric metric) async {
    try {
      final key = 'performance_${metric.timestamp.millisecondsSinceEpoch}';
      await HiveStorageService.saveCache(
        key, 
        metric.toJson(),
        expiry: const Duration(days: 7), // 保存7天
      );
    } catch (e) {
      AppLogger.error('保存性能指标到Hive失败: $e');
    }
  }
  
  /// 检查性能警告
  void _checkPerformanceWarnings(PerformanceMetric metric) {
    // 内存使用过高警告
    if (metric.memoryUsage > 200.0) { // 200MB
      AppLogger.warning('⚠️ 内存使用过高: ${metric.memoryUsage.toStringAsFixed(1)}MB');
    }
    
    // CPU使用过高警告
    if (metric.cpuUsage > 80.0) { // 80%
      AppLogger.warning('⚠️ CPU使用过高: ${metric.cpuUsage.toStringAsFixed(1)}%');
    }
    
    // 电池电量低警告
    if (metric.batteryLevel < 20.0) { // 20%
      AppLogger.warning('⚠️ 电池电量低: ${metric.batteryLevel.toStringAsFixed(1)}%');
    }
    
    // 网络延迟高警告
    if (metric.networkLatency > 1000.0) { // 1秒
      AppLogger.warning('⚠️ 网络延迟高: ${metric.networkLatency.toStringAsFixed(0)}ms');
    }
  }
  
  /// 获取性能统计
  PerformanceStats getPerformanceStats() {
    if (_metrics.isEmpty) {
      return PerformanceStats.empty();
    }
    
    final memoryUsages = _metrics.map((m) => m.memoryUsage).toList();
    final cpuUsages = _metrics.map((m) => m.cpuUsage).toList();
    final networkLatencies = _metrics.where((m) => m.networkLatency >= 0).map((m) => m.networkLatency).toList();
    
    return PerformanceStats(
      avgMemoryUsage: memoryUsages.reduce((a, b) => a + b) / memoryUsages.length,
      maxMemoryUsage: memoryUsages.reduce((a, b) => a > b ? a : b),
      avgCpuUsage: cpuUsages.reduce((a, b) => a + b) / cpuUsages.length,
      maxCpuUsage: cpuUsages.reduce((a, b) => a > b ? a : b),
      avgNetworkLatency: networkLatencies.isEmpty ? 0.0 : networkLatencies.reduce((a, b) => a + b) / networkLatencies.length,
      maxNetworkLatency: networkLatencies.isEmpty ? 0.0 : networkLatencies.reduce((a, b) => a > b ? a : b),
      currentBatteryLevel: _metrics.last.batteryLevel,
      metricsCount: _metrics.length,
    );
  }
  
  /// 清理旧的性能数据
  Future<void> cleanupOldMetrics() async {
    try {
      await HiveStorageService.cleanExpiredCache();
      AppLogger.info('🧹 清理旧的性能数据完成');
    } catch (e) {
      AppLogger.error('清理性能数据失败: $e');
    }
  }
  
  /// 释放资源
  void dispose() {
    stopMonitoring();
    _metrics.clear();
  }
}

/// 性能指标数据类
class PerformanceMetric {
  final DateTime timestamp;
  final double memoryUsage; // MB
  final double cpuUsage; // %
  final double batteryLevel; // %
  final double networkLatency; // ms
  final double frameRate; // fps
  
  PerformanceMetric({
    required this.timestamp,
    required this.memoryUsage,
    required this.cpuUsage,
    required this.batteryLevel,
    required this.networkLatency,
    required this.frameRate,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'memoryUsage': memoryUsage,
      'cpuUsage': cpuUsage,
      'batteryLevel': batteryLevel,
      'networkLatency': networkLatency,
      'frameRate': frameRate,
    };
  }
  
  factory PerformanceMetric.fromJson(Map<String, dynamic> json) {
    return PerformanceMetric(
      timestamp: DateTime.parse(json['timestamp']),
      memoryUsage: (json['memoryUsage'] as num).toDouble(),
      cpuUsage: (json['cpuUsage'] as num).toDouble(),
      batteryLevel: (json['batteryLevel'] as num).toDouble(),
      networkLatency: (json['networkLatency'] as num).toDouble(),
      frameRate: (json['frameRate'] as num).toDouble(),
    );
  }
}

/// 性能统计数据类
class PerformanceStats {
  final double avgMemoryUsage;
  final double maxMemoryUsage;
  final double avgCpuUsage;
  final double maxCpuUsage;
  final double avgNetworkLatency;
  final double maxNetworkLatency;
  final double currentBatteryLevel;
  final int metricsCount;
  
  PerformanceStats({
    required this.avgMemoryUsage,
    required this.maxMemoryUsage,
    required this.avgCpuUsage,
    required this.maxCpuUsage,
    required this.avgNetworkLatency,
    required this.maxNetworkLatency,
    required this.currentBatteryLevel,
    required this.metricsCount,
  });
  
  factory PerformanceStats.empty() {
    return PerformanceStats(
      avgMemoryUsage: 0.0,
      maxMemoryUsage: 0.0,
      avgCpuUsage: 0.0,
      maxCpuUsage: 0.0,
      avgNetworkLatency: 0.0,
      maxNetworkLatency: 0.0,
      currentBatteryLevel: 100.0,
      metricsCount: 0,
    );
  }
}
