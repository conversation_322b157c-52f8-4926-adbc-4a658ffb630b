import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:loadguard/services/logging_service.dart';
import 'package:loadguard/services/performance_optimizer.dart';

/// 🚨 错误处理服务
/// 完善异常捕获和用户友好的错误提示
class ErrorHandlerService {
  static final ErrorHandlerService _instance = ErrorHandlerService._internal();
  factory ErrorHandlerService() => _instance;
  ErrorHandlerService._internal();

  static const String _tag = 'ErrorHandler';

  // 错误处理配置
  static const Duration _retryDelay = Duration(seconds: 2);
  static const int _maxRetryAttempts = 3;
  static const Duration _errorDisplayDuration = Duration(seconds: 5);

  // 错误统计
  final Map<String, int> _errorCounts = {};
  final List<ErrorRecord> _errorHistory = [];
  final int _maxErrorHistory = 100;

  // 重试机制
  final Map<String, int> _retryCounts = {};
  final Map<String, Timer> _retryTimers = {};

  // 全局错误处理器
  FlutterErrorHandler? _flutterErrorHandler;

  /// 初始化错误处理服务
  Future<void> initialize() async {
    LoggingService.info('🚨 初始化错误处理服务', tag: _tag);

    try {
      // 设置Flutter错误处理器
      _setupFlutterErrorHandler();

      // 设置平台通道错误处理
      _setupPlatformErrorHandler();

      // 设置未捕获异常处理
      _setupUncaughtExceptionHandler();

      LoggingService.info('✅ 错误处理服务初始化完成', tag: _tag);
    } catch (e) {
      LoggingService.error('❌ 错误处理服务初始化失败', error: e, tag: _tag);
      rethrow;
    }
  }

  /// 安全执行操作（带重试机制）
  static Future<T> safeExecute<T>(
    Future<T> Function() operation, {
    String? operationName,
    int maxRetries = _maxRetryAttempts,
    Duration? retryDelay,
    String? userFriendlyMessage,
  }) async {
    int attempts = 0;

    while (attempts < maxRetries) {
      try {
        attempts++;

        LoggingService.info(
            '🔄 执行操作: ${operationName ?? 'unnamed'} (尝试 $attempts/$maxRetries)',
            tag: _tag);

        // 在后台线程执行操作
        final result = await PerformanceOptimizer.runInBackground(
          operation,
          operationName: operationName,
        );

        // 成功执行，清除重试计数
        _instance._clearRetryCount(operationName ?? 'unknown');

        LoggingService.info('✅ 操作执行成功: ${operationName ?? 'unnamed'}',
            tag: _tag);
        return result;
      } catch (e, stackTrace) {
        LoggingService.error(
          '❌ 操作执行失败: ${operationName ?? 'unnamed'} (尝试 $attempts/$maxRetries)',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );

        // 记录错误
        _instance._recordError(operationName ?? 'unknown', e, stackTrace);

        // 检查是否还有重试机会
        if (attempts >= maxRetries) {
          // 达到最大重试次数，抛出用户友好的错误
          final errorMessage =
              userFriendlyMessage ?? _getUserFriendlyMessage(e);
          throw UserFriendlyException(errorMessage, e);
        }

        // 等待后重试
        final delay = retryDelay ?? _retryDelay * attempts; // 递增延迟
        LoggingService.info('⏳ 等待 ${delay.inSeconds} 秒后重试...', tag: _tag);
        await Future.delayed(delay);
      }
    }

    // 这里不应该到达，但为了类型安全
    throw Exception('操作执行失败，已达到最大重试次数');
  }

  /// 显示用户友好的错误提示
  static void showUserFriendlyError(
    BuildContext context,
    dynamic error, {
    String? title,
    Duration? duration,
    VoidCallback? onDismiss,
  }) {
    final errorMessage = _getUserFriendlyMessage(error);
    final errorTitle = title ?? '操作失败';

    LoggingService.info('📱 显示用户友好错误: $errorTitle - $errorMessage', tag: _tag);

    // 使用SnackBar显示错误
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    errorTitle,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(errorMessage),
                ],
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red[600],
        duration: duration ?? _errorDisplayDuration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        action: SnackBarAction(
          label: '确定',
          textColor: Colors.white,
          onPressed: onDismiss ?? () {},
        ),
      ),
    );
  }

  /// 显示网络错误提示
  static void showNetworkError(BuildContext context) {
    showUserFriendlyError(
      context,
      '网络连接错误',
      title: '网络问题',
    );
  }

  /// 显示权限错误提示
  static void showPermissionError(BuildContext context, String permission) {
    showUserFriendlyError(
      context,
      '权限不足',
      title: '权限问题',
    );
  }

  /// 显示存储空间错误提示
  static void showStorageError(BuildContext context) {
    showUserFriendlyError(
      context,
      '存储空间不足',
      title: '存储问题',
    );
  }

  /// 设置Flutter错误处理器
  void _setupFlutterErrorHandler() {
    _flutterErrorHandler = (FlutterErrorDetails details) {
      LoggingService.error(
        'Flutter错误: ${details.exception}',
        error: details.exception,
        stackTrace: details.stack,
        tag: _tag,
      );

      // 记录错误
      _recordError('flutter_error', details.exception, details.stack);

      // 显示用户友好的错误提示
      _showGlobalError(details.exception);
    };

    FlutterError.onError = _flutterErrorHandler!;
  }

  /// 设置平台通道错误处理
  void _setupPlatformErrorHandler() {
    SystemChannels.platform.setMethodCallHandler((call) async {
      try {
        // 处理平台调用
        return null;
      } catch (e, stackTrace) {
        LoggingService.error(
          '平台通道错误: ${call.method}',
          error: e,
          stackTrace: stackTrace,
          tag: _tag,
        );

        _recordError('platform_error', e, stackTrace);
        return null;
      }
    });
  }

  /// 设置未捕获异常处理
  void _setupUncaughtExceptionHandler() {
    // 在Dart中，未捕获的异常会自动被Flutter框架处理
    // 这里主要是为了记录和监控
  }

  /// 记录错误
  void _recordError(String operation, dynamic error, StackTrace? stackTrace) {
    // 更新错误计数
    _errorCounts[operation] = (_errorCounts[operation] ?? 0) + 1;

    // 添加到错误历史
    _errorHistory.add(ErrorRecord(
      operation: operation,
      error: error.toString(),
      timestamp: DateTime.now(),
      stackTrace: stackTrace?.toString(),
    ));

    // 限制错误历史大小
    if (_errorHistory.length > _maxErrorHistory) {
      _errorHistory.removeAt(0);
    }
  }

  /// 清除重试计数
  void _clearRetryCount(String operation) {
    _retryCounts.remove(operation);
    _retryTimers[operation]?.cancel();
    _retryTimers.remove(operation);
  }

  /// 显示全局错误
  void _showGlobalError(dynamic error) {
    // 这里可以实现全局错误显示逻辑
    // 比如显示一个全局的错误通知
    LoggingService.warning('全局错误: $error', tag: _tag);
  }

  /// 获取用户友好的错误消息
  static String _getUserFriendlyMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') || errorString.contains('connection')) {
      return '网络连接错误，请检查网络后重试';
    } else if (errorString.contains('permission') ||
        errorString.contains('access')) {
      return '权限不足，请检查应用权限设置';
    } else if (errorString.contains('storage') ||
        errorString.contains('space')) {
      return '存储空间不足，请清理存储空间';
    } else if (errorString.contains('timeout')) {
      return '操作超时，请稍后重试';
    } else if (errorString.contains('file') || errorString.contains('path')) {
      return '文件操作失败，请重试';
    } else if (errorString.contains('camera')) {
      return '相机访问失败，请检查相机权限';
    } else if (errorString.contains('license') ||
        errorString.contains('activation')) {
      return '许可证验证失败，请联系技术支持';
    } else {
      return '操作失败，请稍后重试';
    }
  }

  /// 获取错误统计
  Map<String, dynamic> getErrorStatistics() {
    return {
      'errorCounts': Map<String, int>.from(_errorCounts),
      'totalErrors': _errorHistory.length,
      'recentErrors': _errorHistory.take(10).map((e) => e.toMap()).toList(),
    };
  }

  /// 清除错误历史
  void clearErrorHistory() {
    _errorCounts.clear();
    _errorHistory.clear();
    LoggingService.info('🗑️ 错误历史已清除', tag: _tag);
  }

  /// 释放资源
  void dispose() {
    // 取消所有重试定时器
    for (final timer in _retryTimers.values) {
      timer.cancel();
    }
    _retryTimers.clear();

    LoggingService.info('🔄 错误处理服务已释放', tag: _tag);
  }
}

/// 用户友好的异常类
class UserFriendlyException implements Exception {
  final String userMessage;
  final dynamic originalError;

  UserFriendlyException(this.userMessage, this.originalError);

  @override
  String toString() => userMessage;
}

/// 错误记录
class ErrorRecord {
  final String operation;
  final String error;
  final DateTime timestamp;
  final String? stackTrace;

  ErrorRecord({
    required this.operation,
    required this.error,
    required this.timestamp,
    this.stackTrace,
  });

  Map<String, dynamic> toMap() {
    return {
      'operation': operation,
      'error': error,
      'timestamp': timestamp.toIso8601String(),
      'stackTrace': stackTrace,
    };
  }
}

/// Flutter错误处理器类型
typedef FlutterErrorHandler = void Function(FlutterErrorDetails details);
