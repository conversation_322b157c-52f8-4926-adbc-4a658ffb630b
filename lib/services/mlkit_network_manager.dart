import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🌐 ML Kit 网络管理器
/// 检测网络状态，确保离线模型优先使用
class MLKitNetworkManager {
  static final MLKitNetworkManager _instance = MLKitNetworkManager._internal();
  factory MLKitNetworkManager() => _instance;
  MLKitNetworkManager._internal();

  bool _isOnline = false;
  bool _hasCheckedNetwork = false;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  /// 🔍 检查网络连接状态
  Future<bool> checkNetworkConnection() async {
    if (_hasCheckedNetwork) return _isOnline;

    try {
      AppLogger.info('🔍 检查网络连接状态...');

      // 检查连接类型
      final connectivityResult = await Connectivity().checkConnectivity();

      if (connectivityResult.contains(ConnectivityResult.none)) {
        _isOnline = false;
        AppLogger.info('📴 网络离线，将使用离线模型');
      } else {
        // 进一步检查实际网络可达性
        _isOnline = await _checkInternetReachability();

        if (_isOnline) {
          AppLogger.info('🌐 网络在线，可以下载模型（如果需要）');
        } else {
          AppLogger.info('⚠️ 网络连接不稳定，优先使用离线模型');
        }
      }

      _hasCheckedNetwork = true;
      return _isOnline;
    } catch (e) {
      AppLogger.error('❌ 网络检查失败: $e');
      _isOnline = false;
      return false;
    }
  }

  /// 🔗 检查实际网络可达性
  Future<bool> _checkInternetReachability() async {
    try {
      // 尝试连接 Google 服务器（用于模型下载）
      final result = await InternetAddress.lookup('dl.google.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        return true;
      }

      // 如果 Google 不可达，尝试其他服务器
      final result2 = await InternetAddress.lookup('www.baidu.com');
      return result2.isNotEmpty && result2[0].rawAddress.isNotEmpty;
    } catch (e) {
      AppLogger.error('❌ 网络可达性检查失败: $e');
      return false;
    }
  }

  /// 📡 监听网络状态变化
  void startNetworkMonitoring() {
    _connectivitySubscription =
        Connectivity().onConnectivityChanged.listen((List<ConnectivityResult> result) {
      _hasCheckedNetwork = false; // 重置检查状态
      checkNetworkConnection();
    });
  }

  /// 🛑 停止网络监听
  void stopNetworkMonitoring() {
    _connectivitySubscription?.cancel();
    _connectivitySubscription = null;
  }

  /// 🔒 强制使用离线模式
  void forceOfflineMode() {
    _isOnline = false;
    _hasCheckedNetwork = true;
    AppLogger.info('🔒 强制使用离线模式');
  }

  /// 🌐 获取当前网络状态
  bool get isOnline => _isOnline;

  /// 📊 获取网络状态建议
  String getNetworkRecommendation() {
    if (!_hasCheckedNetwork) {
      return '正在检查网络状态...';
    }

    if (_isOnline) {
      return '网络正常，可以使用在线功能';
    } else {
      return '网络离线或不稳定，建议使用离线模型';
    }
  }

  /// 🗑️ 释放资源
  void dispose() {
    stopNetworkMonitoring();
  }
}

/// 📋 网络状态枚举
enum NetworkStatus {
  online,
  offline,
  unstable,
  checking,
}
