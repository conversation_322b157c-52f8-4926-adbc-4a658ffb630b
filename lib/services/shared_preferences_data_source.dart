import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/task_model.dart';
import '../utils/app_logger.dart';

/// SharedPreferences数据源
/// 作为Hive存储的备份，确保数据安全性
class SharedPreferencesDataSource {
  static const String _tagName = 'SharedPreferencesDataSource';
  static const String _tasksKey = 'tasks_backup';
  static const String _currentTaskKey = 'current_task_backup';
  
  SharedPreferences? _prefs;
  
  /// 初始化SharedPreferences
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      AppLogger.info('SharedPreferences数据源初始化成功', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('SharedPreferences初始化失败: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 加载所有任务
  Future<List<TaskModel>> loadTasks() async {
    try {
      await _ensureInitialized();
      
      final tasksJson = _prefs!.getString(_tasksKey);
      if (tasksJson == null || tasksJson.isEmpty) {
        AppLogger.info('SharedPreferences中没有任务数据', tag: _tagName);
        return [];
      }
      
      final tasksList = jsonDecode(tasksJson) as List;
      final tasks = tasksList
          .map((taskJson) => TaskModel.fromJson(taskJson as Map<String, dynamic>))
          .toList();
      
      AppLogger.info('从SharedPreferences加载${tasks.length}个任务', tag: _tagName);
      return tasks;
    } catch (e, stackTrace) {
      AppLogger.error('从SharedPreferences加载任务失败: $e', 
          tag: _tagName, stackTrace: stackTrace);
      return [];
    }
  }
  
  /// 保存单个任务
  Future<void> saveTask(TaskModel task) async {
    try {
      await _ensureInitialized();
      
      // 先加载现有任务
      final existingTasks = await loadTasks();
      
      // 更新或添加任务
      final index = existingTasks.indexWhere((t) => t.id == task.id);
      if (index >= 0) {
        existingTasks[index] = task;
      } else {
        existingTasks.add(task);
      }
      
      // 保存更新后的任务列表
      await saveTasks(existingTasks);
      
      AppLogger.debug('任务保存到SharedPreferences成功: ${task.id}', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('保存任务到SharedPreferences失败: ${task.id}, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 批量保存任务
  Future<void> saveTasks(List<TaskModel> tasks) async {
    try {
      await _ensureInitialized();
      
      final tasksJson = jsonEncode(tasks.map((task) => task.toJson()).toList());
      await _prefs!.setString(_tasksKey, tasksJson);
      
      AppLogger.debug('批量保存${tasks.length}个任务到SharedPreferences成功', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('批量保存任务到SharedPreferences失败: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 删除任务
  Future<void> deleteTask(String id) async {
    try {
      await _ensureInitialized();
      
      final existingTasks = await loadTasks();
      existingTasks.removeWhere((task) => task.id == id);
      
      await saveTasks(existingTasks);
      
      AppLogger.debug('从SharedPreferences删除任务成功: $id', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('从SharedPreferences删除任务失败: $id, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 清空所有任务
  Future<void> clearAllTasks() async {
    try {
      await _ensureInitialized();
      
      await _prefs!.remove(_tasksKey);
      await _prefs!.remove(_currentTaskKey);
      
      AppLogger.info('SharedPreferences中的所有任务已清空', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('清空SharedPreferences任务失败: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 保存当前任务
  Future<void> saveCurrentTask(TaskModel? task) async {
    try {
      await _ensureInitialized();
      
      if (task == null) {
        await _prefs!.remove(_currentTaskKey);
        AppLogger.debug('清除SharedPreferences中的当前任务', tag: _tagName);
      } else {
        final taskJson = jsonEncode(task.toJson());
        await _prefs!.setString(_currentTaskKey, taskJson);
        AppLogger.debug('保存当前任务到SharedPreferences: ${task.id}', tag: _tagName);
      }
    } catch (e, stackTrace) {
      AppLogger.error('保存当前任务到SharedPreferences失败: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 加载当前任务
  Future<TaskModel?> loadCurrentTask() async {
    try {
      await _ensureInitialized();
      
      final taskJson = _prefs!.getString(_currentTaskKey);
      if (taskJson == null || taskJson.isEmpty) {
        return null;
      }
      
      final task = TaskModel.fromJson(jsonDecode(taskJson) as Map<String, dynamic>);
      AppLogger.debug('从SharedPreferences加载当前任务: ${task.id}', tag: _tagName);
      return task;
    } catch (e, stackTrace) {
      AppLogger.error('从SharedPreferences加载当前任务失败: $e', 
          tag: _tagName, stackTrace: stackTrace);
      return null;
    }
  }
  
  /// 获取存储统计信息
  Future<Map<String, dynamic>> getStorageStats() async {
    try {
      await _ensureInitialized();
      
      final tasks = await loadTasks();
      final currentTask = await loadCurrentTask();
      
      return {
        'totalTasks': tasks.length,
        'hasCurrentTask': currentTask != null,
        'currentTaskId': currentTask?.id,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      AppLogger.warning('获取SharedPreferences存储统计失败: $e', tag: _tagName);
      return {
        'totalTasks': 0,
        'hasCurrentTask': false,
        'error': e.toString(),
      };
    }
  }
  
  /// 数据完整性检查
  Future<bool> validateDataIntegrity() async {
    try {
      await _ensureInitialized();
      
      // 尝试加载和解析所有数据
      final tasks = await loadTasks();
      final currentTask = await loadCurrentTask();
      
      // 检查数据格式是否正确
      for (final task in tasks) {
        if (task.id.isEmpty || task.productCode.isEmpty) {
          AppLogger.warning('发现无效任务数据: ${task.id}', tag: _tagName);
          return false;
        }
      }
      
      AppLogger.info('SharedPreferences数据完整性检查通过', tag: _tagName);
      return true;
    } catch (e) {
      AppLogger.error('SharedPreferences数据完整性检查失败: $e', tag: _tagName);
      return false;
    }
  }
  
  /// 修复损坏的数据
  Future<void> repairCorruptedData() async {
    try {
      await _ensureInitialized();
      
      AppLogger.info('开始修复SharedPreferences损坏数据', tag: _tagName);
      
      // 尝试加载任务，过滤掉损坏的数据
      final tasksJson = _prefs!.getString(_tasksKey);
      if (tasksJson != null && tasksJson.isNotEmpty) {
        try {
          final tasksList = jsonDecode(tasksJson) as List;
          final validTasks = <TaskModel>[];
          
          for (final taskJson in tasksList) {
            try {
              final task = TaskModel.fromJson(taskJson as Map<String, dynamic>);
              if (task.id.isNotEmpty && task.productCode.isNotEmpty) {
                validTasks.add(task);
              }
            } catch (e) {
              AppLogger.warning('跳过损坏的任务数据: $e', tag: _tagName);
            }
          }
          
          // 保存修复后的数据
          await saveTasks(validTasks);
          AppLogger.info('修复完成，保留${validTasks.length}个有效任务', tag: _tagName);
        } catch (e) {
          AppLogger.error('任务数据修复失败，清空数据: $e', tag: _tagName);
          await _prefs!.remove(_tasksKey);
        }
      }
      
      // 检查当前任务
      final currentTaskJson = _prefs!.getString(_currentTaskKey);
      if (currentTaskJson != null && currentTaskJson.isNotEmpty) {
        try {
          TaskModel.fromJson(jsonDecode(currentTaskJson) as Map<String, dynamic>);
        } catch (e) {
          AppLogger.warning('当前任务数据损坏，清除: $e', tag: _tagName);
          await _prefs!.remove(_currentTaskKey);
        }
      }
      
      AppLogger.info('SharedPreferences数据修复完成', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('SharedPreferences数据修复失败: $e', 
          tag: _tagName, stackTrace: stackTrace);
    }
  }
  
  /// 确保已初始化
  Future<void> _ensureInitialized() async {
    if (_prefs == null) {
      await initialize();
    }
  }
}
