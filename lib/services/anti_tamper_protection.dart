import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'security_audit_logger.dart';
import 'hardware_fingerprint.dart';

/// 🛡️ 防篡改保护服务
/// 检测应用运行环境和完整性，防止恶意修改
class AntiTamperProtection {
  static const String _integrityCheckKey = 'app_integrity_baseline';
  static const String _lastCheckKey = 'last_integrity_check';
  static const String _tamperDetectionKey = 'tamper_detection_flags';

  /// 执行完整的防篡改检查
  static Future<TamperCheckResult> performTamperCheck() async {
    try {
      final checks = <String, bool>{};
      final issues = <String>[];

      // 1. 检测调试模式
      final debugCheck = await _checkDebugMode();
      checks['debug_mode'] = debugCheck.isPassed;
      if (!debugCheck.isPassed) issues.add(debugCheck.issue!);

      // 2. 检测Root/越狱
      final rootCheck = await _checkRootJailbreak();
      checks['root_jailbreak'] = rootCheck.isPassed;
      if (!rootCheck.isPassed) issues.add(rootCheck.issue!);

      // 3. 检测模拟器
      final emulatorCheck = await _checkEmulator();
      checks['emulator'] = emulatorCheck.isPassed;
      if (!emulatorCheck.isPassed) issues.add(emulatorCheck.issue!);

      // 4. 检测时间篡改
      final timeCheck = await _checkSystemTime();
      checks['system_time'] = timeCheck.isPassed;
      if (!timeCheck.isPassed) issues.add(timeCheck.issue!);

      // 5. 检测应用完整性
      final integrityCheck = await _checkAppIntegrity();
      checks['app_integrity'] = integrityCheck.isPassed;
      if (!integrityCheck.isPassed) issues.add(integrityCheck.issue!);

      // 6. 检测Hook框架
      final hookCheck = await _checkHookFrameworks();
      checks['hook_frameworks'] = hookCheck.isPassed;
      if (!hookCheck.isPassed) issues.add(hookCheck.issue!);

      // 7. 检测虚拟环境
      final virtualCheck = await _checkVirtualEnvironment();
      checks['virtual_environment'] = virtualCheck.isPassed;
      if (!virtualCheck.isPassed) issues.add(virtualCheck.issue!);

      // 记录检查结果
      await _recordCheckResult(checks, issues);

      // 计算总体风险等级
      final riskLevel = _calculateRiskLevel(issues);

      final result = TamperCheckResult(
        isPassed: issues.isEmpty,
        riskLevel: riskLevel,
        checks: checks,
        issues: issues,
        checkedAt: DateTime.now(),
      );

      // 记录安全事件
      await SecurityAuditLogger.logSecurityEvent(
        SecurityEvent.systemOperation(
          description: '防篡改检查完成',
          metadata: {
            'passed': result.isPassed,
            'risk_level': result.riskLevel.name,
            'issues_count': issues.length,
            'issues': issues,
          },
        ),
      );

      return result;
    } catch (e) {
      await SecurityAuditLogger.logSecurityEvent(
        SecurityEvent.trialError(
          description: '防篡改检查失败',
          metadata: {'error': e.toString()},
        ),
      );

      return TamperCheckResult.error(e.toString());
    }
  }

  /// 快速安全检查（用于应用启动时）
  static Future<bool> quickSecurityCheck() async {
    try {
      // 只进行关键的快速检查
      final debugCheck = await _checkDebugMode();
      final rootCheck = await _checkRootJailbreak();
      final timeCheck = await _checkSystemTime();

      final isPassed =
          debugCheck.isPassed && rootCheck.isPassed && timeCheck.isPassed;

      if (!isPassed) {
        await SecurityAuditLogger.logSecurityEvent(
          SecurityEvent.suspiciousActivity(
            description: '快速安全检查失败',
            metadata: {
              'debug': debugCheck.isPassed,
              'root': rootCheck.isPassed,
              'time': timeCheck.isPassed,
            },
          ),
        );
      }

      return isPassed;
    } catch (e) {
      return false;
    }
  }

  /// 获取设备安全状态
  static Future<DeviceSecurityStatus> getDeviceSecurityStatus() async {
    try {
      final lastCheck = await _getLastCheckResult();
      final currentTime = DateTime.now();

      // 如果距离上次检查超过24小时，执行新的检查
      bool needsNewCheck = lastCheck == null ||
          currentTime.difference(lastCheck.checkedAt).inHours > 24;

      final checkResult =
          needsNewCheck ? await performTamperCheck() : lastCheck;

      return DeviceSecurityStatus(
        isSecure: checkResult.isPassed,
        riskLevel: checkResult.riskLevel,
        lastCheckTime: checkResult.checkedAt,
        securityScore: _calculateSecurityScore(checkResult),
        recommendations: _generateSecurityRecommendations(checkResult),
      );
    } catch (e) {
      return DeviceSecurityStatus.unknown();
    }
  }

  /// 设置反调试陷阱
  static Future<void> setAntiDebugTraps() async {
    if (kDebugMode) return; // 开发模式下不设置陷阱

    try {
      // 设置一些简单的反调试检测
      // 注意：这些检测在Flutter中效果有限，主要起到威慑作用

      // 检测调试器附加
      if (await _isDebuggerAttached()) {
        await SecurityAuditLogger.logSecurityEvent(
          SecurityEvent.suspiciousActivity(
            description: '检测到调试器附加',
            metadata: {'detection_method': 'debugger_check'},
          ),
        );
      }

      // 设置时间陷阱
      await _setTimingTraps();
    } catch (e) {
      // print('设置反调试陷阱失败: $e'); // 删除所有print和debugPrint相关行
    }
  }

  // 私有检查方法

  /// 检测调试模式
  static Future<CheckResult> _checkDebugMode() async {
    try {
      if (kDebugMode) {
        return CheckResult.failed('应用运行在调试模式下');
      }

      // 检查Flutter的断言模式
      bool assertionsEnabled = false;
      assert(() {
        assertionsEnabled = true;
        return true;
      }());

      if (assertionsEnabled) {
        return CheckResult.failed('检测到断言模式已启用');
      }

      return CheckResult.passed();
    } catch (e) {
      return CheckResult.error(e.toString());
    }
  }

  /// 检测Root/越狱
  static Future<CheckResult> _checkRootJailbreak() async {
    try {
      if (kIsWeb) {
        return CheckResult.passed(); // Web平台不需要检查
      }

      List<String> suspiciousFiles = [];
      List<String> suspiciousApps = [];

      if (Platform.isAndroid) {
        // Android Root检测
        final rootFiles = [
          '/system/app/Superuser.apk',
          '/sbin/su',
          '/system/bin/su',
          '/system/xbin/su',
          '/data/local/xbin/su',
          '/data/local/bin/su',
          '/system/sd/xbin/su',
          '/system/bin/failsafe/su',
          '/data/local/su',
          '/su/bin/su',
        ];

        for (final file in rootFiles) {
          if (await File(file).exists()) {
            suspiciousFiles.add(file);
          }
        }

        // 检测Root管理应用
        final rootApps = [
          'com.noshufou.android.su',
          'com.noshufou.android.su.elite',
          'eu.chainfire.supersu',
          'com.koushikdutta.superuser',
          'com.thirdparty.superuser',
          'com.yellowes.su',
        ];

        // 这里应该检测已安装的应用包名，但Flutter无法直接访问
        // 可以通过平台通道实现
      } else if (Platform.isIOS) {
        // iOS越狱检测
        final jailbreakFiles = [
          '/Applications/Cydia.app',
          '/Library/MobileSubstrate/MobileSubstrate.dylib',
          '/bin/bash',
          '/usr/sbin/sshd',
          '/etc/apt',
          '/private/var/lib/apt/',
        ];

        for (final file in jailbreakFiles) {
          if (await File(file).exists()) {
            suspiciousFiles.add(file);
          }
        }
      }

      if (suspiciousFiles.isNotEmpty || suspiciousApps.isNotEmpty) {
        return CheckResult.failed('检测到Root/越狱环境: ${[
          ...suspiciousFiles,
          ...suspiciousApps
        ].join(', ')}');
      }

      return CheckResult.passed();
    } catch (e) {
      // 文件访问异常可能也是一个好兆头（权限受限）
      return CheckResult.passed();
    }
  }

  /// 检测模拟器
  static Future<CheckResult> _checkEmulator() async {
    try {
      if (kIsWeb) {
        return CheckResult.passed(); // Web平台不需要检查
      }

      final deviceInfo = await HardwareFingerprint.generateFingerprint();

      // 模拟器通常有特定的设备特征
      if (Platform.isAndroid) {
        // Android模拟器检测特征
        final emulatorIndicators = [
          'unknown',
          'goldfish',
          'emulator',
          'sdk_gphone',
          'generic'
        ];

        // 这里需要通过设备信息来判断
        // 简化实现：检查设备指纹是否包含模拟器特征
        for (final indicator in emulatorIndicators) {
          if (deviceInfo.toLowerCase().contains(indicator)) {
            return CheckResult.failed('检测到Android模拟器环境');
          }
        }
      }

      return CheckResult.passed();
    } catch (e) {
      return CheckResult.error(e.toString());
    }
  }

  /// 检测系统时间篡改
  static Future<CheckResult> _checkSystemTime() async {
    try {
      final now = DateTime.now();

      // 检查时间是否合理（不能太远的过去或未来）
      final year2020 = DateTime(2020, 1, 1);
      final year2030 = DateTime(2030, 12, 31);

      if (now.isBefore(year2020) || now.isAfter(year2030)) {
        return CheckResult.failed('系统时间异常: ${now.toIso8601String()}');
      }

      // 检查时区是否合理
      final utcOffset = now.timeZoneOffset.inHours;
      if (utcOffset < -12 || utcOffset > 12) {
        return CheckResult.failed(
            '系统时区异常: UTC${utcOffset > 0 ? '+' : ''}$utcOffset');
      }

      return CheckResult.passed();
    } catch (e) {
      return CheckResult.error(e.toString());
    }
  }

  /// 检测应用完整性
  static Future<CheckResult> _checkAppIntegrity() async {
    try {
      // 这里可以检查应用签名、文件完整性等
      // Flutter应用的完整性检查相对有限

      // 检查关键文件是否存在
      final prefs = await SharedPreferences.getInstance();
      final baseline = prefs.getString(_integrityCheckKey);

      if (baseline == null) {
        // 首次运行，建立基线
        await _establishIntegrityBaseline();
        return CheckResult.passed();
      }

      // 验证当前状态与基线的差异
      final currentState = await _calculateAppState();
      if (currentState != baseline) {
        return CheckResult.failed('应用完整性验证失败');
      }

      return CheckResult.passed();
    } catch (e) {
      return CheckResult.error(e.toString());
    }
  }

  /// 检测Hook框架
  static Future<CheckResult> _checkHookFrameworks() async {
    try {
      if (kIsWeb) {
        return CheckResult.passed(); // Web平台不需要检查
      }

      // 检测常见的Hook框架特征
      List<String> hookIndicators = [];

      if (Platform.isAndroid) {
        // Android Hook框架检测
        final xposedFiles = [
          '/data/data/de.robv.android.xposed.installer',
          '/data/data/org.meowcat.edxposed.manager',
          '/data/data/top.canyie.dreamland.manager',
        ];

        for (final file in xposedFiles) {
          if (await Directory(file).exists()) {
            hookIndicators.add('Xposed/EdXposed');
            break;
          }
        }
      }

      if (hookIndicators.isNotEmpty) {
        return CheckResult.failed('检测到Hook框架: ${hookIndicators.join(', ')}');
      }

      return CheckResult.passed();
    } catch (e) {
      return CheckResult.passed(); // 访问错误可能是好事
    }
  }

  /// 检测虚拟环境
  static Future<CheckResult> _checkVirtualEnvironment() async {
    try {
      if (kIsWeb) {
        return CheckResult.passed(); // Web平台不需要检查
      }

      // 检测虚拟机环境特征
      final deviceInfo = await HardwareFingerprint.getDeviceSummary();
      final platform = deviceInfo['platform'] ?? '';

      // 虚拟机通常有特定的硬件特征
      final vmIndicators = [
        'vmware',
        'virtualbox',
        'qemu',
        'bochs',
        'xen',
        'parallels'
      ];

      for (final indicator in vmIndicators) {
        if (platform.toLowerCase().contains(indicator)) {
          return CheckResult.failed('检测到虚拟机环境: $indicator');
        }
      }

      return CheckResult.passed();
    } catch (e) {
      return CheckResult.error(e.toString());
    }
  }

  /// 检测调试器附加
  static Future<bool> _isDebuggerAttached() async {
    try {
      // 简单的调试器检测（在Flutter中效果有限）
      if (kDebugMode) return true;

      // 时间检测 - 调试时执行会变慢
      final start = DateTime.now().millisecondsSinceEpoch;
      await Future.delayed(Duration(milliseconds: 1));
      final end = DateTime.now().millisecondsSinceEpoch;

      // 如果执行时间异常长，可能有调试器
      return (end - start) > 100;
    } catch (e) {
      return false;
    }
  }

  /// 设置时间陷阱
  static Future<void> _setTimingTraps() async {
    try {
      // 设置一些时间相关的检测点
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;

      await prefs.setInt('timing_trap_1', now);

      // 延迟后再设置另一个检测点
      await Future.delayed(Duration(milliseconds: 10));
      await prefs.setInt(
          'timing_trap_2', DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      // print('设置时间陷阱失败: $e'); // 删除所有print和debugPrint相关行
    }
  }

  /// 建立完整性基线
  static Future<void> _establishIntegrityBaseline() async {
    try {
      final appState = await _calculateAppState();
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_integrityCheckKey, appState);
    } catch (e) {
      // print('建立完整性基线失败: $e'); // 删除所有print和debugPrint相关行
    }
  }

  /// 计算应用状态
  static Future<String> _calculateAppState() async {
    try {
      final deviceFingerprint = await HardwareFingerprint.generateFingerprint();
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // 组合各种状态信息
      final stateData = '$deviceFingerprint:$timestamp:APP_STATE';
      final bytes = utf8.encode(stateData);
      final digest = sha256.convert(bytes);

      return digest.toString();
    } catch (e) {
      return 'UNKNOWN_STATE';
    }
  }

  /// 记录检查结果
  static Future<void> _recordCheckResult(
      Map<String, bool> checks, List<String> issues) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final checkData = {
        'timestamp': DateTime.now().toIso8601String(),
        'checks': checks,
        'issues': issues,
      };

      await prefs.setString(_lastCheckKey, jsonEncode(checkData));

      // 如果有问题，设置篡改检测标志
      if (issues.isNotEmpty) {
        await prefs.setStringList(_tamperDetectionKey, issues);
      }
    } catch (e) {
      // print('记录检查结果失败: $e'); // 删除所有print和debugPrint相关行
    }
  }

  /// 获取上次检查结果
  static Future<TamperCheckResult?> _getLastCheckResult() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final checkDataJson = prefs.getString(_lastCheckKey);

      if (checkDataJson == null) return null;

      final checkData = jsonDecode(checkDataJson) as Map<String, dynamic>;

      return TamperCheckResult(
        isPassed: (checkData['issues'] as List).isEmpty,
        riskLevel: _calculateRiskLevel(List<String>.from(checkData['issues'])),
        checks: Map<String, bool>.from(checkData['checks']),
        issues: List<String>.from(checkData['issues']),
        checkedAt: DateTime.parse(checkData['timestamp']),
      );
    } catch (e) {
      return null;
    }
  }

  /// 计算风险等级
  static RiskLevel _calculateRiskLevel(List<String> issues) {
    if (issues.isEmpty) return RiskLevel.none;

    final criticalKeywords = ['root', 'jailbreak', 'hook', 'debug'];
    final hasCritical = issues.any((issue) => criticalKeywords
        .any((keyword) => issue.toLowerCase().contains(keyword)));

    if (hasCritical) return RiskLevel.high;
    if (issues.length > 2) return RiskLevel.medium;
    return RiskLevel.low;
  }

  /// 计算安全分数
  static int _calculateSecurityScore(TamperCheckResult result) {
    if (!result.isPassed) {
      switch (result.riskLevel) {
        case RiskLevel.high:
          return 20;
        case RiskLevel.medium:
          return 50;
        case RiskLevel.low:
          return 70;
        case RiskLevel.none:
          return 100;
      }
    }
    return 100;
  }

  /// 生成安全建议
  static List<String> _generateSecurityRecommendations(
      TamperCheckResult result) {
    final recommendations = <String>[];

    if (!result.isPassed) {
      if (result.issues.any(
          (issue) => issue.contains('root') || issue.contains('jailbreak'))) {
        recommendations.add('建议在非Root/越狱设备上使用应用');
      }

      if (result.issues.any((issue) => issue.contains('debug'))) {
        recommendations.add('请使用正式发布版本');
      }

      if (result.issues.any((issue) => issue.contains('hook'))) {
        recommendations.add('检测到Hook框架，建议卸载相关工具');
      }

      if (result.issues.any((issue) => issue.contains('时间'))) {
        recommendations.add('请确保设备时间设置正确');
      }
    }

    if (recommendations.isEmpty) {
      recommendations.add('设备安全状态良好');
    }

    return recommendations;
  }
}

// 数据类定义

/// 风险等级
enum RiskLevel {
  none,
  low,
  medium,
  high,
}

/// 检查结果
class CheckResult {
  final bool isPassed;
  final String? issue;
  final String? error;

  CheckResult._({
    required this.isPassed,
    this.issue,
    this.error,
  });

  factory CheckResult.passed() => CheckResult._(isPassed: true);
  factory CheckResult.failed(String issue) =>
      CheckResult._(isPassed: false, issue: issue);
  factory CheckResult.error(String error) =>
      CheckResult._(isPassed: false, error: error);
}

/// 篡改检查结果
class TamperCheckResult {
  final bool isPassed;
  final RiskLevel riskLevel;
  final Map<String, bool> checks;
  final List<String> issues;
  final DateTime checkedAt;
  final String? error;

  TamperCheckResult({
    required this.isPassed,
    required this.riskLevel,
    required this.checks,
    required this.issues,
    required this.checkedAt,
    this.error,
  });

  factory TamperCheckResult.error(String error) => TamperCheckResult(
        isPassed: false,
        riskLevel: RiskLevel.high,
        checks: {},
        issues: ['检查过程出错: $error'],
        checkedAt: DateTime.now(),
        error: error,
      );
}

/// 设备安全状态
class DeviceSecurityStatus {
  final bool isSecure;
  final RiskLevel riskLevel;
  final DateTime lastCheckTime;
  final int securityScore;
  final List<String> recommendations;

  DeviceSecurityStatus({
    required this.isSecure,
    required this.riskLevel,
    required this.lastCheckTime,
    required this.securityScore,
    required this.recommendations,
  });

  factory DeviceSecurityStatus.unknown() => DeviceSecurityStatus(
        isSecure: false,
        riskLevel: RiskLevel.medium,
        lastCheckTime: DateTime.now(),
        securityScore: 0,
        recommendations: ['无法获取设备安全状态'],
      );
}
