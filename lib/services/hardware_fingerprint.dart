import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'app_security_service.dart';

/// 🔐 硬件指纹生成服务
/// 生成基于设备硬件特征的唯一标识符，用于设备绑定和安全验证
class HardwareFingerprint {
  static const String _webFallbackId = 'WEB_BROWSER_FALLBACK';
  static String? _cachedFingerprint;

  /// 获取设备ID（统一使用AppSecurityService的方法）
  static Future<String> getDeviceId() async {
    return await AppSecurityService.getDeviceId();
  }

  /// 生成设备硬件指纹（统一使用AppSecurityService的方法）
  /// 返回一个基于多个硬件特征的稳定唯一标识符
  static Future<String> generateFingerprint() async {
    // 统一使用AppSecurityService的设备ID生成方法
    return await AppSecurityService.getDeviceId();
  }

  /// 生成Android设备指纹
  static Future<Map<String, String>> _getAndroidFingerprint() async {
    final deviceInfo = DeviceInfoPlugin();
    final android = await deviceInfo.androidInfo;

    return {
      'androidId': android.id ?? '',
      'androidIdSecure': android.id ?? '',
      'brand': android.brand,
      'device': android.device,
      'hardware': android.hardware ?? '',
      'manufacturer': android.manufacturer,
      'model': android.model,
      'product': android.product ?? '',
      'bootloader': android.bootloader ?? '',
      'display': android.display ?? '',
      'fingerprint': android.fingerprint ?? '',
      'host': android.host ?? '',
      'tags': android.tags ?? '',
      'type': android.type ?? '',
      'board': android.board ?? '',
      'version_sdk': android.version.sdkInt.toString(),
      'version_release': android.version.release ?? '',
      'version_codename': android.version.codename ?? '',
      'supported_abis': android.supportedAbis.join(','),
      'platform': 'android',
    };
  }

  /// 生成iOS设备指纹
  static Future<Map<String, String>> _getIOSFingerprint() async {
    final deviceInfo = DeviceInfoPlugin();
    final ios = await deviceInfo.iosInfo;

    return {
      'name': ios.name,
      'systemName': ios.systemName,
      'systemVersion': ios.systemVersion,
      'model': ios.model,
      'localizedModel': ios.localizedModel,
      'identifierForVendor': ios.identifierForVendor ?? '',
      'utsname_machine': ios.utsname.machine ?? '',
      'utsname_nodename': ios.utsname.nodename ?? '',
      'utsname_release': ios.utsname.release ?? '',
      'utsname_sysname': ios.utsname.sysname ?? '',
      'utsname_version': ios.utsname.version ?? '',
      'isPhysicalDevice': ios.isPhysicalDevice.toString(),
      'platform': 'ios',
    };
  }

  /// 生成Windows设备指纹
  static Future<Map<String, String>> _getWindowsFingerprint() async {
    final deviceInfo = DeviceInfoPlugin();
    final windows = await deviceInfo.windowsInfo;

    return {
      'computerName': windows.computerName,
      'numberOfCores': windows.numberOfCores.toString(),
      'systemMemoryInMegabytes': windows.systemMemoryInMegabytes.toString(),
      'userName': windows.userName,
      'majorVersion': windows.majorVersion.toString(),
      'minorVersion': windows.minorVersion.toString(),
      'buildNumber': windows.buildNumber.toString(),
      'platformId': windows.platformId.toString(),
      'csdVersion': windows.csdVersion ?? '',
      'servicePackMajor': windows.servicePackMajor.toString(),
      'servicePackMinor': windows.servicePackMinor.toString(),
      'suitMask': windows.suitMask.toString(),
      'productType': windows.productType.toString(),
      'reserved': windows.reserved.toString(),
      'buildLab': windows.buildLab ?? '',
      'buildLabEx': windows.buildLabEx ?? '',
      'digitalProductId': windows.digitalProductId?.toString() ?? '',
      'displayVersion': windows.displayVersion ?? '',
      'editionId': windows.editionId ?? '',
      'installDate': windows.installDate?.toString() ?? '',
      'productId': windows.productId ?? '',
      'productName': windows.productName ?? '',
      'registeredOwner': windows.registeredOwner ?? '',
      'releaseId': windows.releaseId ?? '',
      'deviceId': windows.deviceId ?? '',
      'platform': 'windows',
    };
  }

  /// 生成Linux设备指纹
  static Future<Map<String, String>> _getLinuxFingerprint() async {
    final deviceInfo = DeviceInfoPlugin();
    final linux = await deviceInfo.linuxInfo;

    return {
      'name': linux.name,
      'version': linux.version ?? '',
      'id': linux.id ?? '',
      'idLike': linux.idLike?.join(',') ?? '',
      'versionCodename': linux.versionCodename ?? '',
      'versionId': linux.versionId ?? '',
      'prettyName': linux.prettyName ?? '',
      'buildId': linux.buildId ?? '',
      'variant': linux.variant ?? '',
      'variantId': linux.variantId ?? '',
      'machineId': linux.machineId ?? '',
      'platform': 'linux',
    };
  }

  /// 生成macOS设备指纹
  static Future<Map<String, String>> _getMacOSFingerprint() async {
    final deviceInfo = DeviceInfoPlugin();
    final macos = await deviceInfo.macOsInfo;

    return {
      'computerName': macos.computerName,
      'hostName': macos.hostName,
      'arch': macos.arch,
      'model': macos.model,
      'kernelVersion': macos.kernelVersion,
      'majorVersion': macos.majorVersion.toString(),
      'minorVersion': macos.minorVersion.toString(),
      'patchVersion': macos.patchVersion.toString(),
      'osRelease': macos.osRelease,
      'activeCPUs': macos.activeCPUs.toString(),
      'memorySize': macos.memorySize.toString(),
      'cpuFrequency': macos.cpuFrequency.toString(),
      'systemGUID': macos.systemGUID ?? '',
      'platform': 'macos',
    };
  }

  /// 生成Web平台指纹
  static Future<Map<String, String>> _getWebFingerprint() async {
    final deviceInfo = DeviceInfoPlugin();
    final web = await deviceInfo.webBrowserInfo;

    return {
      'browserName': web.browserName.name,
      'appCodeName': web.appCodeName ?? '',
      'appName': web.appName ?? '',
      'appVersion': web.appVersion ?? '',
      'deviceMemory': web.deviceMemory?.toString() ?? '',
      'language': web.language ?? '',
      'languages': web.languages?.join(',') ?? '',
      'platform': web.platform ?? '',
      'product': web.product ?? '',
      'productSub': web.productSub ?? '',
      'userAgent': web.userAgent ?? '',
      'vendor': web.vendor ?? '',
      'vendorSub': web.vendorSub ?? '',
      'hardwareConcurrency': web.hardwareConcurrency?.toString() ?? '',
      'maxTouchPoints': web.maxTouchPoints?.toString() ?? '',
      'platform_type': 'web',
    };
  }

  /// 从设备特征生成指纹
  static String _generateFingerprintFromFeatures(Map<String, String> features) {
    // 按键排序确保一致性
    final sortedKeys = features.keys.toList()..sort();
    final sortedFeatures = <String, String>{};
    for (final key in sortedKeys) {
      sortedFeatures[key] = features[key] ?? '';
    }

    // 生成组合字符串
    final combined =
        sortedFeatures.entries.map((e) => '${e.key}:${e.value}').join('|');

    // 使用SHA-256生成哈希
    final bytes = utf8.encode(combined);
    final digest = sha256.convert(bytes);

    // 返回前32位作为指纹
    return digest.toString().substring(0, 32).toUpperCase();
  }

  /// 生成备用指纹（当正常流程失败时使用）
  static String _generateFallbackFingerprint() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomValue = random.nextInt(999999);

    final fallbackData = 'FALLBACK_${timestamp}_$randomValue';
    final bytes = utf8.encode(fallbackData);
    final digest = sha256.convert(bytes);

    return 'FB${digest.toString().substring(0, 30).toUpperCase()}';
  }

  /// 验证两个指纹是否匹配（考虑一定的容错性）
  static bool isMatch(String fingerprint1, String fingerprint2) {
    if (fingerprint1 == fingerprint2) {
      return true;
    }

    // 如果有一个是备用指纹，进行模糊匹配
    if (fingerprint1.startsWith('FB') || fingerprint2.startsWith('FB')) {
      return _fuzzyMatch(fingerprint1, fingerprint2);
    }

    return false;
  }

  /// 模糊匹配（用于处理设备信息轻微变化的情况）
  static bool _fuzzyMatch(String fingerprint1, String fingerprint2) {
    if (fingerprint1.length != fingerprint2.length) {
      return false;
    }

    int matchCount = 0;
    for (int i = 0; i < fingerprint1.length; i++) {
      if (fingerprint1[i] == fingerprint2[i]) {
        matchCount++;
      }
    }

    // 如果80%以上字符匹配，认为是同一设备
    return matchCount / fingerprint1.length >= 0.8;
  }

  /// 清除缓存的指纹（用于测试或重新生成）
  static void clearCache() {
    _cachedFingerprint = null;
  }

  /// 获取设备平台信息
  static Future<String> getPlatformInfo() async {
    if (kIsWeb) {
      return 'Web Browser';
    } else if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final android = await deviceInfo.androidInfo;
      return 'Android ${android.version.release} (${android.model})';
    } else if (Platform.isIOS) {
      final deviceInfo = DeviceInfoPlugin();
      final ios = await deviceInfo.iosInfo;
      return 'iOS ${ios.systemVersion} (${ios.model})';
    } else if (Platform.isWindows) {
      return 'Windows Desktop';
    } else if (Platform.isLinux) {
      return 'Linux Desktop';
    } else if (Platform.isMacOS) {
      return 'macOS Desktop';
    } else {
      return 'Unknown Platform';
    }
  }

  /// 生成设备摘要信息（用于显示和调试）
  static Future<Map<String, String>> getDeviceSummary() async {
    try {
      final fingerprint = await AppSecurityService.getDeviceId();
      final platformInfo = await getPlatformInfo();

      return {
        'fingerprint': fingerprint,
        'platform': platformInfo,
        'generated_at': DateTime.now().toIso8601String(),
        'cache_status': 'unified', // 统一使用AppSecurityService
      };
    } catch (e) {
      return {
        'fingerprint': 'ERROR',
        'platform': 'Unknown',
        'error': e.toString(),
        'generated_at': DateTime.now().toIso8601String(),
      };
    }
  }
}
