/// 应用异常体系
/// 提供统一的异常处理和用户友好的错误消息

/// 应用异常基类
abstract class AppException implements Exception {
  final String message;
  final String? userMessage;
  final Object? originalError;
  final StackTrace? stackTrace;
  final String? errorCode;
  final Map<String, dynamic>? metadata;

  const AppException(
    this.message, {
    this.userMessage,
    this.originalError,
    this.stackTrace,
    this.errorCode,
    this.metadata,
  });

  /// 用户友好的错误消息
  String get displayMessage => userMessage ?? message;

  /// 是否为用户错误（用户操作导致的错误）
  bool get isUserError => false;

  /// 是否为系统错误（系统内部错误）
  bool get isSystemError => !isUserError;

  /// 错误严重程度
  ErrorSeverity get severity => ErrorSeverity.medium;

  @override
  String toString() {
    return 'AppException(message: $message, userMessage: $userMessage, '
           'errorCode: $errorCode, severity: ${severity.name})';
  }
}

/// 错误严重程度
enum ErrorSeverity {
  low,      // 低：不影响核心功能
  medium,   // 中：影响部分功能
  high,     // 高：影响核心功能
  critical, // 严重：系统无法正常运行
}

/// 数据一致性异常
class DataConsistencyException extends AppException {
  const DataConsistencyException(
    super.message, {
    super.userMessage = '数据同步出现问题，请重试',
    super.originalError,
    super.stackTrace,
    super.errorCode = 'DATA_CONSISTENCY_ERROR',
    super.metadata,
  });

  @override
  bool get isSystemError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.high;
}

/// 工作量计算异常
class WorkloadCalculationException extends AppException {
  const WorkloadCalculationException(
    super.message, {
    super.userMessage = '工作量计算失败，请检查数据',
    super.originalError,
    super.stackTrace,
    super.errorCode = 'WORKLOAD_CALCULATION_ERROR',
    super.metadata,
  });

  @override
  bool get isSystemError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.medium;
}

/// 识别异常
class RecognitionException extends AppException {
  const RecognitionException(
    super.message, {
    super.userMessage = '图像识别失败，请重新拍照',
    super.originalError,
    super.stackTrace,
    super.errorCode = 'RECOGNITION_ERROR',
    super.metadata,
  });

  @override
  bool get isUserError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.medium;
}

/// 图像处理异常
class ImageProcessingException extends AppException {
  const ImageProcessingException(
    super.message, {
    super.userMessage = '图像处理失败，请选择其他图片',
    super.originalError,
    super.stackTrace,
    super.errorCode = 'IMAGE_PROCESSING_ERROR',
    super.metadata,
  });

  @override
  bool get isUserError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.low;
}

/// 图像格式异常
class ImageFormatException extends AppException {
  const ImageFormatException(
    super.message, {
    super.userMessage = '不支持的图像格式，请选择JPG或PNG格式',
    super.originalError,
    super.stackTrace,
    super.errorCode = 'IMAGE_FORMAT_ERROR',
    super.metadata,
  });

  @override
  bool get isUserError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.low;
}

/// 图像过大异常
class ImageTooLargeException extends AppException {
  const ImageTooLargeException(
    super.message, {
    super.userMessage = '图像文件过大，请选择小于50MB的图片',
    super.originalError,
    super.stackTrace,
    super.errorCode = 'IMAGE_TOO_LARGE_ERROR',
    super.metadata,
  });

  @override
  bool get isUserError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.low;
}

/// 存储异常
class StorageException extends AppException {
  const StorageException(
    super.message, {
    super.userMessage = '数据存储失败，请检查存储空间',
    super.originalError,
    super.stackTrace,
    super.errorCode = 'STORAGE_ERROR',
    super.metadata,
  });

  @override
  bool get isSystemError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.high;
}

/// 网络异常
class NetworkException extends AppException {
  const NetworkException(
    super.message, {
    super.userMessage = '网络连接失败，请检查网络设置',
    super.originalError,
    super.stackTrace,
    super.errorCode = 'NETWORK_ERROR',
    super.metadata,
  });

  @override
  bool get isSystemError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.medium;
}

/// 权限异常
class PermissionException extends AppException {
  const PermissionException(
    super.message, {
    super.userMessage = '权限不足，请检查应用权限设置',
    super.originalError,
    super.stackTrace,
    super.errorCode = 'PERMISSION_ERROR',
    super.metadata,
  });

  @override
  bool get isUserError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.medium;
}

/// 验证异常
class ValidationException extends AppException {
  const ValidationException(
    super.message, {
    super.userMessage,
    super.originalError,
    super.stackTrace,
    super.errorCode = 'VALIDATION_ERROR',
    super.metadata,
  });

  @override
  bool get isUserError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.low;
}

/// 配置异常
class ConfigurationException extends AppException {
  const ConfigurationException(
    super.message, {
    super.userMessage = '配置错误，请联系管理员',
    super.originalError,
    super.stackTrace,
    super.errorCode = 'CONFIGURATION_ERROR',
    super.metadata,
  });

  @override
  bool get isSystemError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.high;
}

/// 业务逻辑异常
class BusinessLogicException extends AppException {
  const BusinessLogicException(
    super.message, {
    super.userMessage,
    super.originalError,
    super.stackTrace,
    super.errorCode = 'BUSINESS_LOGIC_ERROR',
    super.metadata,
  });

  @override
  bool get isUserError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.medium;
}

/// 超时异常
class TimeoutException extends AppException {
  const TimeoutException(
    super.message, {
    super.userMessage = '操作超时，请重试',
    super.originalError,
    super.stackTrace,
    super.errorCode = 'TIMEOUT_ERROR',
    super.metadata,
  });

  @override
  bool get isSystemError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.medium;
}

/// 并发异常
class ConcurrencyException extends AppException {
  const ConcurrencyException(
    super.message, {
    super.userMessage = '数据已被其他用户修改，请刷新后重试',
    super.originalError,
    super.stackTrace,
    super.errorCode = 'CONCURRENCY_ERROR',
    super.metadata,
  });

  @override
  bool get isUserError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.medium;
}

/// 资源不足异常
class ResourceException extends AppException {
  const ResourceException(
    super.message, {
    super.userMessage = '系统资源不足，请稍后重试',
    super.originalError,
    super.stackTrace,
    super.errorCode = 'RESOURCE_ERROR',
    super.metadata,
  });

  @override
  bool get isSystemError => true;

  @override
  ErrorSeverity get severity => ErrorSeverity.high;
}

/// 异常工厂类
class AppExceptionFactory {
  /// 从通用异常创建应用异常
  static AppException fromException(Object error, {StackTrace? stackTrace}) {
    if (error is AppException) {
      return error;
    }

    // 根据异常类型创建相应的应用异常
    if (error is FormatException) {
      return ValidationException(
        'Format error: ${error.message}',
        userMessage: '数据格式错误，请检查输入',
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    if (error is ArgumentError) {
      return ValidationException(
        'Argument error: ${error.message}',
        userMessage: '参数错误，请检查输入',
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    if (error is StateError) {
      return DataConsistencyException(
        'State error: ${error.message}',
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    if (error is FileSystemException) {
      return StorageException(
        'File system error: ${error.message}',
        originalError: error,
        stackTrace: stackTrace,
      );
    }

    // 默认创建通用异常
    return AppException(
      error.toString(),
      userMessage: '操作失败，请重试',
      originalError: error,
      stackTrace: stackTrace,
      errorCode: 'UNKNOWN_ERROR',
    );
  }

  /// 创建数据一致性异常
  static DataConsistencyException dataConsistency(
    String message, {
    Object? originalError,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) {
    return DataConsistencyException(
      message,
      originalError: originalError,
      stackTrace: stackTrace,
      metadata: metadata,
    );
  }

  /// 创建工作量计算异常
  static WorkloadCalculationException workloadCalculation(
    String message, {
    Object? originalError,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) {
    return WorkloadCalculationException(
      message,
      originalError: originalError,
      stackTrace: stackTrace,
      metadata: metadata,
    );
  }

  /// 创建识别异常
  static RecognitionException recognition(
    String message, {
    String? userMessage,
    Object? originalError,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) {
    return RecognitionException(
      message,
      userMessage: userMessage,
      originalError: originalError,
      stackTrace: stackTrace,
      metadata: metadata,
    );
  }

  /// 创建验证异常
  static ValidationException validation(
    String message, {
    String? userMessage,
    Object? originalError,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
  }) {
    return ValidationException(
      message,
      userMessage: userMessage ?? message,
      originalError: originalError,
      stackTrace: stackTrace,
      metadata: metadata,
    );
  }
}
