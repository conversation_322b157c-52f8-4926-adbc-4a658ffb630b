import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'responsive_helper.dart';
import 'theme_colors.dart';

/// 🔍 可访问性助手类
/// 提供更好的用户体验和可访问性支持
class AccessibilityHelper {
  /// 获取系统字体大小缩放因子 - 兼容新API
  static double getSystemTextScaleFactor(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    // 兼容新旧API
    return mediaQuery.textScaler.scale(1.0);
  }

  /// 检查是否启用了大字体模式
  static bool isLargeTextEnabled(BuildContext context) {
    final textScale = getSystemTextScaleFactor(context);
    return textScale > 1.3;
  }

  /// 检查是否启用了高对比度模式
  static bool isHighContrastEnabled(BuildContext context) {
    return MediaQuery.of(context).highContrast;
  }

  /// 检查是否启用了减少动画
  static bool isReduceAnimationsEnabled(BuildContext context) {
    return MediaQuery.of(context).disableAnimations;
  }

  /// 获取可访问的字体大小
  static double getAccessibleFontSize(
      BuildContext context, double baseFontSize) {
    final systemScale = getSystemTextScaleFactor(context);
    final responsiveSize =
        ResponsiveHelper.getResponsiveFontSize(context, baseFontSize);

    // 限制最大缩放，防止界面布局崩溃
    final maxScale = ResponsiveHelper.isMobile(context) ? 2.0 : 1.8;
    final limitedScale = systemScale > maxScale ? maxScale : systemScale;

    return responsiveSize * limitedScale;
  }

  /// 获取高对比度颜色
  static Color getContrastColor(BuildContext context, Color originalColor) {
    if (!isHighContrastEnabled(context)) {
      return originalColor;
    }

    // 高对比度模式下的颜色映射
    if (originalColor == ThemeColors.textLight) {
      return Colors.black87;
    } else if (originalColor == ThemeColors.textMedium) {
      return Colors.black;
    } else if (originalColor == ThemeColors.primary) {
      return const Color(0xFF000066); // 更深的蓝色
    }

    return originalColor;
  }

  /// 检查颜色对比度是否符合WCAG标准
  static bool hasGoodContrast(Color foreground, Color background) {
    final contrast = calculateContrastRatio(foreground, background);
    return contrast >= 4.5; // WCAG AA标准
  }

  /// 计算颜色对比度
  static double calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = _calculateLuminance(color1);
    final luminance2 = _calculateLuminance(color2);

    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;

    return (lighter + 0.05) / (darker + 0.05);
  }

  /// 计算颜色亮度
  static double _calculateLuminance(Color color) {
    final r = (color.r * 255.0).round() & 0xff;
    final g = (color.g * 255.0).round() & 0xff;
    final b = (color.b * 255.0).round() & 0xff;

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  static double _linearize(double value) {
    return value <= 0.03928
        ? value / 12.92
        : math.pow((value + 0.055) / 1.055, 2.4).toDouble();
  }

  /// 提供触觉反馈
  static void provideTactileFeedback(
      [HapticFeedbackType type = HapticFeedbackType.lightImpact]) {
    switch (type) {
      case HapticFeedbackType.lightImpact:
        HapticFeedback.lightImpact();
        break;
      case HapticFeedbackType.mediumImpact:
        HapticFeedback.mediumImpact();
        break;
      case HapticFeedbackType.heavyImpact:
        HapticFeedback.heavyImpact();
        break;
      case HapticFeedbackType.selectionClick:
        HapticFeedback.selectionClick();
        break;
      case HapticFeedbackType.vibrate:
        HapticFeedback.vibrate();
        break;
    }
  }

  /// 获取可访问的动画时长
  static Duration getAccessibleAnimationDuration(
      BuildContext context, Duration baseDuration) {
    if (isReduceAnimationsEnabled(context)) {
      return Duration.zero; // 禁用动画
    }

    // 根据用户偏好调整动画时长
    final systemScale = getSystemTextScaleFactor(context);
    if (systemScale > 1.5) {
      // 大字体用户可能更喜欢稍慢的动画
      return Duration(
          milliseconds: (baseDuration.inMilliseconds * 1.2).round());
    }

    return baseDuration;
  }

  /// 获取适合屏幕阅读器的文本
  static String getSemanticLabel(String text, {String? context}) {
    if (context != null) {
      return '$context: $text';
    }
    return text;
  }

  /// 检查是否需要额外的点击区域
  static bool needsLargerTouchTarget(BuildContext context) {
    return isLargeTextEnabled(context) || ResponsiveHelper.isMobile(context);
  }

  /// 获取最小触摸目标尺寸
  static double getMinTouchTargetSize(BuildContext context) {
    if (needsLargerTouchTarget(context)) {
      return 48.0; // Material Design推荐的最小触摸目标
    }
    return 40.0;
  }

  /// 创建可访问的文本样式
  static TextStyle createAccessibleTextStyle(
    BuildContext context, {
    required double baseFontSize,
    FontWeight? fontWeight,
    Color? color,
    TextDecoration? decoration,
  }) {
    return TextStyle(
      fontSize: getAccessibleFontSize(context, baseFontSize),
      fontWeight: fontWeight,
      color: color != null ? getContrastColor(context, color) : null,
      decoration: decoration,
      // 增加行高以提高可读性
      height: isLargeTextEnabled(context) ? 1.6 : 1.4,
    );
  }

  /// 创建可访问的按钮
  static Widget createAccessibleButton({
    required BuildContext context,
    required String text,
    required VoidCallback onPressed,
    String? semanticLabel,
    IconData? icon,
    bool isPrimary = true,
  }) {
    final minSize = getMinTouchTargetSize(context);

    return Semantics(
      label: semanticLabel ?? text,
      button: true,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            provideTactileFeedback();
            onPressed();
          },
          borderRadius: BorderRadius.circular(8),
          child: Container(
            constraints: BoxConstraints(
              minWidth: minSize,
              minHeight: minSize,
            ),
            padding: EdgeInsets.symmetric(
              horizontal: ResponsiveHelper.getResponsiveSpacing(context, 16),
              vertical: ResponsiveHelper.getResponsiveSpacing(context, 12),
            ),
            decoration: BoxDecoration(
              color: isPrimary ? ThemeColors.primary : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
              border: isPrimary ? null : Border.all(color: ThemeColors.primary),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (icon != null) ...[
                  Icon(
                    icon,
                    color: isPrimary ? Colors.white : ThemeColors.primary,
                    size: getAccessibleFontSize(context, 20),
                  ),
                  SizedBox(
                      width: ResponsiveHelper.getResponsiveSpacing(context, 8)),
                ],
                Text(
                  text,
                  style: createAccessibleTextStyle(
                    context,
                    baseFontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isPrimary ? Colors.white : ThemeColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 触觉反馈类型枚举
enum HapticFeedbackType {
  lightImpact,
  mediumImpact,
  heavyImpact,
  selectionClick,
  vibrate,
}

/// 可访问的文本组件
class AccessibleText extends StatelessWidget {
  final String text;
  final double baseFontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final String? semanticLabel;

  const AccessibleText(
    this.text, {
    Key? key,
    this.baseFontSize = 16,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.semanticLabel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel,
      child: Text(
        text,
        style: AccessibilityHelper.createAccessibleTextStyle(
          context,
          baseFontSize: baseFontSize,
          fontWeight: fontWeight,
          color: color,
        ),
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: overflow,
      ),
    );
  }
}
