import 'package:flutter/material.dart';
import 'dart:ui' as ui;

/// 高级渐变与材质效果工具类
/// 用于实现"精准科技"风格的渐变和玻璃态效果
class GradientExtensions {
  /// 创建科技风格的微渐变文本
  static TextStyle createGradientTextStyle({
    required double fontSize,
    FontWeight fontWeight = FontWeight.bold,
    required List<Color> colors,
    TextDecoration? decoration,
    double? letterSpacing,
  }) {
    // 根据颜色数量生成相应的stops
    List<double> stops = [];
    if (colors.length > 1) {
      double interval = 1.0 / (colors.length - 1);
      for (int i = 0; i < colors.length; i++) {
        stops.add(i * interval);
      }
    }

    return TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      foreground: Paint()
        ..shader = ui.Gradient.linear(
          const Offset(0, 0),
          Offset(0, fontSize * 1.2),
          colors,
          stops,
        ),
      decoration: decoration,
      letterSpacing: letterSpacing,
    );
  }

  /// 创建标准的浅灰到白色微渐变文本样式
  static TextStyle createTechDataTextStyle({
    required double fontSize,
    FontWeight fontWeight = FontWeight.bold,
  }) {
    return createGradientTextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      colors: const [
        Color(0xFFD8D8D8),
        Color(0xFFFFFFFF),
      ],
    );
  }

  /// 创建金属质感文本样式
  static TextStyle createMetallicTextStyle({
    required double fontSize,
    FontWeight fontWeight = FontWeight.bold,
  }) {
    return createGradientTextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      colors: const [
        Color(0xFFB0BEC5),
        Color(0xFFECEFF1),
        Color(0xFFB0BEC5),
      ],
    );
  }

  /// 创建深空灰蓝色调渐变背景
  static BoxDecoration createSpaceBlueGradient() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Color(0xFF2C3E50),
          Color(0xFF546E7A),
        ],
        stops: [0.0, 1.0],
      ),
    );
  }

  /// 创建平板车模块渐变背景(浅灰蓝→银白)
  static BoxDecoration createFlatbedGradient() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFF78909C),
          Color(0xFFB0BEC5),
        ],
        stops: [0.0, 1.0],
      ),
    );
  }

  /// 创建集装箱模块渐变背景(浅灰橙→琥珀金)
  static BoxDecoration createContainerGradient() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFFFFB74D),
          Color(0xFFFFA726),
        ],
        stops: [0.0, 1.0],
      ),
    );
  }

  /// 创建柔和单层背景渐变(顶部浅灰蓝→底部透白)
  static BoxDecoration createSoftGradient() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Color(0xFF546E7A),
          Colors.white,
        ],
        stops: [0.0, 1.0],
      ),
    );
  }

  /// 创建玻璃态卡片装饰器
  static BoxDecoration createGlassDecoration({
    required BorderRadius borderRadius,
    Color backgroundColor = Colors.transparent,
    Color borderColor = Colors.white30,
    List<BoxShadow>? boxShadow,
  }) {
    return BoxDecoration(
      color: backgroundColor,
      borderRadius: borderRadius,
      border: Border.all(
        color: borderColor,
        width: 1.0,
      ),
      boxShadow: boxShadow,
    );
  }
}

/// 晕影效果的自定义画布
/// 用于创建弱光环境的晕影效果
class VignetteCanvas extends CustomPainter {
  final double intensity;
  final Color color;

  VignetteCanvas({
    this.intensity = 0.3,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // 创建从边缘到中心的径向渐变
    final gradient = RadialGradient(
      center: Alignment.center,
      radius: 1.0,
      colors: [
        Colors.transparent,
        color.withValues(alpha: 0.1),
      ],
      stops: [0.6, 1.0],
    );

    final paint = Paint()
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.fill;

    canvas.drawRect(rect, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 微交互动画控制器 - 用于创建微妙的UI交互效果
class MicroInteractionController {
  // 清晰度变化动画 - 用于点击时玻璃模块短暂"清晰化"
  static Animation<double> createClarityAnimation(
      AnimationController controller) {
    return Tween<double>(begin: 5.0, end: 0.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  // 数据加载渐变动画 - 用于数字加载渐变浮现效果
  static Animation<double> createDataLoadingAnimation(
      AnimationController controller) {
    return Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: Curves.easeInOut,
      ),
    );
  }
}

/// 文本渐变扩展
extension GradientTextExtension on Text {
  /// 为文本应用渐变效果
  Widget applyGradient(LinearGradient gradient) {
    return ShaderMask(
      blendMode: BlendMode.srcIn,
      shaderCallback: (bounds) => gradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      child: this,
    );
  }
}
