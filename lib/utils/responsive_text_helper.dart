import 'package:flutter/material.dart';
import 'responsive_helper.dart';

/// 📝 响应式文本助手类
/// 提供统一的响应式字体大小管理
class ResponsiveTextHelper {
  /// 字体大小常量定义
  static const double h1 = 32.0;
  static const double h2 = 28.0;
  static const double h3 = 24.0;
  static const double h4 = 20.0;
  static const double h5 = 18.0;
  static const double h6 = 16.0;

  static const double bodyLarge = 16.0;
  static const double bodyMedium = 14.0;
  static const double bodySmall = 12.0;

  static const double captionLarge = 12.0;
  static const double captionMedium = 11.0;
  static const double captionSmall = 10.0;

  static const double labelLarge = 14.0;
  static const double labelMedium = 12.0;
  static const double labelSmall = 11.0;

  /// 获取响应式标题字体大小
  static double getH1(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, h1);
  static double getH2(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, h2);
  static double getH3(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, h3);
  static double getH4(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, h4);
  static double getH5(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, h5);
  static double getH6(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, h6);

  /// 获取响应式正文字体大小
  static double getBodyLarge(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, bodyLarge);
  static double getBodyMedium(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, bodyMedium);
  static double getBodySmall(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, bodySmall);

  /// 获取响应式说明文字字体大小
  static double getCaptionLarge(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, captionLarge);
  static double getCaptionMedium(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, captionMedium);
  static double getCaptionSmall(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, captionSmall);

  /// 获取响应式标签字体大小
  static double getLabelLarge(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, labelLarge);
  static double getLabelMedium(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, labelMedium);
  static double getLabelSmall(BuildContext context) =>
      ResponsiveHelper.getResponsiveFontSize(context, labelSmall);

  /// 获取自定义响应式字体大小
  static double getCustomSize(BuildContext context, double baseSize) {
    return ResponsiveHelper.getResponsiveFontSize(context, baseSize);
  }

  /// 创建响应式TextStyle
  static TextStyle createResponsiveTextStyle(
    BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? letterSpacing,
    double? height,
    TextDecoration? decoration,
    String? fontFamily,
  }) {
    return TextStyle(
      fontSize: fontSize != null
          ? ResponsiveHelper.getResponsiveFontSize(context, fontSize)
          : null,
      fontWeight: fontWeight,
      color: color,
      letterSpacing: letterSpacing,
      height: height,
      decoration: decoration,
      fontFamily: fontFamily,
    );
  }

  /// 快速创建标题样式
  static TextStyle createHeadingStyle(
    BuildContext context, {
    int level = 1,
    Color? color,
    FontWeight? fontWeight,
  }) {
    double fontSize;
    switch (level) {
      case 1:
        fontSize = h1;
        break;
      case 2:
        fontSize = h2;
        break;
      case 3:
        fontSize = h3;
        break;
      case 4:
        fontSize = h4;
        break;
      case 5:
        fontSize = h5;
        break;
      case 6:
        fontSize = h6;
        break;
      default:
        fontSize = h3;
        break;
    }

    return TextStyle(
      fontSize: ResponsiveHelper.getResponsiveFontSize(context, fontSize),
      color: color,
      fontWeight: fontWeight ?? FontWeight.bold,
    );
  }

  /// 快速创建正文样式
  static TextStyle createBodyStyle(
    BuildContext context, {
    String size = 'medium',
    Color? color,
    FontWeight? fontWeight,
  }) {
    double fontSize;
    switch (size) {
      case 'large':
        fontSize = bodyLarge;
        break;
      case 'medium':
        fontSize = bodyMedium;
        break;
      case 'small':
        fontSize = bodySmall;
        break;
      default:
        fontSize = bodyMedium;
        break;
    }

    return TextStyle(
      fontSize: ResponsiveHelper.getResponsiveFontSize(context, fontSize),
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 快速创建标签样式
  static TextStyle createLabelStyle(
    BuildContext context, {
    String size = 'medium',
    Color? color,
    FontWeight? fontWeight,
  }) {
    double fontSize;
    switch (size) {
      case 'large':
        fontSize = labelLarge;
        break;
      case 'medium':
        fontSize = labelMedium;
        break;
      case 'small':
        fontSize = labelSmall;
        break;
      default:
        fontSize = labelMedium;
        break;
    }

    return TextStyle(
      fontSize: ResponsiveHelper.getResponsiveFontSize(context, fontSize),
      color: color,
      fontWeight: fontWeight,
    );
  }

  /// 快速创建说明文字样式
  static TextStyle createCaptionStyle(
    BuildContext context, {
    String size = 'medium',
    Color? color,
    FontWeight? fontWeight,
  }) {
    double fontSize;
    switch (size) {
      case 'large':
        fontSize = captionLarge;
        break;
      case 'medium':
        fontSize = captionMedium;
        break;
      case 'small':
        fontSize = captionSmall;
        break;
      default:
        fontSize = captionMedium;
        break;
    }

    return TextStyle(
      fontSize: ResponsiveHelper.getResponsiveFontSize(context, fontSize),
      color: color,
      fontWeight: fontWeight,
    );
  }
}


