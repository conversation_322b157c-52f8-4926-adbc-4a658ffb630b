import 'package:flutter/foundation.dart';

/// 🔧 应用配置管理
class AppConfig {
  /// 是否为发布版本
  static const bool isRelease = kReleaseMode;

  /// 是否启用调试日志
  static const bool enableDebugLogs = !isRelease;

  /// 是否启用性能监控
  static const bool enablePerformanceMonitoring = !isRelease;

  /// 是否启用详细错误报告
  static const bool enableDetailedErrorReporting = !isRelease;

  /// 是否启用UI调试信息
  static const bool enableUIDebugging = false; // 生产环境完全关闭

  /// 是否启用网络调试
  static const bool enableNetworkDebugging = false; // 生产环境完全关闭

  /// 应用版本信息
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';

  /// 获取应用模式描述
  static String get modeDescription {
    if (kReleaseMode) return '生产版本';
    if (kProfileMode) return '性能分析版本';
    return '开发版本';
  }
}
