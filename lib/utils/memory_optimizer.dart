import 'package:flutter/painting.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/foundation.dart';
import 'app_logger.dart';

/// 🚀 高级内存管理优化器
/// 为装运卫士系统提供企业级内存管理
class MemoryOptimizer {
  static bool _isInitialized = false;

  /// 初始化内存优化配置
  static void initialize() {
    if (_isInitialized) return;

    try {
      // 1. 图像缓存优化 - 设置为100MB
      PaintingBinding.instance.imageCache.maximumSizeBytes = 100 << 20;
      PaintingBinding.instance.imageCache.maximumSize = 500; // 最大图像数量

      // 2. 图像缓存策略优化
      _configureImageCache();

      // 3. 内存压力监听
      _setupMemoryPressureListener();

      _isInitialized = true;
      AppLogger.info('[MemoryOptimizer] 内存优化初始化完成');
      AppLogger.info(
          '[MemoryOptimizer] 图像缓存: ${(PaintingBinding.instance.imageCache.maximumSizeBytes / (1024 * 1024)).toInt()}MB');
    } catch (e) {
      AppLogger.error('[MemoryOptimizer] 初始化失败: $e');
    }
  }

  /// 配置图像缓存策略
  static void _configureImageCache() {
    final imageCache = PaintingBinding.instance.imageCache;

    // 实时监控缓存状态
    if (kDebugMode) {
      AppLogger.debug(
          '[MemoryOptimizer] 当前缓存大小: ${imageCache.currentSizeBytes ~/ (1024 * 1024)}MB');
      AppLogger.debug('[MemoryOptimizer] 当前缓存图像数: ${imageCache.currentSize}');
    }
  }

  /// 设置内存压力监听器
  static void _setupMemoryPressureListener() {
    // 在实际应用中，可以监听系统内存压力事件
    // 这里提供基础框架
  }

  /// 清理图像缓存
  static void clearImageCache() {
    try {
      PaintingBinding.instance.imageCache.clear();
      AppLogger.info('[MemoryOptimizer] 图像缓存已清理');
    } catch (e) {
      AppLogger.error('[MemoryOptimizer] 清理缓存失败: $e');
    }
  }

  /// 强制垃圾回收（仅调试模式）
  static void forceGarbageCollection() {
    if (kDebugMode) {
      // 注意：强制GC可能影响性能，仅用于调试
      AppLogger.debug('[MemoryOptimizer] 执行垃圾回收');
    }
  }

  /// 获取内存使用统计
  static Map<String, dynamic> getMemoryStats() {
    final imageCache = PaintingBinding.instance.imageCache;
    return {
      'imageCacheSize': imageCache.currentSizeBytes,
      'imageCacheSizeMB':
          (imageCache.currentSizeBytes / (1024 * 1024)).toStringAsFixed(2),
      'imageCacheCount': imageCache.currentSize,
      'maxCacheSize': imageCache.maximumSizeBytes,
      'maxCacheSizeMB': (imageCache.maximumSizeBytes / (1024 * 1024)).toInt(),
      'maxCacheCount': imageCache.maximumSize,
    };
  }

  /// 优化内存使用
  static void optimizeMemory() {
    try {
      // 1. 清理图像缓存中的过期项
      final imageCache = PaintingBinding.instance.imageCache;
      if (imageCache.currentSizeBytes > (imageCache.maximumSizeBytes * 0.8)) {
        imageCache.clear();
        AppLogger.info('[MemoryOptimizer] 内存压力清理执行');
      }

      // 2. 强制执行微任务队列
      if (kDebugMode) {
        AppLogger.debug('[MemoryOptimizer] 微任务队列优化完成');
      }
    } catch (e) {
      AppLogger.error('[MemoryOptimizer] 内存优化失败: $e');
    }
  }

  /// 启动后的内存优化
  static void postStartupOptimization() {
    try {
      AppLogger.info('[MemoryOptimizer] 启动优化完成');
    } catch (e) {
      AppLogger.error('[MemoryOptimizer] 启动后优化失败: $e');
    }
  }

  /// 应用生命周期内存管理
  static void handleAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.paused:
        // 应用暂停时清理缓存
        optimizeMemory();
        AppLogger.info('[MemoryOptimizer] 应用暂停，执行内存优化');
        break;
      case AppLifecycleState.resumed:
        // 应用恢复时重新配置
        _configureImageCache();
        AppLogger.info('[MemoryOptimizer] 应用恢复，重新配置缓存');
        break;
      case AppLifecycleState.detached:
        // 应用分离时清理所有缓存
        clearImageCache();
        AppLogger.info('[MemoryOptimizer] 应用分离，清理所有缓存');
        break;
      default:
        break;
    }
  }
}
