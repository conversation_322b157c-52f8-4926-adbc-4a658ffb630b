/// 🆕 16.0.0优化：路由历史记录管理器
class RouteHistoryManager {
  static final List<String> _history = [];
  static const int _maxHistorySize = 50;

  /// 添加路由到历史记录
  static void addRoute(String routeName) {
    _history.add(routeName);
    if (_history.length > _maxHistorySize) {
      _history.removeAt(0);
    }
  }

  /// 获取路由历史记录
  static List<String> getHistory() {
    return List.unmodifiable(_history);
  }

  /// 获取上一个路由
  static String? getPreviousRoute() {
    if (_history.length > 1) {
      return _history[_history.length - 2];
    }
    return null;
  }

  /// 清空历史记录
  static void clearHistory() {
    _history.clear();
  }

  /// 检查是否包含特定路由
  static bool containsRoute(String routeName) {
    return _history.contains(routeName);
  }
}
