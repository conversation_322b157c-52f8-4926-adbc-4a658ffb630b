import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// 🚀 路由配置管理器 - 适配go_router 16.0.0
/// 提供集中化的路由配置管理和优化
class RouterConfigManager {
  /// 🆕 16.0.0优化：路由配置常量
  static const Map<String, String> _routePaths = {
    'launcher': '/',
    'home': '/home',
    'activation': '/activation',
    'template-selection': '/template-selection',
    'new-task': '/enhanced-task/new',
    'task-detail': '/task-detail',
    'result': '/result',
    'security-management': '/security-management',
    'about': '/about',
    'performance-stats': '/performance-stats',
    'enterprise-control': '/enterprise-control',

    'workload-management': '/workload-management',
    'workload-statistics': '/workload-statistics',
    'admin-management': '/admin-management',
    'enhanced-security-management': '/enhanced-security-management',
    'worker-selection': '/worker-selection',
  };

  /// 🆕 16.0.0优化：路由元数据
  static const Map<String, RouteMetadata> _routeMetadata = {
    'launcher': RouteMetadata(
      title: '启动页',
      icon: Icons.launch,
      requiresAuth: false,
      isRoot: true,
    ),
    'home': RouteMetadata(
      title: '首页',
      icon: Icons.home,
      requiresAuth: true,
      isRoot: true,
    ),
    'activation': RouteMetadata(
      title: '激活',
      icon: Icons.vpn_key,
      requiresAuth: false,
      isRoot: false,
    ),
    'template-selection': RouteMetadata(
      title: '模板选择',
      icon: Icons.list_alt,
      requiresAuth: true,
      isRoot: false,
    ),
    'new-task': RouteMetadata(
      title: '新建任务',
      icon: Icons.add_task,
      requiresAuth: true,
      isRoot: false,
    ),
    'task-detail': RouteMetadata(
      title: '任务详情',
      icon: Icons.description,
      requiresAuth: true,
      isRoot: false,
    ),
    'result': RouteMetadata(
      title: '结果页面',
      icon: Icons.assessment,
      requiresAuth: true,
      isRoot: false,
    ),
    'security-management': RouteMetadata(
      title: '安全管理',
      icon: Icons.security,
      requiresAuth: true,
      isRoot: false,
    ),
    'about': RouteMetadata(
      title: '关于',
      icon: Icons.info,
      requiresAuth: false,
      isRoot: false,
    ),
    'performance-stats': RouteMetadata(
      title: '性能统计',
      icon: Icons.analytics,
      requiresAuth: true,
      isRoot: false,
    ),
    'enterprise-control': RouteMetadata(
      title: '企业控制',
      icon: Icons.business,
      requiresAuth: true,
      isRoot: false,
    ),

    'workload-management': RouteMetadata(
      title: '工作负载管理',
      icon: Icons.work,
      requiresAuth: true,
      isRoot: false,
    ),
    'workload-statistics': RouteMetadata(
      title: '工作负载统计',
      icon: Icons.bar_chart,
      requiresAuth: true,
      isRoot: false,
    ),
    'admin-management': RouteMetadata(
      title: '管理员管理',
      icon: Icons.admin_panel_settings,
      requiresAuth: true,
      isRoot: false,
    ),
    'enhanced-security-management': RouteMetadata(
      title: '增强安全管理',
      icon: Icons.enhanced_encryption,
      requiresAuth: true,
      isRoot: false,
    ),
    'worker-selection': RouteMetadata(
      title: '工人选择',
      icon: Icons.people,
      requiresAuth: true,
      isRoot: false,
    ),
  };

  /// 🆕 16.0.0优化：获取路由路径
  static String? getRoutePath(String routeName) {
    return _routePaths[routeName];
  }

  /// 🆕 16.0.0优化：获取路由元数据
  static RouteMetadata? getRouteMetadata(String routeName) {
    return _routeMetadata[routeName];
  }

  /// 🆕 16.0.0优化：获取所有路由名称
  static List<String> getAllRouteNames() {
    return _routePaths.keys.toList();
  }

  /// 🆕 16.0.0优化：获取需要认证的路由
  static List<String> getAuthRequiredRoutes() {
    return _routeMetadata.entries
        .where((entry) => entry.value.requiresAuth)
        .map((entry) => entry.key)
        .toList();
  }

  /// 🆕 16.0.0优化：获取根页面路由
  static List<String> getRootRoutes() {
    return _routeMetadata.entries
        .where((entry) => entry.value.isRoot)
        .map((entry) => entry.key)
        .toList();
  }

  /// 🆕 16.0.0优化：检查路由是否需要认证
  static bool requiresAuth(String routeName) {
    return _routeMetadata[routeName]?.requiresAuth ?? false;
  }

  /// 🆕 16.0.0优化：检查是否为根页面
  static bool isRootRoute(String routeName) {
    return _routeMetadata[routeName]?.isRoot ?? false;
  }

  /// 🆕 16.0.0优化：获取路由标题
  static String getRouteTitle(String routeName) {
    return _routeMetadata[routeName]?.title ?? routeName;
  }

  /// 🆕 16.0.0优化：获取路由图标
  static IconData getRouteIcon(String routeName) {
    return _routeMetadata[routeName]?.icon ?? Icons.route;
  }

  /// 🆕 16.0.0优化：创建优化的路由配置
  static GoRouter createOptimizedRouter({
    required List<GoRoute> routes,
    String initialLocation = '/',
    Widget Function(BuildContext, GoRouterState)? errorBuilder,
    String? Function(BuildContext, GoRouterState)? redirect,
    bool debugLogDiagnostics = false,
  }) {
    return GoRouter(
      initialLocation: initialLocation,
      routes: routes,
      errorBuilder: errorBuilder ?? _defaultErrorBuilder,
      redirect: redirect ?? _defaultRedirect,
      debugLogDiagnostics: debugLogDiagnostics,
      // 🆕 16.0.0新特性：改进的配置选项
      restorationScopeId: 'loadguard_router',
    );
  }

  /// 🆕 16.0.0优化：默认重定向逻辑
  static String? _defaultRedirect(BuildContext context, GoRouterState state) {
    // 这里可以实现全局重定向逻辑
    // 例如：检查用户认证状态、权限等
    return null;
  }

  /// 🆕 16.0.0优化：默认错误页面构建器
  static Widget _defaultErrorBuilder(
      BuildContext context, GoRouterState state) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('页面错误'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              '页面加载失败',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '路径: ${state.uri.path}',
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.goNamed('home'),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    );
  }

  /// 🆕 16.0.0优化：创建带缓存的页面构建器
  static Widget Function(BuildContext, GoRouterState) createCachedPageBuilder(
    Widget Function(BuildContext, GoRouterState) builder,
  ) {
    return (context, state) {
      // 这里可以实现页面缓存逻辑
      return builder(context, state);
    };
  }

  /// 🆕 16.0.0优化：创建带加载状态的页面构建器
  static Widget Function(BuildContext, GoRouterState) createLoadingPageBuilder(
    Widget Function(BuildContext, GoRouterState) builder,
  ) {
    return (context, state) {
      return FutureBuilder(
        future: Future.delayed(const Duration(milliseconds: 100)),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          }
          return builder(context, state);
        },
      );
    };
  }

  /// 🆕 16.0.0优化：创建带认证检查的页面构建器
  static Widget Function(BuildContext, GoRouterState) createAuthPageBuilder(
    Widget Function(BuildContext, GoRouterState) builder,
    bool requiresAuth,
  ) {
    return (context, state) {
      if (requiresAuth) {
        // 这里可以添加认证检查逻辑
        // 例如：检查用户是否已登录
        return builder(context, state);
      }
      return builder(context, state);
    };
  }

  /// 🆕 16.0.0优化：路由参数验证器
  static bool validateRouteParameters(
      GoRouterState state, List<String> requiredParams) {
    for (final param in requiredParams) {
      if (state.pathParameters[param] == null) {
        return false;
      }
    }
    return true;
  }

  /// 🆕 16.0.0优化：查询参数验证器
  static bool validateQueryParameters(
      GoRouterState state, List<String> requiredParams) {
    for (final param in requiredParams) {
      if (state.uri.queryParameters[param] == null) {
        return false;
      }
    }
    return true;
  }

  // 🆕 16.0.0优化：路由历史记录管理器已移至独立文件
  // 使用 RouteHistoryManager 类

  // 🆕 16.0.0优化：路由性能监控器已移至独立文件
  // 使用 RoutePerformanceMonitor 类
}

/// 🆕 16.0.0优化：路由元数据类
class RouteMetadata {
  final String title;
  final IconData icon;
  final bool requiresAuth;
  final bool isRoot;
  final Map<String, dynamic>? additionalData;

  const RouteMetadata({
    required this.title,
    required this.icon,
    required this.requiresAuth,
    required this.isRoot,
    this.additionalData,
  });

  @override
  String toString() {
    return 'RouteMetadata(title: $title, requiresAuth: $requiresAuth, isRoot: $isRoot)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RouteMetadata &&
        other.title == title &&
        other.icon == icon &&
        other.requiresAuth == requiresAuth &&
        other.isRoot == isRoot;
  }

  @override
  int get hashCode {
    return title.hashCode ^
        icon.hashCode ^
        requiresAuth.hashCode ^
        isRoot.hashCode;
  }
}
