import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

/// 🎯 简化的导航助手
/// 参考微信、抖音等主流应用的导航模式
/// 移除冗余的双重实现，提供统一的导航体验
class SimpleNavigationHelper {
  // 双击退出确认间隔
  static DateTime? _lastBackPressTime;
  static const Duration _exitConfirmDuration = Duration(seconds: 2);

  /// 📱 标准页面导航封装
  /// 适用于大部分页面的通用导航需求
  static Widget buildPage({
    required Widget child,
    bool enableSwipeBack = true,
    bool isRootPage = false,
    VoidCallback? onBackPressed,
    String? exitMessage,
  }) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        // 根页面双击退出逻辑
        if (isRootPage) {
          final shouldExit = await _handleRootPageExit(child, exitMessage);
          if (shouldExit) {
            SystemNavigator.pop();
          }
          return;
        }

        // 执行自定义或默认返回逻辑
        if (onBackPressed != null) {
          onBackPressed();
        } else {
          _performDefaultBack(child);
        }
      },
      child: enableSwipeBack
          ? _buildSwipeBackDetector(child, onBackPressed, isRootPage)
          : child,
    );
  }

  /// 🏠 根页面导航（首页专用）
  static Widget buildRootPage({
    required Widget child,
    String? exitMessage,
    bool enableSwipeBack = false, // 首页通常不需要侧滑
  }) {
    return buildPage(
      child: child,
      isRootPage: true,
      enableSwipeBack: enableSwipeBack,
      exitMessage: exitMessage ?? '再按一次退出应用',
    );
  }

  /// 📋 标准内页导航
  static Widget buildStandardPage({
    required Widget child,
    VoidCallback? onBackPressed,
    bool enableSwipeBack = true,
  }) {
    return buildPage(
      child: child,
      enableSwipeBack: enableSwipeBack,
      onBackPressed: onBackPressed,
    );
  }

  /// 👆 侧滑返回检测器（简化版）
  static Widget _buildSwipeBackDetector(
    Widget child,
    VoidCallback? onBackPressed,
    bool isRootPage,
  ) {
    return GestureDetector(
      onHorizontalDragEnd: (details) {
        // 简化的侧滑逻辑：从左边缘向右滑动
        if (details.globalPosition.dx > 100 &&
            details.velocity.pixelsPerSecond.dx > 300) {
          if (isRootPage) {
            // 根页面不响应侧滑返回
            return;
          }

          // 触觉反馈
          HapticFeedback.lightImpact();

          // 执行返回
          if (onBackPressed != null) {
            onBackPressed();
          } else {
            _performDefaultBack(child);
          }
        }
      },
      child: child,
    );
  }

  /// 🔄 默认返回逻辑
  static void _performDefaultBack(Widget child) {
    final context = _findContext(child);
    if (context != null && context.mounted) {
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      } else {
        context.go('/home');
      }
    }
  }

  /// 🏠 根页面双击退出处理
  static Future<bool> _handleRootPageExit(
      Widget child, String? exitMessage) async {
    final now = DateTime.now();

    if (_lastBackPressTime == null ||
        now.difference(_lastBackPressTime!) > _exitConfirmDuration) {
      _lastBackPressTime = now;

      // 显示退出提示
      final context = _findContext(child);
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(exitMessage ?? '再按一次退出应用'),
            duration: _exitConfirmDuration,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
      return false;
    }

    return true;
  }

  /// 🔍 安全的Context获取
  static BuildContext? _findContext(Widget child) {
    // 尝试从GlobalKey获取
    if (child.key is GlobalKey) {
      return (child.key as GlobalKey).currentContext;
    }

    // 如果没有找到，返回null，由调用方处理
    return null;
  }

  /// 📱 弹窗导航
  static Future<T?> showManagedDialog<T>({
    required BuildContext context,
    required Widget dialog,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => PopScope(
        canPop: barrierDismissible,
        onPopInvokedWithResult: (didPop, result) {
          if (!didPop && barrierDismissible) {
            Navigator.of(context).pop();
          }
        },
        child: dialog,
      ),
    );
  }

  /// 📄 底部Sheet导航
  static Future<T?> showManagedBottomSheet<T>({
    required BuildContext context,
    required Widget Function(BuildContext) builder,
    bool isDismissible = true,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isDismissible: isDismissible,
      backgroundColor: Colors.transparent,
      builder: (context) => PopScope(
        canPop: isDismissible,
        onPopInvokedWithResult: (didPop, result) {
          if (!didPop && isDismissible) {
            Navigator.of(context).pop();
          }
        },
        child: builder(context),
      ),
    );
  }

  /// 🚀 快速导航方法
  static void goBack(BuildContext context) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    } else {
      context.go('/home');
    }
  }

  static void goHome(BuildContext context) {
    context.go('/home');
  }

  static void goToPage(BuildContext context, String route) {
    context.go(route);
  }
}

/// 🎨 页面过渡动画（可选）
class SwipeBackPageRoute<T> extends PageRoute<T> {
  final Widget child;

  SwipeBackPageRoute({
    required this.child,
    RouteSettings? settings,
  }) : super(settings: settings);

  @override
  bool get maintainState => true;

  @override
  Duration get transitionDuration => const Duration(milliseconds: 300);

  @override
  Color? get barrierColor => null;

  @override
  String? get barrierLabel => null;

  @override
  Widget buildPage(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation) {
    return child;
  }

  @override
  Widget buildTransitions(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(1.0, 0.0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Curves.easeInOut,
      )),
      child: child,
    );
  }
}
