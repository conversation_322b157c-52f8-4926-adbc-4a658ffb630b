import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// 📱 响应式布局助手类
/// 提供统一的多终端适配解决方案
class ResponsiveHelper {
  /// 设备类型枚举
  static const double mobileBreakpoint = 480;
  static const double tabletBreakpoint = 768;
  static const double desktopBreakpoint = 1024;
  static const double largeDesktopBreakpoint = 1440;

  /// 获取设备类型
  static DeviceType getDeviceType(BuildContext context) {
    return getDeviceTypeFromWidth(MediaQuery.of(context).size.width);
  }

  /// 根据宽度获取设备类型
  static DeviceType getDeviceTypeFromWidth(double width) {
    if (width < mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < tabletBreakpoint) {
      return DeviceType.mobileLarge;
    } else if (width < desktopBreakpoint) {
      return DeviceType.tablet;
    } else if (width < largeDesktopBreakpoint) {
      return DeviceType.desktop;
    } else {
      return DeviceType.largeDesktop;
    }
  }

  /// 检查是否为移动设备
  static bool isMobile(BuildContext context) {
    final deviceType = getDeviceType(context);
    return deviceType == DeviceType.mobile ||
        deviceType == DeviceType.mobileLarge;
  }

  /// 检查是否为平板设备
  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == DeviceType.tablet;
  }

  /// 检查是否为桌面设备
  static bool isDesktop(BuildContext context) {
    final deviceType = getDeviceType(context);
    return deviceType == DeviceType.desktop ||
        deviceType == DeviceType.largeDesktop;
  }

  /// 获取平台类型
  static PlatformType getPlatformType() {
    if (kIsWeb) {
      return PlatformType.web;
    } else if (Platform.isAndroid) {
      return PlatformType.android;
    } else if (Platform.isIOS) {
      return PlatformType.ios;
    } else if (Platform.isWindows) {
      return PlatformType.windows;
    } else if (Platform.isMacOS) {
      return PlatformType.macos;
    } else if (Platform.isLinux) {
      return PlatformType.linux;
    } else {
      return PlatformType.other;
    }
  }

  /// 检查是否为移动平台
  static bool isMobilePlatform() {
    final platform = getPlatformType();
    return platform == PlatformType.android || platform == PlatformType.ios;
  }

  /// 检查是否为桌面平台
  static bool isDesktopPlatform() {
    final platform = getPlatformType();
    return platform == PlatformType.windows ||
        platform == PlatformType.macos ||
        platform == PlatformType.linux;
  }

  /// 检查屏幕方向
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  /// 获取屏幕尺寸信息
  static ScreenSizeInfo getScreenSizeInfo(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;

    return ScreenSizeInfo(
      width: size.width,
      height: size.height,
      devicePixelRatio: devicePixelRatio,
      deviceType: getDeviceType(context),
      platformType: getPlatformType(),
      isLandscape: isLandscape(context),
    );
  }

  /// 获取响应式字体大小
  static double getResponsiveFontSize(
      BuildContext context, double baseFontSize) {
    final deviceType = getDeviceType(context);
    final screenWidth = MediaQuery.of(context).size.width;

    // 基础缩放因子
    double scaleFactor = 1.0;

    switch (deviceType) {
      case DeviceType.mobile:
        scaleFactor = max(0.8, min(1.0, screenWidth / 375)); // 以iPhone X为基准
        break;
      case DeviceType.mobileLarge:
        scaleFactor = max(0.9, min(1.1, screenWidth / 414)); // 以iPhone Plus为基准
        break;
      case DeviceType.tablet:
        scaleFactor = max(1.0, min(1.3, screenWidth / 768)); // 以iPad为基准
        break;
      case DeviceType.desktop:
        scaleFactor = max(1.1, min(1.4, screenWidth / 1024));
        break;
      case DeviceType.largeDesktop:
        scaleFactor = max(1.2, min(1.6, screenWidth / 1440));
        break;
    }

    // 检查用户字体缩放设置
    final textScaler = MediaQuery.of(context).textScaler;
    final textScaleFactor = textScaler.scale(1.0);

    return baseFontSize * scaleFactor * min(textScaleFactor, 1.3); // 限制最大缩放
  }

  /// 获取响应式间距
  static double getResponsiveSpacing(BuildContext context, double baseSpacing) {
    final deviceType = getDeviceType(context);

    switch (deviceType) {
      case DeviceType.mobile:
        return baseSpacing * 0.8;
      case DeviceType.mobileLarge:
        return baseSpacing * 0.9;
      case DeviceType.tablet:
        return baseSpacing * 1.1;
      case DeviceType.desktop:
        return baseSpacing * 1.2;
      case DeviceType.largeDesktop:
        return baseSpacing * 1.4;
    }
  }

  /// 获取响应式边距
  static EdgeInsets getResponsivePadding(
      BuildContext context, EdgeInsets basePadding) {
    final scaleFactor = _getScaleFactor(context);

    return EdgeInsets.only(
      left: basePadding.left * scaleFactor,
      top: basePadding.top * scaleFactor,
      right: basePadding.right * scaleFactor,
      bottom: basePadding.bottom * scaleFactor,
    );
  }

  /// 获取响应式布局列数
  static int getResponsiveColumns(
    BuildContext context, {
    int mobileColumns = 1,
    int mobileLargeColumns = 1,
    int tabletColumns = 2,
    int desktopColumns = 3,
    int largeDesktopColumns = 4,
  }) {
    final deviceType = getDeviceType(context);

    switch (deviceType) {
      case DeviceType.mobile:
        return mobileColumns;
      case DeviceType.mobileLarge:
        return mobileLargeColumns;
      case DeviceType.tablet:
        return tabletColumns;
      case DeviceType.desktop:
        return desktopColumns;
      case DeviceType.largeDesktop:
        return largeDesktopColumns;
    }
  }

  /// 获取响应式最大宽度
  static double getResponsiveMaxWidth(
    BuildContext context, {
    double? mobileMaxWidth,
    double? tabletMaxWidth,
    double? desktopMaxWidth,
  }) {
    final deviceType = getDeviceType(context);
    final screenWidth = MediaQuery.of(context).size.width;

    switch (deviceType) {
      case DeviceType.mobile:
      case DeviceType.mobileLarge:
        return mobileMaxWidth ?? screenWidth;
      case DeviceType.tablet:
        return tabletMaxWidth ?? min(600, screenWidth * 0.9);
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return desktopMaxWidth ?? min(1200, screenWidth * 0.8);
    }
  }

  /// 获取缩放因子
  static double _getScaleFactor(BuildContext context) {
    final deviceType = getDeviceType(context);

    switch (deviceType) {
      case DeviceType.mobile:
        return 0.8;
      case DeviceType.mobileLarge:
        return 0.9;
      case DeviceType.tablet:
        return 1.1;
      case DeviceType.desktop:
        return 1.2;
      case DeviceType.largeDesktop:
        return 1.4;
    }
  }

  /// 检查是否支持鼠标悬停
  static bool supportsHover() {
    return isDesktopPlatform() || kIsWeb;
  }

  /// 检查是否支持触摸
  static bool supportsTouch() {
    return isMobilePlatform() || kIsWeb;
  }

  /// 获取安全区域
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// 获取键盘高度
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  /// 检查是否有键盘
  static bool hasKeyboard(BuildContext context) {
    return getKeyboardHeight(context) > 0;
  }
}

/// 设备类型枚举
enum DeviceType {
  mobile, // < 480px
  mobileLarge, // 480px - 768px
  tablet, // 768px - 1024px
  desktop, // 1024px - 1440px
  largeDesktop, // > 1440px
}

/// 平台类型枚举
enum PlatformType {
  android,
  ios,
  web,
  windows,
  macos,
  linux,
  other,
}

/// 屏幕尺寸信息
class ScreenSizeInfo {
  final double width;
  final double height;
  final double devicePixelRatio;
  final DeviceType deviceType;
  final PlatformType platformType;
  final bool isLandscape;

  const ScreenSizeInfo({
    required this.width,
    required this.height,
    required this.devicePixelRatio,
    required this.deviceType,
    required this.platformType,
    required this.isLandscape,
  });

  /// 获取逻辑像素密度等级
  DensityLevel get densityLevel {
    if (devicePixelRatio < 2.0) {
      return DensityLevel.mdpi;
    } else if (devicePixelRatio < 3.0) {
      return DensityLevel.hdpi;
    } else if (devicePixelRatio < 4.0) {
      return DensityLevel.xhdpi;
    } else {
      return DensityLevel.xxhdpi;
    }
  }

  /// 获取屏幕对角线尺寸（英寸）
  double get diagonalInches {
    final diagonalPixels = sqrt(width * width + height * height);
    return diagonalPixels / (devicePixelRatio * 160); // 160 DPI为基准
  }

  @override
  String toString() {
    return 'ScreenSizeInfo(width: $width, height: $height, deviceType: $deviceType, platformType: $platformType)';
  }
}

/// 像素密度等级
enum DensityLevel {
  mdpi, // 1x
  hdpi, // 1.5x
  xhdpi, // 2x
  xxhdpi, // 3x+
}

/// 响应式构建器
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, DeviceType deviceType)? mobile;
  final Widget Function(BuildContext context, DeviceType deviceType)?
      mobileLarge;
  final Widget Function(BuildContext context, DeviceType deviceType)? tablet;
  final Widget Function(BuildContext context, DeviceType deviceType)? desktop;
  final Widget Function(BuildContext context, DeviceType deviceType)?
      largeDesktop;
  final Widget Function(BuildContext context, DeviceType deviceType)
      defaultBuilder;

  const ResponsiveBuilder({
    Key? key,
    this.mobile,
    this.mobileLarge,
    this.tablet,
    this.desktop,
    this.largeDesktop,
    required this.defaultBuilder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveHelper.getDeviceType(context);

    switch (deviceType) {
      case DeviceType.mobile:
        return mobile?.call(context, deviceType) ??
            defaultBuilder(context, deviceType);
      case DeviceType.mobileLarge:
        return mobileLarge?.call(context, deviceType) ??
            mobile?.call(context, deviceType) ??
            defaultBuilder(context, deviceType);
      case DeviceType.tablet:
        return tablet?.call(context, deviceType) ??
            mobileLarge?.call(context, deviceType) ??
            mobile?.call(context, deviceType) ??
            defaultBuilder(context, deviceType);
      case DeviceType.desktop:
        return desktop?.call(context, deviceType) ??
            tablet?.call(context, deviceType) ??
            defaultBuilder(context, deviceType);
      case DeviceType.largeDesktop:
        return largeDesktop?.call(context, deviceType) ??
            desktop?.call(context, deviceType) ??
            tablet?.call(context, deviceType) ??
            defaultBuilder(context, deviceType);
    }
  }
}
