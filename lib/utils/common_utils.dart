import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:image/image.dart' as img;

/// 🛠️ 通用工具类
/// 提取项目中重复的代码，提供统一的公共方法
class CommonUtils {
  /// 📁 获取应用文档目录
  static Future<String> getAppDocumentsPath() async {
    final directory = await getApplicationDocumentsDirectory();
    return directory.path;
  }

  /// 📁 获取应用缓存目录
  static Future<String> getAppCachePath() async {
    final directory = await getTemporaryDirectory();
    return directory.path;
  }

  /// 🖼️ 图片压缩处理
  static Future<Uint8List> compressImage(
    Uint8List imageData, {
    int maxWidth = 1920,
    int maxHeight = 1080,
    int quality = 85,
  }) async {
    try {
      final image = img.decodeImage(imageData);
      if (image == null) return imageData;

      // 计算压缩后的尺寸
      double scale = 1.0;
      if (image.width > maxWidth) {
        scale = maxWidth / image.width;
      }
      if (image.height * scale > maxHeight) {
        scale = maxHeight / image.height;
      }

      if (scale < 1.0) {
        final resized = img.copyResize(
          image,
          width: (image.width * scale).round(),
          height: (image.height * scale).round(),
        );
        return Uint8List.fromList(img.encodeJpg(resized, quality: quality));
      }

      return Uint8List.fromList(img.encodeJpg(image, quality: quality));
    } catch (e) {
      // debugPrint('图片压缩失败: $e');
      return imageData;
    }
  }

  /// 📊 文件大小格式化
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// ⏱️ 时间格式化
  static String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String hours = twoDigits(duration.inHours);
    String minutes = twoDigits(duration.inMinutes.remainder(60));
    String seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$hours:$minutes:$seconds';
  }

  /// 🔍 安全文件操作
  static Future<bool> safeFileExists(String path) async {
    try {
      final file = File(path);
      return await file.exists();
    } catch (e) {
      // debugPrint('文件存在检查失败: $e');
      return false;
    }
  }

  /// 📝 安全文件读取
  static Future<String?> safeReadFile(String path) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        return await file.readAsString();
      }
      return null;
    } catch (e) {
      // debugPrint('文件读取失败: $e');
      return null;
    }
  }

  /// 💾 安全文件写入
  static Future<bool> safeWriteFile(String path, String content) async {
    try {
      final file = File(path);
      await file.writeAsString(content);
      return true;
    } catch (e) {
      // debugPrint('文件写入失败: $e');
      return false;
    }
  }

  /// 🗑️ 安全文件删除
  static Future<bool> safeDeleteFile(String path) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      // debugPrint('文件删除失败: $e');
      return false;
    }
  }

  /// 📁 创建目录
  static Future<bool> createDirectory(String path) async {
    try {
      final directory = Directory(path);
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      return true;
    } catch (e) {
      // debugPrint('目录创建失败: $e');
      return false;
    }
  }

  /// 🔄 重试机制
  static Future<T> retry<T>(
    Future<T> Function() operation, {
    int maxAttempts = 3,
    Duration delay = const Duration(seconds: 1),
  }) async {
    int attempts = 0;
    while (attempts < maxAttempts) {
      try {
        return await operation();
      } catch (e) {
        attempts++;
        if (attempts >= maxAttempts) {
          rethrow;
        }
        await Future.delayed(delay * attempts);
      }
    }
    throw Exception('重试次数已达上限');
  }

  /// 📊 内存使用监控
  static Map<String, dynamic> getMemoryInfo() {
    // 这里可以集成实际的内存监控库
    return {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'available': 'N/A', // 需要集成具体的内存监控
      'used': 'N/A',
      'total': 'N/A',
    };
  }

  /// 🔧 设备信息获取
  static Map<String, String> getDeviceInfo() {
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
      'locale': Platform.localeName,
    };
  }

  /// 🎨 颜色工具方法
  static Color blendColors(Color color1, Color color2, double ratio) {
    return Color.lerp(color1, color2, ratio)!;
  }

  static Color darkenColor(Color color, double amount) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    return hsl
        .withLightness((hsl.lightness - amount).clamp(0.0, 1.0))
        .toColor();
  }

  static Color lightenColor(Color color, double amount) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    return hsl
        .withLightness((hsl.lightness + amount).clamp(0.0, 1.0))
        .toColor();
  }
}
