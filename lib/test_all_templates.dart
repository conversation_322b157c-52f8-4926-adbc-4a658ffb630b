import 'package:flutter/material.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/photo_item.dart';
import 'package:loadguard/models/batch_info.dart';
import 'package:loadguard/models/recognition_result.dart';
import 'package:loadguard/models/template_config.dart';
import 'package:loadguard/services/pdf_service.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

/// 全模板PDF测试页面 - 验证所有任务类型
class TestAllTemplates extends StatefulWidget {
  const TestAllTemplates({Key? key}) : super(key: key);

  @override
  State<TestAllTemplates> createState() => _TestAllTemplatesState();
}

class _TestAllTemplatesState extends State<TestAllTemplates> {
  bool _isGenerating = false;
  String? _lastResult;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('全模板PDF测试'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '全面验证测试',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '测试所有模板和任务类型：\n'
                      '• 平板车单批次任务\n'
                      '• 平板车混合任务\n'
                      '• 集装箱单批次任务\n'
                      '• 集装箱混合任务',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton.icon(
              onPressed: _isGenerating ? null : _testFlatbedSingle,
              icon: _isGenerating ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)) : const Icon(Icons.local_shipping),
              label: const Text('测试平板车单批次'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green[600], foregroundColor: Colors.white, padding: const EdgeInsets.symmetric(vertical: 12)),
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: _isGenerating ? null : _testFlatbedMixed,
              icon: _isGenerating ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)) : const Icon(Icons.layers),
              label: const Text('测试平板车混合任务'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.orange[600], foregroundColor: Colors.white, padding: const EdgeInsets.symmetric(vertical: 12)),
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: _isGenerating ? null : _testContainerSingle,
              icon: _isGenerating ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)) : const Icon(Icons.inventory_2),
              label: const Text('测试集装箱单批次'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.blue[600], foregroundColor: Colors.white, padding: const EdgeInsets.symmetric(vertical: 12)),
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: _isGenerating ? null : _testContainerMixed,
              icon: _isGenerating ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)) : const Icon(Icons.view_module),
              label: const Text('测试集装箱混合任务'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.purple[600], foregroundColor: Colors.white, padding: const EdgeInsets.symmetric(vertical: 12)),
            ),
            const SizedBox(height: 20),
            if (_lastResult != null) ...[
              Card(
                color: Colors.green[50],
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.check_circle, color: Colors.green[600]),
                          const SizedBox(width: 8),
                          Text('测试结果', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green[700])),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(_lastResult!, style: const TextStyle(fontSize: 12)),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 创建测试照片
  List<PhotoItem> _createTestPhotos(String template, {bool includePhotos = true}) {
    final configs = TemplateConfig.getPhotoConfigs(template);
    final photos = <PhotoItem>[];
    
    for (int i = 0; i < configs.length; i++) {
      final config = configs[i];
      final photo = PhotoItem(
        label: config.label,
        configId: config.id,
        isRequired: config.isRequired,
        needRecognition: config.needRecognition,
      );
      
      // 模拟一些照片已拍摄
      if (includePhotos && i < 5) {
        photo.imagePath = '/test/photo_${i + 1}.jpg';
        if (config.needRecognition) {
          if (i % 3 == 0) {
            // 识别成功
            photo.recognitionStatus = RecognitionStatus.completed;
            photo.recognitionResult = RecognitionResult(
              extractedProductCode: 'TEST${i + 1}',
              extractedBatchNumber: 'B${DateTime.now().millisecondsSinceEpoch}',
              matchesPreset: true,
              confidence: 0.9,
            );
            photo.isVerified = true;
          } else {
            // 识别失败
            photo.recognitionStatus = RecognitionStatus.failed;
            photo.recognitionFailed = true;
          }
        } else {
          photo.recognitionStatus = RecognitionStatus.completed;
        }
      }
      
      photos.add(photo);
    }
    
    return photos;
  }

  Future<void> _testFlatbedSingle() async {
    setState(() { _isGenerating = true; _lastResult = null; });

    try {
      final task = TaskModel(
        template: '平板车',
        batches: [
          BatchInfo(productCode: 'FB001', batchNumber: 'FB20250130001', plannedQuantity: 10),
        ],
        photos: _createTestPhotos('平板车'),
        participants: ['测试员1', '测试员2'],
      );

      final pdfBytes = await PdfService().generateTaskReport([task], '平板车单批次测试报告');
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/flatbed_single_test.pdf');
      await file.writeAsBytes(pdfBytes);

      setState(() {
        _lastResult = '平板车单批次任务PDF生成成功\n'
                   '文件: ${file.path}\n'
                   '大小: ${(pdfBytes.length / 1024).toStringAsFixed(1)} KB\n'
                   '模板: ${task.template}\n'
                   '批次数: ${task.batches.length}\n'
                   '照片数: ${task.photos.where((p) => p.imagePath != null).length}';
      });
    } catch (e) {
      setState(() { _lastResult = '平板车单批次测试失败: $e'; });
    } finally {
      setState(() { _isGenerating = false; });
    }
  }

  Future<void> _testFlatbedMixed() async {
    setState(() { _isGenerating = true; _lastResult = null; });

    try {
      final task = TaskModel(
        template: '平板车',
        batches: [
          BatchInfo(productCode: 'FB001', batchNumber: 'FB20250130001', plannedQuantity: 8),
          BatchInfo(productCode: 'FB002', batchNumber: 'FB20250130002', plannedQuantity: 12),
          BatchInfo(productCode: 'FB003', batchNumber: 'FB20250130003', plannedQuantity: 6),
        ],
        photos: _createTestPhotos('平板车'),
        participants: ['测试员1', '测试员2', '测试员3'],
      );

      final pdfBytes = await PdfService().generateTaskReport([task], '平板车混合任务测试报告');
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/flatbed_mixed_test.pdf');
      await file.writeAsBytes(pdfBytes);

      setState(() {
        _lastResult = '平板车混合任务PDF生成成功\n'
                   '文件: ${file.path}\n'
                   '大小: ${(pdfBytes.length / 1024).toStringAsFixed(1)} KB\n'
                   '模板: ${task.template}\n'
                   '批次数: ${task.batches.length}\n'
                   '照片数: ${task.photos.where((p) => p.imagePath != null).length}';
      });
    } catch (e) {
      setState(() { _lastResult = '平板车混合任务测试失败: $e'; });
    } finally {
      setState(() { _isGenerating = false; });
    }
  }

  Future<void> _testContainerSingle() async {
    setState(() { _isGenerating = true; _lastResult = null; });

    try {
      final task = TaskModel(
        template: '集装箱',
        batches: [
          BatchInfo(productCode: 'CT001', batchNumber: 'CT20250130001', plannedQuantity: 15),
        ],
        photos: _createTestPhotos('集装箱'),
        participants: ['测试员1'],
      );

      final pdfBytes = await PdfService().generateTaskReport([task], '集装箱单批次测试报告');
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/container_single_test.pdf');
      await file.writeAsBytes(pdfBytes);

      setState(() {
        _lastResult = '集装箱单批次任务PDF生成成功\n'
                   '文件: ${file.path}\n'
                   '大小: ${(pdfBytes.length / 1024).toStringAsFixed(1)} KB\n'
                   '模板: ${task.template}\n'
                   '批次数: ${task.batches.length}\n'
                   '照片数: ${task.photos.where((p) => p.imagePath != null).length}';
      });
    } catch (e) {
      setState(() { _lastResult = '集装箱单批次测试失败: $e'; });
    } finally {
      setState(() { _isGenerating = false; });
    }
  }

  Future<void> _testContainerMixed() async {
    setState(() { _isGenerating = true; _lastResult = null; });

    try {
      final task = TaskModel(
        template: '集装箱',
        batches: [
          BatchInfo(productCode: 'CT001', batchNumber: 'CT20250130001', plannedQuantity: 10),
          BatchInfo(productCode: 'CT002', batchNumber: 'CT20250130002', plannedQuantity: 15),
        ],
        photos: _createTestPhotos('集装箱'),
        participants: ['测试员1', '测试员2'],
      );

      final pdfBytes = await PdfService().generateTaskReport([task], '集装箱混合任务测试报告');
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/container_mixed_test.pdf');
      await file.writeAsBytes(pdfBytes);

      setState(() {
        _lastResult = '集装箱混合任务PDF生成成功\n'
                   '文件: ${file.path}\n'
                   '大小: ${(pdfBytes.length / 1024).toStringAsFixed(1)} KB\n'
                   '模板: ${task.template}\n'
                   '批次数: ${task.batches.length}\n'
                   '照片数: ${task.photos.where((p) => p.imagePath != null).length}';
      });
    } catch (e) {
      setState(() { _lastResult = '集装箱混合任务测试失败: $e'; });
    } finally {
      setState(() { _isGenerating = false; });
    }
  }
}
