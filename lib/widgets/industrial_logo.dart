import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/theme_colors.dart';

/// 🏭 现代化工业物流Logo组件
/// 设计理念：专业、科技、高端、现代
class IndustrialLogo extends StatelessWidget {
  final double size;
  final Color? primaryColor;
  final Color? accentColor;
  final bool showText;
  final bool showTagline;
  final double? fontSize;

  const IndustrialLogo({
    Key? key,
    this.size = 80,
    this.primaryColor,
    this.accentColor,
    this.showText = true,
    this.showTagline = false,
    this.fontSize,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final primary = primaryColor ?? ThemeColors.primary;
    final accent = accentColor ?? ThemeColors.primaryHover;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Logo图形部分
        Container(
          width: size,
          height: size,
          child: CustomPaint(
            painter: IndustrialLogoPainter(
              primaryColor: primary,
              accentColor: accent,
            ),
          ),
        ),

        if (showText) ...[
          SizedBox(height: size * 0.15),

          // 应用名称
          Text(
            '装运卫士',
            style: TextStyle(
              fontSize: fontSize ?? size * 0.25,
              fontWeight: FontWeight.w800,
              color: primary,
              letterSpacing: 1.0,
            ),
          ),

          if (showTagline) ...[
            SizedBox(height: size * 0.08),

            // 专业标语
            Text(
              '工业级物流标签智能识别系统',
              style: TextStyle(
                fontSize: fontSize != null ? fontSize! * 0.5 : size * 0.12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
                letterSpacing: 0.5,
              ),
            ),
          ],
        ],
      ],
    );
  }
}

/// 🎨 工业Logo绘制器
/// 设计元素：货物箱、扫描框、AI符号、科技感
class IndustrialLogoPainter extends CustomPainter {
  final Color primaryColor;
  final Color accentColor;

  IndustrialLogoPainter({
    required this.primaryColor,
    required this.accentColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = size.center(Offset.zero);
    final radius = size.width / 2;

    // 1. 外圆环 - 科技感背景
    _drawTechRing(canvas, center, radius);

    // 2. 货物箱 - 主体标识（已优化标签表现）
    _drawCargoBox(canvas, center, radius * 0.6);
    // 2.1 物流轨迹线/发货箭头
    _drawLogisticsArrow(canvas, center, radius * 0.6);
    // 3. 扫描框 - 识别功能
    _drawScanFrame(canvas, center, radius * 0.8);
    // 4. AI标识 - 智能化
    _drawAISymbol(canvas, center, radius * 0.3);
    // 5. 动态元素 - 活力感
    _drawDynamicElements(canvas, center, radius);
  }

  /// 🔵 绘制科技感外圆环
  void _drawTechRing(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..shader = RadialGradient(
        colors: [
          primaryColor.withValues(alpha: 0.1),
          primaryColor.withValues(alpha: 0.3),
          primaryColor.withValues(alpha: 0.1),
        ],
        stops: [0.0, 0.7, 1.0],
      ).createShader(Rect.fromCircle(center: center, radius: radius));

    canvas.drawCircle(center, radius, paint);

    // 外环边框
    final borderPaint = Paint()
      ..color = primaryColor.withValues(alpha: 0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    canvas.drawCircle(center, radius * 0.95, borderPaint);
  }

  /// 📦 绘制货物箱
  void _drawCargoBox(Canvas canvas, Offset center, double size) {
    final rect = Rect.fromCenter(
      center: center,
      width: size,
      height: size * 0.8,
    );
    // 主箱体
    final boxPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          primaryColor,
          primaryColor.withBlue(255),
        ],
        stops: [0.0, 1.0],
      ).createShader(rect);
    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(size * 0.1));
    canvas.drawRRect(rrect, boxPaint);
    // 箱体高光
    final highlightPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Colors.white.withValues(alpha: 0.35), Colors.transparent],
        stops: [0.0, 1.0],
      ).createShader(rect)
      ..style = PaintingStyle.fill;
    canvas.drawRRect(rrect, highlightPaint);
    // 货物标签（更大更显眼，玻璃态）
    final labelRect = Rect.fromCenter(
      center: Offset(center.dx, center.dy - size * 0.08),
      width: size * 0.7,
      height: size * 0.32,
    );
    final labelPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Colors.white.withValues(alpha: 0.95), accentColor.withValues(alpha: 0.18)],
        stops: [0.0, 1.0],
      ).createShader(labelRect);
    canvas.drawRRect(
      RRect.fromRectAndRadius(labelRect, Radius.circular(size * 0.08)),
      labelPaint,
    );
    // 标签边框
    final borderPaint = Paint()
      ..color = accentColor.withValues(alpha: 0.7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    canvas.drawRRect(
      RRect.fromRectAndRadius(labelRect, Radius.circular(size * 0.08)),
      borderPaint,
    );
    // 标签上加条码符号
    final barcodePaint = Paint()
      ..color = accentColor.withValues(alpha: 0.7)
      ..strokeWidth = 1.1;
    final barCount = 7;
    for (int i = 0; i < barCount; i++) {
      final x = labelRect.left + (labelRect.width / (barCount + 1)) * (i + 1);
      final top = labelRect.top + labelRect.height * 0.22;
      final bottom = labelRect.bottom - labelRect.height * 0.22;
      canvas.drawLine(Offset(x, top), Offset(x, bottom), barcodePaint);
    }
  }

  /// 🔍 绘制扫描框
  void _drawScanFrame(Canvas canvas, Offset center, double size) {
    final paint = Paint()
      ..color = accentColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;

    final cornerSize = size * 0.15;
    final frameSize = size * 0.5;

    // 四个角的扫描框
    final corners = [
      // 左上角
      Offset(center.dx - frameSize, center.dy - frameSize),
      // 右上角
      Offset(center.dx + frameSize, center.dy - frameSize),
      // 右下角
      Offset(center.dx + frameSize, center.dy + frameSize),
      // 左下角
      Offset(center.dx - frameSize, center.dy + frameSize),
    ];

    for (int i = 0; i < corners.length; i++) {
      final corner = corners[i];
      final isLeft = i == 0 || i == 3;
      final isTop = i == 0 || i == 1;

      // 水平线
      canvas.drawLine(
        corner,
        corner + Offset(isLeft ? cornerSize : -cornerSize, 0),
        paint,
      );

      // 垂直线
      canvas.drawLine(
        corner,
        corner + Offset(0, isTop ? cornerSize : -cornerSize),
        paint,
      );
    }

    // 扫描线动画效果（静态版本）
    final scanLinePaint = Paint()
      ..color = accentColor.withValues(alpha: 0.6)
      ..strokeWidth = 2.0;

    canvas.drawLine(
      Offset(center.dx - frameSize * 0.8, center.dy),
      Offset(center.dx + frameSize * 0.8, center.dy),
      scanLinePaint,
    );
  }

  /// 🤖 绘制AI标识
  void _drawAISymbol(Canvas canvas, Offset center, double size) {
    final paint = Paint()
      ..color = accentColor
      ..style = PaintingStyle.fill;

    // AI芯片形状
    final chipRect = Rect.fromCenter(
      center: Offset(center.dx + size * 0.6, center.dy - size * 0.6),
      width: size * 0.4,
      height: size * 0.4,
    );

    canvas.drawRRect(
      RRect.fromRectAndRadius(chipRect, Radius.circular(size * 0.05)),
      paint,
    );

    // AI点阵
    final dotPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final dotSize = size * 0.03;
    for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 3; j++) {
        if ((i + j) % 2 == 0) {
          final dotCenter = Offset(
            chipRect.left + (i + 1) * chipRect.width / 4,
            chipRect.top + (j + 1) * chipRect.height / 4,
          );
          canvas.drawCircle(dotCenter, dotSize, dotPaint);
        }
      }
    }
  }

  /// ⚡ 绘制动态元素
  void _drawDynamicElements(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = accentColor.withValues(alpha: 0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // 数据流线条
    for (int i = 0; i < 6; i++) {
      final angle = (i * 60) * (3.14159 / 180);
      final startRadius = radius * 0.7;
      final endRadius = radius * 0.85;

      final start = Offset(
        center.dx + startRadius * math.cos(angle),
        center.dy + startRadius * math.sin(angle),
      );

      final end = Offset(
        center.dx + endRadius * math.cos(angle),
        center.dy + endRadius * math.sin(angle),
      );

      canvas.drawLine(start, end, paint);
    }
  }

  /// 新增：物流轨迹线/发货箭头
  void _drawLogisticsArrow(Canvas canvas, Offset center, double boxSize) {
    final paint = Paint()
      ..color = accentColor.withValues(alpha: 0.85)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.2
      ..strokeCap = StrokeCap.round;
    // 箱体右侧画一条带箭头的物流线
    final start = Offset(center.dx + boxSize * 0.45, center.dy);
    final end = Offset(center.dx + boxSize * 0.85, center.dy - boxSize * 0.18);
    canvas.drawLine(start, end, paint);
    // 箭头
    final arrowLen = boxSize * 0.12;
    final angle = math.pi / 7;
    final arrowP1 = Offset(
      end.dx - arrowLen * math.cos(angle),
      end.dy + arrowLen * math.sin(angle),
    );
    final arrowP2 = Offset(
      end.dx - arrowLen * math.cos(-angle),
      end.dy + arrowLen * math.sin(-angle),
    );
    canvas.drawLine(end, arrowP1, paint);
    canvas.drawLine(end, arrowP2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// 📱 带动画的Logo组件
class AnimatedIndustrialLogo extends StatefulWidget {
  final double size;
  final Color? primaryColor;
  final Color? accentColor;
  final bool showText;
  final bool showTagline;

  const AnimatedIndustrialLogo({
    Key? key,
    this.size = 80,
    this.primaryColor,
    this.accentColor,
    this.showText = true,
    this.showTagline = false,
  }) : super(key: key);

  @override
  State<AnimatedIndustrialLogo> createState() => _AnimatedIndustrialLogoState();
}

class _AnimatedIndustrialLogoState extends State<AnimatedIndustrialLogo>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    )..repeat();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_rotationController, _pulseController]),
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 + (_pulseController.value * 0.05),
          child: Transform.rotate(
            angle: _rotationController.value * 2 * 3.14159,
            child: IndustrialLogo(
              size: widget.size,
              primaryColor: widget.primaryColor,
              accentColor: widget.accentColor,
              showText: widget.showText,
              showTagline: widget.showTagline,
            ),
          ),
        );
      },
    );
  }
}
