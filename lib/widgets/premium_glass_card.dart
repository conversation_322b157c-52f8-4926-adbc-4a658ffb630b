import 'package:flutter/material.dart';
import 'dart:ui';
import '../utils/theme_colors.dart';

/// 玻璃态卡片类型
enum GlassCardType {
  /// 基础玻璃态
  basic,

  /// 企业级玻璃态,带有微磨砂金属边框
  enterprise,

  /// 平板车模块,户外光影噪点
  flatbed,

  /// 集装箱模块,弱光渐变晕影
  container
}

/// 高级玻璃态卡片组件
/// 实现多层叠透效果:玻璃本体+反光膜+环境融合
class PremiumGlassCard extends StatefulWidget {
  final Widget child;
  final GlassCardType type;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final double? height;
  final double? width;
  final VoidCallback? onTap;
  final bool applyInnerShadow;

  const PremiumGlassCard({
    Key? key,
    required this.child,
    this.type = GlassCardType.basic,
    this.padding,
    this.margin,
    this.borderRadius,
    this.height,
    this.width,
    this.onTap,
    this.applyInnerShadow = true,
  }) : super(key: key);

  @override
  State<PremiumGlassCard> createState() => _PremiumGlassCardState();
}

class _PremiumGlassCardState extends State<PremiumGlassCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _blurAnimation;

  @override
  void initState() {
    super.initState();
    // 初始化微交互动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 点击时短暂清晰化的模糊动画
    _blurAnimation = Tween<double>(
      begin: 10.0,
      end: 5.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
      reverseCurve: Curves.easeIn,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // 获取不同卡片类型的背景颜色
  Color _getBackgroundColor() {
    switch (widget.type) {
      case GlassCardType.basic:
        return ThemeColors.glassBackground;
      case GlassCardType.enterprise:
        return const Color(0x20FFFFFF);
      case GlassCardType.flatbed:
        return const Color(0x18FFFFFF);
      case GlassCardType.container:
        return const Color(0x15FFFFFF);
    }
  }

  // 获取不同卡片类型的边框颜色
  Color _getBorderColor() {
    switch (widget.type) {
      case GlassCardType.basic:
        return Colors.white30;
      case GlassCardType.enterprise:
        return const Color(0x40CBD5E0);
      case GlassCardType.flatbed:
        return const Color(0x30FFFFFF);
      case GlassCardType.container:
        return const Color(0x25FFFFFF);
    }
  }

  @override
  Widget build(BuildContext context) {
    final borderRadius =
        widget.borderRadius ?? BorderRadius.circular(ThemeColors.radiusMedium);

    // 添加内阴影效果
    final List<BoxShadow>? innerShadow = widget.applyInnerShadow
        ? [
            const BoxShadow(
              color: Color(0x20000000),
              blurRadius: 8,
              offset: Offset(2, 2),
              spreadRadius: -2,
            )
          ]
        : null;

    return Padding(
      padding: widget.margin ?? const EdgeInsets.all(8),
      child: GestureDetector(
        onTapDown: (_) => _animationController.forward(),
        onTapUp: (_) => _animationController.reverse(),
        onTapCancel: () => _animationController.reverse(),
        onTap: widget.onTap,
        child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Container(
                width: widget.width,
                height: widget.height,
                decoration: BoxDecoration(
                  borderRadius: borderRadius,
                  border: Border.all(
                    color: _getBorderColor(),
                    width: 1.0,
                  ),
                  boxShadow: innerShadow,
                ),
                child: ClipRRect(
                  borderRadius: borderRadius,
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: _blurAnimation.value,
                      sigmaY: _blurAnimation.value,
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        color: _getBackgroundColor(),
                        borderRadius: borderRadius,
                      ),
                      padding: widget.padding ?? const EdgeInsets.all(16),
                      child: widget.child,
                    ),
                  ),
                ),
              );
            }),
      ),
    );
  }
}
