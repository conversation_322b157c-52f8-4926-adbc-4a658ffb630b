import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:loadguard/utils/theme_colors.dart';
import 'dart:math' as math;

/// 🎯 工作量统计图表组件集合
/// 包含雷达图、饼图、柱状图等专业图表

/// 雷达图组件
class WorkloadRadarChart extends StatelessWidget {
  final Map<String, double> data;
  final Color primaryColor;
  final double size;

  const WorkloadRadarChart({
    Key? key,
    required this.data,
    this.primaryColor = ThemeColors.primary,
    this.size = 280, // 增大默认尺寸
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return _buildEmptyState();
    }

    return Center(
      child: SizedBox(
        width: size,
        height: size,
        child: RadarChart(
          RadarChartData(
            radarTouchData: RadarTouchData(enabled: false),
            dataSets: [
              RadarDataSet(
                fillColor: primaryColor.withValues(alpha: 0.3), // 增加透明度
                borderColor: primaryColor,
                entryRadius: 4, // 增大节点半径
                dataEntries: data.entries.map((entry) {
                  return RadarEntry(value: entry.value);
                }).toList(),
                borderWidth: 3, // 增加边框宽度
              ),
            ],
            radarBackgroundColor: Colors.transparent,
            borderData: FlBorderData(show: false),
            radarBorderData: BorderSide(
                color: ThemeColors.textOnDark.withValues(alpha: 0.3), width: 2),
            titlePositionPercentageOffset: 0.15, // 调整标题位置
            titleTextStyle: TextStyle(
              color: ThemeColors.textOnDark,
              fontSize: 14, // 增大字体
              fontWeight: FontWeight.bold,
            ),
            getTitle: (index, angle) {
              final keys = data.keys.toList();
              if (index < keys.length) {
                return RadarChartTitle(text: keys[index]);
              }
              return const RadarChartTitle(text: '');
            },
            tickCount: 5,
            ticksTextStyle: TextStyle(
              color: ThemeColors.textOnDark.withValues(alpha: 0.7), // 增强可读性
              fontSize: 11, // 增大字体
            ),
            tickBorderData: BorderSide(
                color: ThemeColors.textOnDark.withValues(alpha: 0.2), width: 1),
            gridBorderData: BorderSide(
                color: ThemeColors.textOnDark.withValues(alpha: 0.3), width: 1),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return SizedBox(
      width: size,
      height: size,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.radar,
              color: ThemeColors.textOnDark.withValues(alpha: 0.54),
              size: 48, // 增大图标
            ),
            const SizedBox(height: 12),
            Text(
              '暂无数据',
              style: TextStyle(
                color: ThemeColors.textOnDark.withValues(alpha: 0.54),
                fontSize: 16, // 增大字体
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 饼图组件
class WorkloadPieChart extends StatelessWidget {
  final Map<String, double> data;
  final double size;

  const WorkloadPieChart({
    Key? key,
    required this.data,
    this.size = 280, // 增大默认尺寸
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty || data.values.every((v) => v == 0)) {
      return _buildEmptyState();
    }

    final colors = [
      ThemeColors.primary,
      ThemeColors.success,
      ThemeColors.warning,
      ThemeColors.accent,
      ThemeColors.error,
      ThemeColors.secondary,
      ThemeColors.yellow,
      ThemeColors.info,
    ];

    // 计算总值用于百分比显示
    final totalValue = data.values.fold(0.0, (sum, value) => sum + value);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 饼图
          SizedBox(
            width: size,
            height: size * 0.8, // 为图例留出空间
            child: PieChart(
              PieChartData(
                sectionsSpace: 3, // 增加扇形间距
                centerSpaceRadius: size * 0.15, // 调整中心空间
                sections: data.entries.toList().asMap().entries.map((entry) {
                  final index = entry.key;
                  final dataEntry = entry.value;
                  final color = colors[index % colors.length];
                  final percentage = (dataEntry.value / totalValue * 100);

                  return PieChartSectionData(
                    color: color,
                    value: dataEntry.value,
                    title: percentage > 5
                        ? '${percentage.toStringAsFixed(1)}%'
                        : '', // 只显示大于5%的标签
                    radius: size * 0.25, // 增大半径
                    titleStyle: TextStyle(
                      fontSize: 12, // 增大字体
                      fontWeight: FontWeight.bold,
                      color: ThemeColors.textOnDark,
                    ),
                    titlePositionPercentageOffset: 0.6, // 调整标题位置
                  );
                }).toList(),
              ),
            ),
          ),
          // 图例
          const SizedBox(height: 16),
          Wrap(
            spacing: 16,
            runSpacing: 8,
            alignment: WrapAlignment.center,
            children: data.entries.toList().asMap().entries.map((entry) {
              final index = entry.key;
              final dataEntry = entry.value;
              final color = colors[index % colors.length];
              final percentage = (dataEntry.value / totalValue * 100);

              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    '${dataEntry.key} (${percentage.toStringAsFixed(1)}%)',
                    style: TextStyle(
                      color: ThemeColors.textOnDark,
                      fontSize: 12,
                    ),
                  ),
                ],
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return SizedBox(
      width: size,
      height: size,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.pie_chart,
              color: ThemeColors.textOnDark.withValues(alpha: 0.54),
              size: 48, // 增大图标
            ),
            const SizedBox(height: 12),
            Text(
              '暂无数据',
              style: TextStyle(
                color: ThemeColors.textOnDark.withValues(alpha: 0.54),
                fontSize: 16, // 增大字体
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 柱状图组件
class WorkloadBarChart extends StatelessWidget {
  final Map<String, double> data;
  final double height;
  final String title;
  final bool showLabels;

  const WorkloadBarChart({
    Key? key,
    required this.data,
    this.height = 200,
    this.title = '',
    this.showLabels = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return _buildEmptyState();
    }

    return SizedBox(
      height: height,
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY:
              data.values.isNotEmpty ? data.values.reduce(math.max) * 1.2 : 100,
          barTouchData: BarTouchData(enabled: false),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final keys = data.keys.toList();
                  if (value.toInt() < keys.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        keys[value.toInt()],
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 10,
                        ),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toInt().toString(),
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 10,
                    ),
                  );
                },
              ),
            ),
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false),
          barGroups: data.entries.toList().asMap().entries.map((entry) {
            final index = entry.key;
            final mapEntry = entry.value;
            final value = mapEntry.value;

            return BarChartGroupData(
              x: index,
              barRods: [
                BarChartRodData(
                  toY: value,
                  color: Colors.blue,
                  width: 16,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return SizedBox(
      height: height,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart,
              color: Colors.white54,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              '暂无数据',
              style: TextStyle(
                color: Colors.white54,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 折线图组件
class WorkloadLineChart extends StatelessWidget {
  final List<Map<String, dynamic>> data;
  final double height;
  final String title;

  const WorkloadLineChart({
    Key? key,
    required this.data,
    this.height = 200,
    this.title = '',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return _buildEmptyState();
    }

    return SizedBox(
      height: height,
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            horizontalInterval: 1,
            verticalInterval: 1,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.white.withValues(alpha: 0.1),
                strokeWidth: 1,
              );
            },
            getDrawingVerticalLine: (value) {
              return FlLine(
                color: Colors.white.withValues(alpha: 0.1),
                strokeWidth: 1,
              );
            },
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                interval: 1,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < data.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        data[index]['label'] ?? '',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 10,
                        ),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                interval: 1,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toInt().toString(),
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 10,
                    ),
                  );
                },
                reservedSize: 42,
              ),
            ),
          ),
          borderData: FlBorderData(show: false),
          minX: 0,
          maxX: data.length.toDouble() - 1,
          minY: 0,
          maxY: data.isNotEmpty
              ? data.map((d) => d['value'] as double).reduce(math.max) * 1.2
              : 100,
          lineBarsData: [
            LineChartBarData(
              spots: data.asMap().entries.map((entry) {
                return FlSpot(
                  entry.key.toDouble(),
                  entry.value['value'] as double,
                );
              }).toList(),
              isCurved: true,
              color: Colors.blue,
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: 4,
                    color: Colors.white,
                    strokeWidth: 2,
                    strokeColor: Colors.blue,
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: true,
                color: Colors.blue.withValues(alpha: 0.1),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return SizedBox(
      height: height,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.show_chart,
              color: Colors.white54,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              '暂无数据',
              style: TextStyle(
                color: Colors.white54,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 🎯 新增：识别效率仪表盘
class RecognitionEfficiencyGauge extends StatelessWidget {
  final double value; // 0-100
  final String label;
  final Color color;
  final double size;

  const RecognitionEfficiencyGauge({
    Key? key,
    required this.value,
    required this.label,
    this.color = Colors.blue,
    this.size = 120,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 背景圆环
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              value: 1.0,
              strokeWidth: 8,
              backgroundColor: Colors.white.withValues(alpha: 0.1),
              valueColor:
                  AlwaysStoppedAnimation<Color>(Colors.white.withValues(alpha: 0.1)),
            ),
          ),
          // 进度圆环
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              value: value / 100,
              strokeWidth: 8,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(color),
            ),
          ),
          // 中心文本
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${value.toStringAsFixed(1)}%',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// 🎯 新增：热力图组件
class WorkloadHeatmap extends StatelessWidget {
  final Map<String, List<double>>
      data; // 例如：{'8': [1.2, 2.3, 1.8], '9': [2.1, 1.9, 2.5]}
  final double cellSize;
  final String title;

  const WorkloadHeatmap({
    Key? key,
    required this.data,
    this.cellSize = 20,
    this.title = '',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return _buildEmptyState();
    }

    final maxValue = data.values
        .expand((values) => values)
        .fold(0.0, (max, value) => math.max(max, value));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title.isNotEmpty) ...[
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
        ],
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: data.entries.map((entry) {
              final hour = entry.key;
              final values = entry.value;

              return Row(
                children: [
                  // 时间标签
                  SizedBox(
                    width: 30,
                    child: Text(
                      '${hour}h',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 10,
                      ),
                    ),
                  ),
                  // 热力图单元格
                  ...values.asMap().entries.map((valueEntry) {
                    final value = valueEntry.value;
                    final intensity = maxValue > 0 ? value / maxValue : 0.0;

                    return Container(
                      width: cellSize,
                      height: cellSize,
                      margin: const EdgeInsets.all(1),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: intensity * 0.8 + 0.1),
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: Center(
                        child: Text(
                          value.toStringAsFixed(1),
                          style: TextStyle(
                            color:
                                intensity > 0.5 ? Colors.white : Colors.white70,
                            fontSize: 8,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.grid_on,
            color: Colors.white54,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            '暂无热力图数据',
            style: TextStyle(
              color: Colors.white54,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}

/// 🎯 新增：桑基图组件（简化版）
class WorkloadSankeyChart extends StatelessWidget {
  final Map<String, Map<String, int>>
      flowData; // 例如：{'仓库A': {'仓库B': 10, '仓库C': 5}}
  final double height;

  const WorkloadSankeyChart({
    Key? key,
    required this.flowData,
    this.height = 200,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (flowData.isEmpty) {
      return _buildEmptyState();
    }

    return SizedBox(
      height: height,
      child: CustomPaint(
        painter: SankeyPainter(flowData),
        child: Container(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return SizedBox(
      height: height,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_tree,
              color: Colors.white54,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              '暂无流转数据',
              style: TextStyle(
                color: Colors.white54,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 桑基图绘制器
class SankeyPainter extends CustomPainter {
  final Map<String, Map<String, int>> flowData;

  SankeyPainter(this.flowData);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.withValues(alpha: 0.6)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // 简化的桑基图绘制逻辑
    final nodes = <String>{};
    flowData.forEach((from, toMap) {
      nodes.add(from);
      nodes.addAll(toMap.keys);
    });

    final nodeList = nodes.toList();
    final nodeHeight = size.height / nodeList.length;

    // 绘制节点
    for (int i = 0; i < nodeList.length; i++) {
      final y = i * nodeHeight + nodeHeight / 2;

      // 左侧节点
      canvas.drawCircle(
        Offset(50, y),
        8,
        Paint()..color = Colors.blue,
      );

      // 右侧节点
      canvas.drawCircle(
        Offset(size.width - 50, y),
        8,
        Paint()..color = Colors.green,
      );

      // 连接线（简化）
      canvas.drawLine(
        const Offset(50, 0) + Offset(0, y),
        Offset(size.width - 50, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
