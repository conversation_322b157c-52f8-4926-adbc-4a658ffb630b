import 'package:flutter/material.dart';
import 'package:loadguard/services/async_upload_service.dart';
import 'package:loadguard/utils/theme_colors.dart';

/// 📊 上传进度显示组件
class UploadProgressWidget extends StatefulWidget {
  final AsyncUploadService uploadService;

  const UploadProgressWidget({
    Key? key,
    required this.uploadService,
  }) : super(key: key);

  @override
  State<UploadProgressWidget> createState() => _UploadProgressWidgetState();
}

class _UploadProgressWidgetState extends State<UploadProgressWidget> {
  late Stream<UploadEvent> _eventStream;

  @override
  void initState() {
    super.initState();
    _eventStream = widget.uploadService.eventStream;
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<UploadEvent>(
      stream: _eventStream,
      builder: (context, snapshot) {
        final activeUploads = widget.uploadService.getActiveUploads();
        if (activeUploads.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: ThemeColors.textOnDark,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: ThemeColors.textMedium.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Icon(Icons.cloud_upload, color: ThemeColors.accent),
                  const SizedBox(width: 8),
                  Text(
                    '上传进度 (${activeUploads.length})',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              ...activeUploads.map((upload) => _buildUploadItem(upload)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildUploadItem(UploadProgress upload) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _getStatusIcon(upload.status),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  upload.message,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              Text(
                '${(upload.progress * 100).toInt()}%',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: upload.progress,
            backgroundColor: ThemeColors.border,
            valueColor: AlwaysStoppedAnimation<Color>(
              _getStatusColor(upload.status),
            ),
          ),
        ],
      ),
    );
  }

  Widget _getStatusIcon(UploadStatus status) {
    switch (status) {
      case UploadStatus.queued:
        return Icon(Icons.schedule, size: 16, color: ThemeColors.warning);
      case UploadStatus.uploading:
        return const SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(strokeWidth: 2),
        );
      case UploadStatus.recognizing:
        return Icon(Icons.psychology, size: 16, color: ThemeColors.accent);
      case UploadStatus.completed:
        return Icon(Icons.check_circle, size: 16, color: ThemeColors.success);
      case UploadStatus.failed:
        return Icon(Icons.error, size: 16, color: ThemeColors.error);
      default:
        return Icon(Icons.info, size: 16, color: ThemeColors.textMedium);
    }
  }

  Color _getStatusColor(UploadStatus status) {
    switch (status) {
      case UploadStatus.queued:
        return ThemeColors.warning;
      case UploadStatus.uploading:
        return ThemeColors.accent;
      case UploadStatus.recognizing:
        return ThemeColors.accent;
      case UploadStatus.completed:
        return ThemeColors.success;
      case UploadStatus.failed:
        return ThemeColors.error;
      default:
        return ThemeColors.textMedium;
    }
  }
}
