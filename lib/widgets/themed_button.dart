import 'package:flutter/material.dart';
import '../utils/theme_colors.dart';

enum ButtonType { primary, success, warning, danger, secondary }

enum ButtonStyle3 { elevated, filled, outlined, text } // Material 3按钮样式

class ThemedButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final bool isOutlined;
  final bool isSmall;
  final IconData? icon;
  final bool loading;
  final LinearGradient? gradient;
  final ButtonStyle3? material3Style; // 新增：Material 3样式选项
  final bool useMaterial3; // 新增：是否使用Material 3原生组件

  const ThemedButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.isOutlined = false,
    this.isSmall = false,
    this.icon,
    this.loading = false,
    this.gradient,
    this.material3Style, // 新增参数
    this.useMaterial3 = true, // 默认使用Material 3
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 🆕 Material 3原生组件支持
    if (useMaterial3) {
      return _buildMaterial3Button(context);
    }

    // 🔄 保持原有实现以确保兼容性
    final effectiveGradient = gradient ?? _getGradient();
    final color = _getColor();
    final height = isSmall ? 36.0 : 48.0;
    final fontSize = isSmall ? 14.0 : 16.0;
    final iconSize = isSmall ? 18.0 : 20.0;

    return Container(
      height: height,
      decoration: BoxDecoration(
        gradient: isOutlined ? null : effectiveGradient,
        border: isOutlined ? Border.all(color: color, width: 2) : null,
        borderRadius: BorderRadius.circular(ThemeColors.buttonRadius),
        boxShadow: isOutlined ? null : ThemeColors.buttonShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: loading ? null : onPressed,
          borderRadius: BorderRadius.circular(ThemeColors.buttonRadius),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: isSmall ? 16 : 24,
              vertical: isSmall ? 8 : 12,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (loading)
                  SizedBox(
                    width: iconSize,
                    height: iconSize,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isOutlined ? color : ThemeColors.textOnGradient,
                      ),
                    ),
                  )
                else if (icon != null) ...[
                  Icon(
                    icon,
                    size: iconSize,
                    color: isOutlined ? color : ThemeColors.textOnGradient,
                  ),
                  const SizedBox(width: 8),
                ],
                if (!loading)
                  Flexible(
                    child: Text(
                      text,
                      style: TextStyle(
                        fontSize: fontSize,
                        fontWeight: FontWeight.w600,
                        color: isOutlined ? color : ThemeColors.textOnGradient,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  LinearGradient _getGradient() {
    switch (type) {
      case ButtonType.primary:
        return ThemeColors.primaryButtonGradient;
      case ButtonType.success:
        return ThemeColors.successButtonGradient;
      case ButtonType.warning:
        return ThemeColors.warningButtonGradient;
      case ButtonType.danger:
        return ThemeColors.dangerButtonGradient;
      case ButtonType.secondary:
        return const LinearGradient(
          colors: [ThemeColors.secondary, ThemeColors.secondary],
          stops: [0.0, 1.0],
        );
    }
  }

  Color _getColor() {
    switch (type) {
      case ButtonType.primary:
        return ThemeColors.primary;
      case ButtonType.success:
        return ThemeColors.success;
      case ButtonType.warning:
        return ThemeColors.warning;
      case ButtonType.danger:
        return ThemeColors.accent;
      case ButtonType.secondary:
        return ThemeColors.secondary;
    }
  }

  /// 🆕 构建Material 3原生按钮
  Widget _buildMaterial3Button(BuildContext context) {
    final effectiveStyle = material3Style ??
        (isOutlined ? ButtonStyle3.outlined : ButtonStyle3.filled);
    final buttonText = loading ? '正在加载...' : text;

    // 构建按钮内容
    Widget buttonChild;
    if (loading) {
      buttonChild = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.onPrimary,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(buttonText),
        ],
      );
    } else if (icon != null) {
      buttonChild = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: isSmall ? 18 : 20),
          const SizedBox(width: 8),
          Text(buttonText),
        ],
      );
    } else {
      buttonChild = Text(buttonText);
    }

    // 根据样式返回对应的Material 3按钮
    switch (effectiveStyle) {
      case ButtonStyle3.elevated:
        return ElevatedButton(
          onPressed: loading ? null : onPressed,
          style: _getMaterial3ButtonStyle(context, effectiveStyle),
          child: buttonChild,
        );
      case ButtonStyle3.filled:
        return FilledButton(
          onPressed: loading ? null : onPressed,
          style: _getMaterial3ButtonStyle(context, effectiveStyle),
          child: buttonChild,
        );
      case ButtonStyle3.outlined:
        return OutlinedButton(
          onPressed: loading ? null : onPressed,
          style: _getMaterial3ButtonStyle(context, effectiveStyle),
          child: buttonChild,
        );
      case ButtonStyle3.text:
        return TextButton(
          onPressed: loading ? null : onPressed,
          style: _getMaterial3ButtonStyle(context, effectiveStyle),
          child: buttonChild,
        );
    }
  }

  /// 🎨 获取Material 3按钮样式
  ButtonStyle _getMaterial3ButtonStyle(
      BuildContext context, ButtonStyle3 style) {
    // 根据按钮类型获取颜色
    Color backgroundColor;
    Color foregroundColor;

    switch (type) {
      case ButtonType.primary:
        backgroundColor = ThemeColors.primary;
        foregroundColor = ThemeColors.textHighContrast;
        break;
      case ButtonType.success:
        backgroundColor = ThemeColors.success;
        foregroundColor = ThemeColors.textHighContrast;
        break;
      case ButtonType.warning:
        backgroundColor = ThemeColors.warning;
        foregroundColor = ThemeColors.textHighContrast;
        break;
      case ButtonType.danger:
        backgroundColor = ThemeColors.danger;
        foregroundColor = ThemeColors.textHighContrast;
        break;
      case ButtonType.secondary:
        backgroundColor = ThemeColors.secondary;
        foregroundColor = ThemeColors.textHighContrast;
        break;
    }

    return ButtonStyle(
      minimumSize: WidgetStateProperty.all(
        Size(isSmall ? 64 : 88, isSmall ? 40 : 48),
      ),
      padding: WidgetStateProperty.all(
        EdgeInsets.symmetric(
          horizontal: isSmall ? 16 : 24,
          vertical: isSmall ? 8 : 16,
        ),
      ),
      backgroundColor:
          style == ButtonStyle3.outlined || style == ButtonStyle3.text
              ? null
              : WidgetStateProperty.all(backgroundColor),
      foregroundColor: WidgetStateProperty.all(
        style == ButtonStyle3.outlined ? backgroundColor : foregroundColor,
      ),
      side: style == ButtonStyle3.outlined
          ? WidgetStateProperty.all(
              BorderSide(color: backgroundColor, width: 1.5))
          : null,
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
        ),
      ),
      textStyle: WidgetStateProperty.all(
        TextStyle(
          fontSize: isSmall ? 14 : 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
