import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 📸 拍照引导动画组件
/// 提供视觉引导用户正确拍摄照片
class PhotoGuideAnimation extends StatefulWidget {
  final bool isVisible;
  final String guideText;
  final IconData? guideIcon;
  final Color? primaryColor;
  final Duration animationDuration;
  final VoidCallback? onAnimationComplete;

  const PhotoGuideAnimation({
    Key? key,
    this.isVisible = true,
    this.guideText = '请将产品放在框内',
    this.guideIcon = Icons.camera_alt,
    this.primaryColor,
    this.animationDuration = const Duration(milliseconds: 1500),
    this.onAnimationComplete,
  }) : super(key: key);

  @override
  State<PhotoGuideAnimation> createState() => _PhotoGuideAnimationState();
}

class _PhotoGuideAnimationState extends State<PhotoGuideAnimation>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late AnimationController _rotateController;

  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _rotateAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _rotateController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _setupAnimations();

    if (widget.isVisible) {
      _startAnimations();
    }
  }

  void _setupAnimations() {
    // 脉冲动画
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // 滑入动画
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    // 旋转动画
    _rotateAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotateController,
      curve: Curves.linear,
    ));

    // 透明度动画
    _opacityAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimations() {
    _slideController.forward();
    _pulseController.repeat(reverse: true);
    _rotateController.repeat();

    // 延迟调用完成回调
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        widget.onAnimationComplete?.call();
      }
    });
  }

  void _stopAnimations() {
    _pulseController.stop();
    _slideController.reverse();
    _rotateController.stop();
  }

  @override
  void didUpdateWidget(PhotoGuideAnimation oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _startAnimations();
      } else {
        _stopAnimations();
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    _rotateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    final primaryColor = widget.primaryColor ?? Theme.of(context).primaryColor;

    return SlideTransition(
      position: _slideAnimation,
      child: AnimatedBuilder(
        animation: Listenable.merge([_pulseAnimation, _opacityAnimation]),
        builder: (context, child) {
          return Opacity(
            opacity: _opacityAnimation.value,
            child: Transform.scale(
              scale: _pulseAnimation.value,
              child: child,
            ),
          );
        },
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: primaryColor.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildGuideIcon(primaryColor),
              const SizedBox(height: 16),
              _buildGuideText(),
              const SizedBox(height: 16),
              _buildFocusFrame(primaryColor),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGuideIcon(Color primaryColor) {
    return AnimatedBuilder(
      animation: _rotateAnimation,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotateAnimation.value,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: primaryColor.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              widget.guideIcon,
              size: 32,
              color: primaryColor,
            ),
          ),
        );
      },
    );
  }

  Widget _buildGuideText() {
    return Text(
      widget.guideText,
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: Theme.of(context).textTheme.bodyLarge?.color,
      ),
    );
  }

  Widget _buildFocusFrame(Color primaryColor) {
    return Container(
      width: 200,
      height: 150,
      decoration: BoxDecoration(
        border: Border.all(
          color: primaryColor,
          width: 3,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        children: [
          // 四个角的装饰
          ..._buildCornerDecorations(primaryColor),

          // 中心十字准线
          Center(
            child: AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Opacity(
                  opacity: _pulseAnimation.value - 0.3,
                  child: Icon(
                    Icons.add,
                    size: 24,
                    color: primaryColor,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildCornerDecorations(Color primaryColor) {
    const double cornerSize = 20;
    const double cornerThickness = 4;

    return [
      // 左上角
      Positioned(
        top: -cornerThickness / 2,
        left: -cornerThickness / 2,
        child: Container(
          width: cornerSize,
          height: cornerSize,
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(color: primaryColor, width: cornerThickness),
              left: BorderSide(color: primaryColor, width: cornerThickness),
            ),
          ),
        ),
      ),

      // 右上角
      Positioned(
        top: -cornerThickness / 2,
        right: -cornerThickness / 2,
        child: Container(
          width: cornerSize,
          height: cornerSize,
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(color: primaryColor, width: cornerThickness),
              right: BorderSide(color: primaryColor, width: cornerThickness),
            ),
          ),
        ),
      ),

      // 左下角
      Positioned(
        bottom: -cornerThickness / 2,
        left: -cornerThickness / 2,
        child: Container(
          width: cornerSize,
          height: cornerSize,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: primaryColor, width: cornerThickness),
              left: BorderSide(color: primaryColor, width: cornerThickness),
            ),
          ),
        ),
      ),

      // 右下角
      Positioned(
        bottom: -cornerThickness / 2,
        right: -cornerThickness / 2,
        child: Container(
          width: cornerSize,
          height: cornerSize,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: primaryColor, width: cornerThickness),
              right: BorderSide(color: primaryColor, width: cornerThickness),
            ),
          ),
        ),
      ),
    ];
  }
}

/// 📷 相机覆盖引导组件
class CameraOverlayGuide extends StatefulWidget {
  final bool isVisible;
  final String guideText;
  final VoidCallback? onDismiss;

  const CameraOverlayGuide({
    Key? key,
    this.isVisible = true,
    this.guideText = '请将产品对准框内拍摄',
    this.onDismiss,
  }) : super(key: key);

  @override
  State<CameraOverlayGuide> createState() => _CameraOverlayGuideState();
}

class _CameraOverlayGuideState extends State<CameraOverlayGuide>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    if (widget.isVisible) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(CameraOverlayGuide oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(
          opacity: _animation.value,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7 * _animation.value),
            ),
            child: Stack(
              children: [
                // 半透明背景
                Positioned.fill(
                  child: GestureDetector(
                    onTap: widget.onDismiss,
                    child: Container(
                      color: Colors.transparent,
                    ),
                  ),
                ),

                // 中心引导框
                Center(
                  child: Transform.scale(
                    scale: _animation.value,
                    child: PhotoGuideAnimation(
                      isVisible: widget.isVisible,
                      guideText: widget.guideText,
                      onAnimationComplete: widget.onDismiss,
                    ),
                  ),
                ),

                // 关闭按钮
                Positioned(
                  top: 50,
                  right: 20,
                  child: Transform.scale(
                    scale: _animation.value,
                    child: GestureDetector(
                      onTap: widget.onDismiss,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
