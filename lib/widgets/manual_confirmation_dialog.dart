import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:loadguard/models/task_model.dart';

/// 🔧 人工确认对话框
/// 用于识别失败时的人工确认功能
class ManualConfirmationDialog extends StatefulWidget {
  final PhotoItem photo;
  final String? presetProductCode;
  final String? presetBatchNumber;
  final Function(String productCode, String batchNumber, String? notes)
      onConfirm;
  final VoidCallback? onCancel;

  const ManualConfirmationDialog({
    super.key,
    required this.photo,
    this.presetProductCode,
    this.presetBatchNumber,
    required this.onConfirm,
    this.onCancel,
  });

  @override
  State<ManualConfirmationDialog> createState() =>
      _ManualConfirmationDialogState();
}

class _ManualConfirmationDialogState extends State<ManualConfirmationDialog> {
  late TextEditingController _productCodeController;
  late TextEditingController _batchNumberController;
  late TextEditingController _notesController;

  bool _isLoading = false;
  String? _productCodeError;
  String? _batchNumberError;

  @override
  void initState() {
    super.initState();

    // 初始化控制器，优先使用预设值，其次使用识别结果
    _productCodeController = TextEditingController(
        text: widget.presetProductCode ??
            widget.photo.recognitionResult?.extractedProductCode ??
            widget.photo.matchedProductCode ??
            '');

    _batchNumberController = TextEditingController(
        text: widget.presetBatchNumber ??
            widget.photo.recognitionResult?.extractedBatchNumber ??
            widget.photo.matchedBatchNumber ??
            '');

    _notesController = TextEditingController();
  }

  @override
  void dispose() {
    _productCodeController.dispose();
    _batchNumberController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF8FAFC),
              Color(0xFFF1F5F9),
              Color(0xFFE2E8F0),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.15),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            _buildContent(),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF3B82F6), Color(0xFF1D4ED8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.edit_note,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '人工确认',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '照片: ${widget.photo.label}',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 识别结果展示
          if (widget.photo.recognitionResult?.ocrText != null) ...[
            _buildInfoCard(
              '原始识别文本',
              widget.photo.recognitionResult!.ocrText!,
              Icons.text_fields,
              Colors.blue,
            ),
            const SizedBox(height: 16),
          ],

          // 产品代码输入
          _buildInputField(
            controller: _productCodeController,
            label: '产品代码 (牌号)',
            hint: '请输入正确的产品代码',
            icon: Icons.inventory_2,
            errorText: _productCodeError,
            validator: _validateProductCode,
          ),

          const SizedBox(height: 16),

          // 批号输入
          _buildInputField(
            controller: _batchNumberController,
            label: '批号',
            hint: '请输入正确的批号',
            icon: Icons.qr_code,
            errorText: _batchNumberError,
            validator: _validateBatchNumber,
          ),

          const SizedBox(height: 16),

          // 备注输入
          _buildInputField(
            controller: _notesController,
            label: '备注 (可选)',
            hint: '请输入人工确认的原因或说明',
            icon: Icons.note_add,
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(
      String title, String content, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: color,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  content,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? errorText,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: Colors.blue),
            errorText: errorText,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.grey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.blue, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
          onChanged: (value) {
            if (validator != null) {
              setState(() {
                if (controller == _productCodeController) {
                  _productCodeError = validator(value);
                } else if (controller == _batchNumberController) {
                  _batchNumberError = validator(value);
                }
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _isLoading
                  ? null
                  : () {
                      widget.onCancel?.call();
                      Navigator.of(context).pop();
                    },
              icon: const Icon(Icons.cancel),
              label: const Text('取消'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                side: const BorderSide(color: Colors.grey),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: FilledButton.icon(
              onPressed: _isLoading ? null : _handleConfirm,
              icon: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.check_circle),
              label: Text(_isLoading ? '确认中...' : '确认'),
              style: FilledButton.styleFrom(
                backgroundColor: const Color(0xFF10B981),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String? _validateProductCode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '产品代码不能为空';
    }
    if (value.trim().length < 3) {
      return '产品代码长度不能少于3位';
    }
    return null;
  }

  String? _validateBatchNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '批号不能为空';
    }
    if (value.trim().length < 6) {
      return '批号长度不能少于6位';
    }
    return null;
  }

  void _handleConfirm() async {
    final productCode = _productCodeController.text.trim();
    final batchNumber = _batchNumberController.text.trim();
    final notes = _notesController.text.trim();

    // 验证输入
    final productCodeError = _validateProductCode(productCode);
    final batchNumberError = _validateBatchNumber(batchNumber);

    if (productCodeError != null || batchNumberError != null) {
      setState(() {
        _productCodeError = productCodeError;
        _batchNumberError = batchNumberError;
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 触觉反馈
      HapticFeedback.lightImpact();

      // 调用确认回调
      widget.onConfirm(productCode, batchNumber, notes.isEmpty ? null : notes);

      // 关闭对话框
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('确认失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
