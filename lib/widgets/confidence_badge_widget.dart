import 'package:flutter/material.dart';
import 'package:loadguard/services/confidence_evaluation_service.dart';

/// 🏷️ 置信度徽章组件
/// 用于在列表、卡片等地方简洁显示置信度信息
class ConfidenceBadgeWidget extends StatelessWidget {
  final ConfidenceScore confidenceScore;
  final bool showPercentage;
  final bool showLevel;
  final double size;
  final EdgeInsets? padding;

  const ConfidenceBadgeWidget({
    super.key,
    required this.confidenceScore,
    this.showPercentage = true,
    this.showLevel = false,
    this.size = 14.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final level = confidenceScore.level;
    final color = Color(level.colorValue);

    return Container(
      padding:
          padding ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getIcon(level),
            color: color,
            size: size,
          ),
          if (showPercentage || showLevel) const SizedBox(width: 4),
          if (showPercentage)
            Text(
              confidenceScore.percentageString,
              style: TextStyle(
                color: color,
                fontSize: size - 2,
                fontWeight: FontWeight.bold,
              ),
            ),
          if (showLevel) ...[
            if (showPercentage) const SizedBox(width: 4),
            Text(
              level.displayName,
              style: TextStyle(
                color: color,
                fontSize: size - 2,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getIcon(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.high:
        return Icons.verified;
      case ConfidenceLevel.medium:
        return Icons.check_circle_outline;
      case ConfidenceLevel.low:
        return Icons.warning_amber;
      case ConfidenceLevel.veryLow:
        return Icons.error_outline;
    }
  }
}

/// 🎯 置信度进度条组件
class ConfidenceProgressWidget extends StatelessWidget {
  final ConfidenceScore confidenceScore;
  final double height;
  final bool showLabel;

  const ConfidenceProgressWidget({
    super.key,
    required this.confidenceScore,
    this.height = 8.0,
    this.showLabel = true,
  });

  @override
  Widget build(BuildContext context) {
    final level = confidenceScore.level;
    final color = Color(level.colorValue);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showLabel) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '置信度',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                ),
              ),
              Text(
                confidenceScore.percentageString,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
        ],
        LinearProgressIndicator(
          value: confidenceScore.finalScore,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: height,
        ),
      ],
    );
  }
}

/// 📊 置信度指示器组件（圆形）
class ConfidenceIndicatorWidget extends StatelessWidget {
  final ConfidenceScore confidenceScore;
  final double size;
  final bool showPercentage;

  const ConfidenceIndicatorWidget({
    super.key,
    required this.confidenceScore,
    this.size = 60.0,
    this.showPercentage = true,
  });

  @override
  Widget build(BuildContext context) {
    final level = confidenceScore.level;
    final color = Color(level.colorValue);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color.withValues(alpha: 0.1),
        border: Border.all(color: color, width: 2),
      ),
      child: Stack(
        children: [
          // 圆形进度条
          Positioned.fill(
            child: CircularProgressIndicator(
              value: confidenceScore.finalScore,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(color),
              strokeWidth: 3,
            ),
          ),
          // 中心内容
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _getIcon(level),
                  color: color,
                  size: size * 0.3,
                ),
                if (showPercentage) ...[
                  const SizedBox(height: 2),
                  Text(
                    confidenceScore.percentageString,
                    style: TextStyle(
                      color: color,
                      fontSize: size * 0.15,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getIcon(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.high:
        return Icons.check;
      case ConfidenceLevel.medium:
        return Icons.remove;
      case ConfidenceLevel.low:
        return Icons.warning;
      case ConfidenceLevel.veryLow:
        return Icons.close;
    }
  }
}
