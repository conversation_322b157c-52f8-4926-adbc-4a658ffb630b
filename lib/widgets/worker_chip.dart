import 'package:flutter/material.dart';
import '../models/worker_info_data.dart';
import '../utils/theme_colors.dart';

/// 工人信息芯片组件
/// 用于显示工人的基本信息，支持多种显示样式
class WorkerChip extends StatelessWidget {
  final WorkerInfo worker;
  final bool showRole;
  final bool showWarehouse;
  final bool showGroup;
  final VoidCallback? onTap;
  final bool isSelected;
  final Color? backgroundColor;
  final Color? textColor;
  final EdgeInsetsGeometry? padding;
  final double? fontSize;

  const WorkerChip({
    Key? key,
    required this.worker,
    this.showRole = false,
    this.showWarehouse = false,
    this.showGroup = false,
    this.onTap,
    this.isSelected = false,
    this.backgroundColor,
    this.textColor,
    this.padding,
    this.fontSize,
  }) : super(key: key);

  /// 根据WorkerInfo创建WorkerChip的便捷构造函数
  factory WorkerChip.fromWorker(
    WorkerInfo worker, {
    bool showRole = false,
    bool showWarehouse = false,
    bool showGroup = false,
    VoidCallback? onTap,
    bool isSelected = false,
  }) {
    return WorkerChip(
      worker: worker,
      showRole: showRole,
      showWarehouse: showWarehouse,
      showGroup: showGroup,
      onTap: onTap,
      isSelected: isSelected,
    );
  }

  /// 创建紧凑样式的WorkerChip
  factory WorkerChip.compact(
    WorkerInfo worker, {
    VoidCallback? onTap,
    bool isSelected = false,
  }) {
    return WorkerChip(
      worker: worker,
      onTap: onTap,
      isSelected: isSelected,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      fontSize: 12,
    );
  }

  /// 创建详细样式的WorkerChip
  factory WorkerChip.detailed(
    WorkerInfo worker, {
    VoidCallback? onTap,
    bool isSelected = false,
  }) {
    return WorkerChip(
      worker: worker,
      showRole: true,
      showWarehouse: true,
      showGroup: true,
      onTap: onTap,
      isSelected: isSelected,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    );
  }

  @override
  Widget build(BuildContext context) {
    final effectivePadding =
        padding ?? const EdgeInsets.symmetric(horizontal: 10, vertical: 6);
    final effectiveFontSize = fontSize ?? 14.0;
    final theme = Theme.of(context);
    final effectiveBackgroundColor = backgroundColor ??
        (isSelected
            ? ThemeColors.primary
            : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3));
    final effectiveTextColor = textColor ??
        (isSelected ? ThemeColors.textOnDark : theme.colorScheme.onSurface);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: effectivePadding,
        decoration: BoxDecoration(
          color: effectiveBackgroundColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? ThemeColors.primary
                : theme.colorScheme.outline.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 角色图标
            if (showRole && worker.role.isNotEmpty)
              Container(
                margin: const EdgeInsets.only(right: 6),
                child: Icon(
                  _getRoleIcon(worker.role),
                  size: effectiveFontSize + 2,
                  color: _getRoleColor(worker.role),
                ),
              ),

            // 主要信息
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 姓名
                Text(
                  worker.name,
                  style: TextStyle(
                    fontSize: effectiveFontSize,
                    fontWeight: FontWeight.w600,
                    color: effectiveTextColor,
                  ),
                ),

                // 附加信息
                if (showRole || showWarehouse || showGroup)
                  Text(
                    _buildSubtitle(),
                    style: TextStyle(
                      fontSize: effectiveFontSize - 2,
                      color: effectiveTextColor.withValues(alpha: 0.7),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建副标题文本
  String _buildSubtitle() {
    final parts = <String>[];

    if (showRole && worker.role.isNotEmpty && worker.role != '无') {
      parts.add(worker.role);
    }

    if (showWarehouse && worker.warehouse.isNotEmpty) {
      parts.add(worker.warehouse);
    }

    if (showGroup && worker.group.isNotEmpty) {
      parts.add(worker.group);
    }

    return parts.join(' • ');
  }

  /// 根据角色获取图标
  IconData _getRoleIcon(String role) {
    switch (role) {
      case '叉车':
        return Icons.warehouse;
      case '仓管':
        return Icons.inventory;
      case '主管':
        return Icons.supervisor_account;
      case '班长':
        return Icons.groups;
      default:
        return Icons.person;
    }
  }

  /// 根据角色获取颜色
  Color _getRoleColor(String role) {
    switch (role) {
      case '叉车':
        return ThemeColors.primary;
      case '仓管':
        return ThemeColors.success;
      case '主管':
        return ThemeColors.warning;
      case '班长':
        return ThemeColors.accent;
      default:
        return ThemeColors.textMedium;
    }
  }
}

/// 工人信息芯片列表组件
/// 用于显示多个工人的信息芯片
class WorkerChipList extends StatelessWidget {
  final List<WorkerInfo> workers;
  final bool showRole;
  final bool showWarehouse;
  final bool showGroup;
  final Function(WorkerInfo)? onWorkerTap;
  final Set<String>? selectedWorkerIds;
  final Axis direction;
  final double spacing;
  final double runSpacing;
  final EdgeInsetsGeometry? padding;

  const WorkerChipList({
    Key? key,
    required this.workers,
    this.showRole = false,
    this.showWarehouse = false,
    this.showGroup = false,
    this.onWorkerTap,
    this.selectedWorkerIds,
    this.direction = Axis.horizontal,
    this.spacing = 8.0,
    this.runSpacing = 8.0,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (workers.isEmpty) {
      return const SizedBox.shrink();
    }

    final chips = workers.map((worker) {
      final isSelected = selectedWorkerIds?.contains(worker.id) ?? false;

      return WorkerChip(
        worker: worker,
        showRole: showRole,
        showWarehouse: showWarehouse,
        showGroup: showGroup,
        isSelected: isSelected,
        onTap: onWorkerTap != null ? () => onWorkerTap!(worker) : null,
      );
    }).toList();

    Widget content;
    if (direction == Axis.horizontal) {
      content = Wrap(
        spacing: spacing,
        runSpacing: runSpacing,
        children: chips,
      );
    } else {
      content = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: chips
            .map((chip) => Padding(
                  padding: EdgeInsets.only(bottom: spacing),
                  child: chip,
                ))
            .toList(),
      );
    }

    if (padding != null) {
      content = Padding(
        padding: padding!,
        child: content,
      );
    }

    return content;
  }
}
