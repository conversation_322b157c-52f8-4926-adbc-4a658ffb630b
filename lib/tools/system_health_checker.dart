import 'package:flutter/material.dart';
import '../services/data_consistency_checker.dart';
import '../services/unified_license_storage.dart';
import '../services/app_security_service.dart';
import '../services/hardware_fingerprint.dart';
import '../services/strict_security_service.dart';
import '../services/logging_service.dart';

/// 🏥 系统健康检查和修复工具
/// 提供完整的系统诊断和自动修复功能
class SystemHealthChecker {
  static const String _tag = 'SystemHealthChecker';
  
  /// 执行完整的系统健康检查
  static Future<SystemHealthReport> performHealthCheck() async {
    LoggingService.info('🏥 开始系统健康检查', tag: _tag);
    
    final report = SystemHealthReport();
    report.startTime = DateTime.now();
    
    try {
      // 1. 数据一致性检查
      LoggingService.info('🔍 执行数据一致性检查', tag: _tag);
      report.dataConsistency = await DataConsistencyChecker.performFullCheck();
      
      // 2. 设备ID验证
      LoggingService.info('📱 验证设备ID', tag: _tag);
      report.deviceIdValidation = await _validateDeviceIds();
      
      // 3. 许可证状态检查
      LoggingService.info('📄 检查许可证状态', tag: _tag);
      report.licenseStatus = await _checkLicenseStatus();
      
      // 4. 存储系统检查
      LoggingService.info('💾 检查存储系统', tag: _tag);
      report.storageHealth = await _checkStorageHealth();
      
      // 5. 服务初始化检查
      LoggingService.info('⚙️ 检查服务初始化', tag: _tag);
      report.serviceInitialization = await _checkServiceInitialization();
      
      // 6. 计算总体健康状态
      report.overallHealth = _calculateOverallHealth(report);
      
      report.endTime = DateTime.now();
      report.duration = report.endTime.difference(report.startTime);
      
      LoggingService.info('✅ 系统健康检查完成，总体状态: ${report.overallHealth}', tag: _tag);
      return report;
    } catch (e) {
      LoggingService.error('❌ 系统健康检查失败', error: e, tag: _tag);
      report.overallHealth = HealthStatus.critical;
      report.errorMessage = e.toString();
      report.endTime = DateTime.now();
      return report;
    }
  }
  
  /// 执行自动修复
  static Future<SystemRepairResult> performAutoRepair() async {
    LoggingService.info('🔧 开始系统自动修复', tag: _tag);
    
    final result = SystemRepairResult();
    result.startTime = DateTime.now();
    
    try {
      // 1. 执行数据一致性修复
      LoggingService.info('🔧 执行数据一致性修复', tag: _tag);
      result.dataRepair = await DataConsistencyChecker.performAutoRepair();
      
      // 2. 修复设备ID问题
      LoggingService.info('📱 修复设备ID问题', tag: _tag);
      result.deviceIdRepair = await _repairDeviceIds();
      
      // 3. 修复许可证问题
      LoggingService.info('📄 修复许可证问题', tag: _tag);
      result.licenseRepair = await _repairLicenseIssues();
      
      // 4. 修复存储问题
      LoggingService.info('💾 修复存储问题', tag: _tag);
      result.storageRepair = await _repairStorageIssues();
      
      // 5. 重新初始化服务
      LoggingService.info('⚙️ 重新初始化服务', tag: _tag);
      result.serviceRepair = await _repairServiceInitialization();
      
      // 6. 验证修复结果
      LoggingService.info('✅ 验证修复结果', tag: _tag);
      result.verificationReport = await performHealthCheck();
      
      result.endTime = DateTime.now();
      result.duration = result.endTime.difference(result.startTime);
      
      LoggingService.info('✅ 系统自动修复完成', tag: _tag);
      return result;
    } catch (e) {
      LoggingService.error('❌ 系统自动修复失败', error: e, tag: _tag);
      result.errorMessage = e.toString();
      result.endTime = DateTime.now();
      return result;
    }
  }
  
  /// 验证设备ID
  static Future<DeviceIdValidation> _validateDeviceIds() async {
    final validation = DeviceIdValidation();
    
    try {
      // 获取所有设备ID
      validation.appSecurityId = await AppSecurityService.getDeviceId();
      validation.hardwareFingerprintId = await HardwareFingerprint.getDeviceId();
      
      await StrictSecurityService.initialize();
      final licenseStatus = await StrictSecurityService.getLicenseStatus();
      validation.strictSecurityId = licenseStatus['deviceId'] ?? 'Unknown';
      
      // 验证一致性
      validation.isConsistent = validation.appSecurityId == validation.hardwareFingerprintId && 
                               validation.appSecurityId == validation.strictSecurityId;
      
      // 验证格式
      validation.hasValidFormat = validation.appSecurityId.isNotEmpty && 
                                 validation.appSecurityId.length >= 8;
      
      if (validation.isConsistent && validation.hasValidFormat) {
        validation.status = HealthStatus.healthy;
      } else if (validation.isConsistent) {
        validation.status = HealthStatus.warning;
      } else {
        validation.status = HealthStatus.unhealthy;
      }
    } catch (e) {
      validation.status = HealthStatus.critical;
      validation.errorMessage = e.toString();
    }
    
    return validation;
  }
  
  /// 检查许可证状态
  static Future<LicenseStatusCheck> _checkLicenseStatus() async {
    final check = LicenseStatusCheck();
    
    try {
      // 检查统一存储
      final unifiedData = await UnifiedLicenseStorage.loadLicense();
      check.hasUnifiedLicense = unifiedData != null;
      
      // 检查许可证有效性
      if (unifiedData != null) {
        final expiryTime = unifiedData['expiryTime'] as int?;
        if (expiryTime != null) {
          final now = DateTime.now().millisecondsSinceEpoch;
          check.isValid = now < expiryTime;
          check.remainingDays = ((expiryTime - now) / (24 * 60 * 60 * 1000)).ceil();
        } else {
          check.isValid = true; // 永久许可证
          check.remainingDays = -1;
        }
      }
      
      // 检查存储状态
      final storageStatus = await UnifiedLicenseStorage.getStorageStatus();
      check.needsMigration = storageStatus['needsMigration'] ?? false;
      
      if (check.hasUnifiedLicense && check.isValid && !check.needsMigration) {
        check.status = HealthStatus.healthy;
      } else if (check.hasUnifiedLicense && check.isValid) {
        check.status = HealthStatus.warning;
      } else {
        check.status = HealthStatus.unhealthy;
      }
    } catch (e) {
      check.status = HealthStatus.critical;
      check.errorMessage = e.toString();
    }
    
    return check;
  }
  
  /// 检查存储系统健康状态
  static Future<StorageHealthCheck> _checkStorageHealth() async {
    final check = StorageHealthCheck();
    
    try {
      final storageStatus = await UnifiedLicenseStorage.getStorageStatus();
      
      check.hasUnifiedStorage = storageStatus['hasUnifiedData'] ?? false;
      check.hasBackup = storageStatus['hasBackup'] ?? false;
      check.isComplete = storageStatus['isComplete'] ?? false;
      check.hasLegacyData = storageStatus['needsMigration'] ?? false;
      
      if (check.isComplete && !check.hasLegacyData) {
        check.status = HealthStatus.healthy;
      } else if (check.hasUnifiedStorage) {
        check.status = HealthStatus.warning;
      } else {
        check.status = HealthStatus.unhealthy;
      }
    } catch (e) {
      check.status = HealthStatus.critical;
      check.errorMessage = e.toString();
    }
    
    return check;
  }
  
  /// 检查服务初始化状态
  static Future<ServiceInitializationCheck> _checkServiceInitialization() async {
    final check = ServiceInitializationCheck();
    
    try {
      // 测试各个服务是否正常工作
      check.appSecurityInitialized = await _testAppSecurityService();
      check.strictSecurityInitialized = await _testStrictSecurityService();
      check.hardwareFingerprintInitialized = await _testHardwareFingerprintService();
      
      final initializedCount = [
        check.appSecurityInitialized,
        check.strictSecurityInitialized,
        check.hardwareFingerprintInitialized,
      ].where((initialized) => initialized).length;
      
      if (initializedCount == 3) {
        check.status = HealthStatus.healthy;
      } else if (initializedCount >= 2) {
        check.status = HealthStatus.warning;
      } else {
        check.status = HealthStatus.unhealthy;
      }
    } catch (e) {
      check.status = HealthStatus.critical;
      check.errorMessage = e.toString();
    }
    
    return check;
  }
  
  /// 测试AppSecurityService
  static Future<bool> _testAppSecurityService() async {
    try {
      await AppSecurityService.getDeviceId();
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// 测试StrictSecurityService
  static Future<bool> _testStrictSecurityService() async {
    try {
      await StrictSecurityService.initialize();
      await StrictSecurityService.getLicenseStatus();
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// 测试HardwareFingerprintService
  static Future<bool> _testHardwareFingerprintService() async {
    try {
      await HardwareFingerprint.getDeviceId();
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// 修复设备ID问题
  static Future<RepairResult> _repairDeviceIds() async {
    try {
      // 设备ID已经统一，只需要验证
      final validation = await _validateDeviceIds();
      if (validation.isConsistent) {
        return RepairResult(success: true, message: '设备ID已经一致');
      } else {
        return RepairResult(success: false, message: '设备ID仍然不一致');
      }
    } catch (e) {
      return RepairResult(success: false, message: '设备ID修复失败: $e');
    }
  }
  
  /// 修复许可证问题
  static Future<RepairResult> _repairLicenseIssues() async {
    try {
      // 触发数据迁移
      await UnifiedLicenseStorage.loadLicense();
      return RepairResult(success: true, message: '许可证数据已迁移到统一存储');
    } catch (e) {
      return RepairResult(success: false, message: '许可证修复失败: $e');
    }
  }
  
  /// 修复存储问题
  static Future<RepairResult> _repairStorageIssues() async {
    try {
      // 检查并修复存储完整性
      final storageStatus = await UnifiedLicenseStorage.getStorageStatus();
      if (storageStatus['isComplete'] == true) {
        return RepairResult(success: true, message: '存储系统健康');
      } else {
        return RepairResult(success: false, message: '存储系统需要手动修复');
      }
    } catch (e) {
      return RepairResult(success: false, message: '存储修复失败: $e');
    }
  }
  
  /// 修复服务初始化问题
  static Future<RepairResult> _repairServiceInitialization() async {
    try {
      // 重新初始化所有服务
      await StrictSecurityService.initialize();
      return RepairResult(success: true, message: '服务重新初始化成功');
    } catch (e) {
      return RepairResult(success: false, message: '服务初始化修复失败: $e');
    }
  }
  
  /// 计算总体健康状态
  static HealthStatus _calculateOverallHealth(SystemHealthReport report) {
    final statuses = [
      report.dataConsistency.overallStatus == ConsistencyStatus.consistent ? HealthStatus.healthy : HealthStatus.unhealthy,
      report.deviceIdValidation.status,
      report.licenseStatus.status,
      report.storageHealth.status,
      report.serviceInitialization.status,
    ];
    
    if (statuses.any((status) => status == HealthStatus.critical)) {
      return HealthStatus.critical;
    } else if (statuses.any((status) => status == HealthStatus.unhealthy)) {
      return HealthStatus.unhealthy;
    } else if (statuses.any((status) => status == HealthStatus.warning)) {
      return HealthStatus.warning;
    } else {
      return HealthStatus.healthy;
    }
  }
}

/// 健康状态枚举
enum HealthStatus {
  healthy,    // 健康
  warning,    // 警告
  unhealthy,  // 不健康
  critical,   // 严重
}

/// 系统健康报告
class SystemHealthReport {
  late DateTime startTime;
  late DateTime endTime;
  late Duration duration;
  late DataConsistencyReport dataConsistency;
  late DeviceIdValidation deviceIdValidation;
  late LicenseStatusCheck licenseStatus;
  late StorageHealthCheck storageHealth;
  late ServiceInitializationCheck serviceInitialization;
  late HealthStatus overallHealth;
  String? errorMessage;
}

/// 设备ID验证结果
class DeviceIdValidation {
  String appSecurityId = '';
  String hardwareFingerprintId = '';
  String strictSecurityId = '';
  bool isConsistent = false;
  bool hasValidFormat = false;
  HealthStatus status = HealthStatus.unhealthy;
  String? errorMessage;
}

/// 许可证状态检查结果
class LicenseStatusCheck {
  bool hasUnifiedLicense = false;
  bool isValid = false;
  int remainingDays = 0;
  bool needsMigration = false;
  HealthStatus status = HealthStatus.unhealthy;
  String? errorMessage;
}

/// 存储健康检查结果
class StorageHealthCheck {
  bool hasUnifiedStorage = false;
  bool hasBackup = false;
  bool isComplete = false;
  bool hasLegacyData = false;
  HealthStatus status = HealthStatus.unhealthy;
  String? errorMessage;
}

/// 服务初始化检查结果
class ServiceInitializationCheck {
  bool appSecurityInitialized = false;
  bool strictSecurityInitialized = false;
  bool hardwareFingerprintInitialized = false;
  HealthStatus status = HealthStatus.unhealthy;
  String? errorMessage;
}

/// 系统修复结果
class SystemRepairResult {
  late DateTime startTime;
  late DateTime endTime;
  late Duration duration;
  late DataRepairResult dataRepair;
  late RepairResult deviceIdRepair;
  late RepairResult licenseRepair;
  late RepairResult storageRepair;
  late RepairResult serviceRepair;
  late SystemHealthReport verificationReport;
  String? errorMessage;
}

/// 修复结果
class RepairResult {
  final bool success;
  final String message;
  
  RepairResult({required this.success, required this.message});
}
