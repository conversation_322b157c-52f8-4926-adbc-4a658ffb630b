import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/task_model.dart';
import '../repositories/task_repository.dart';
import '../services/hive_storage_service.dart';
import '../services/shared_preferences_data_source.dart';
import '../utils/app_logger.dart';

/// TaskRepository Provider
final taskRepositoryProvider = Provider<TaskRepository>((ref) {
  final hiveStorage = HiveStorageService();
  final backupStorage = SharedPreferencesDataSource();
  
  return TaskRepositoryImpl(
    hiveStorage: hiveStorage,
    backupStorage: backupStorage,
  );
});

/// 任务列表状态管理
/// 替换TaskService中的ChangeNotifier部分，提供响应式状态管理
final taskListProvider = StreamProvider<List<TaskModel>>((ref) {
  final repository = ref.watch(taskRepositoryProvider);
  return repository.watchTasks();
});

/// 当前任务状态管理
final currentTaskProvider = StateNotifierProvider<CurrentTaskNotifier, TaskModel?>((ref) {
  final repository = ref.watch(taskRepositoryProvider);
  return CurrentTaskNotifier(repository);
});

/// 当前任务状态管理器
class CurrentTaskNotifier extends StateNotifier<TaskModel?> {
  static const String _tagName = 'CurrentTaskNotifier';
  
  final TaskRepository _repository;
  
  CurrentTaskNotifier(this._repository) : super(null) {
    _loadCurrentTask();
  }
  
  /// 加载当前任务
  Future<void> _loadCurrentTask() async {
    try {
      final currentTask = _repository.currentTask;
      state = currentTask;
      AppLogger.info('加载当前任务: ${currentTask?.id ?? 'null'}', tag: _tagName);
    } catch (e) {
      AppLogger.error('加载当前任务失败: $e', tag: _tagName);
    }
  }
  
  /// 设置当前任务
  Future<void> setCurrentTask(TaskModel? task) async {
    try {
      await _repository.setCurrentTask(task);
      state = task;
      AppLogger.info('设置当前任务: ${task?.id ?? 'null'}', tag: _tagName);
    } catch (e) {
      AppLogger.error('设置当前任务失败: $e', tag: _tagName);
      rethrow;
    }
  }
  
  /// 更新当前任务
  Future<void> updateCurrentTask(TaskModel task) async {
    try {
      await _repository.saveTask(task);
      await _repository.setCurrentTask(task);
      state = task;
      AppLogger.info('更新当前任务: ${task.id}', tag: _tagName);
    } catch (e) {
      AppLogger.error('更新当前任务失败: ${task.id}, 错误: $e', tag: _tagName);
      rethrow;
    }
  }
  
  /// 清除当前任务
  Future<void> clearCurrentTask() async {
    try {
      await _repository.setCurrentTask(null);
      state = null;
      AppLogger.info('清除当前任务', tag: _tagName);
    } catch (e) {
      AppLogger.error('清除当前任务失败: $e', tag: _tagName);
      rethrow;
    }
  }
}

/// 任务操作Provider
/// 提供任务的CRUD操作，保持业务逻辑的纯净性
final taskOperationsProvider = Provider<TaskOperations>((ref) {
  final repository = ref.watch(taskRepositoryProvider);
  return TaskOperations(repository);
});

/// 任务操作类
/// 封装任务的业务操作，替换TaskService中的业务逻辑部分
class TaskOperations {
  static const String _tagName = 'TaskOperations';
  
  final TaskRepository _repository;
  
  TaskOperations(this._repository);
  
  /// 创建新任务
  Future<TaskModel> createTask({
    required String template,
    required String productCode,
    required String batchNumber,
    required int quantity,
    required List<String> participants,
  }) async {
    try {
      AppLogger.info('创建新任务: $productCode', tag: _tagName);
      
      // 这里保持原有的任务创建逻辑
      // 从TaskService中迁移createTask方法的核心逻辑
      final task = TaskModel(
        id: _generateTaskId(),
        template: template,
        productCode: productCode,
        batchNumber: batchNumber,
        quantity: quantity,
        participants: participants,
        createTime: DateTime.now(),
        photos: _createPhotosForTemplate(template),
        isCompleted: false,
        recognitionMetadata: {},
      );
      
      await _repository.saveTask(task);
      AppLogger.info('任务创建成功: ${task.id}', tag: _tagName);
      
      return task;
    } catch (e, stackTrace) {
      AppLogger.error('创建任务失败: $productCode, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 更新任务
  Future<void> updateTask(TaskModel task) async {
    try {
      await _repository.saveTask(task);
      AppLogger.info('任务更新成功: ${task.id}', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('更新任务失败: ${task.id}, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 删除任务
  Future<void> deleteTask(String taskId) async {
    try {
      await _repository.deleteTask(taskId);
      AppLogger.info('任务删除成功: $taskId', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('删除任务失败: $taskId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 完成任务
  Future<void> completeTask(String taskId) async {
    try {
      final task = await _repository.getTask(taskId);
      if (task == null) {
        throw Exception('任务不存在: $taskId');
      }
      
      final completedTask = task.copyWith(
        isCompleted: true,
        completedAt: DateTime.now(),
      );
      
      await _repository.saveTask(completedTask);
      AppLogger.info('任务完成: $taskId', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('完成任务失败: $taskId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 更新任务照片
  Future<void> updateTaskPhoto(String taskId, String photoId, String imagePath) async {
    try {
      final task = await _repository.getTask(taskId);
      if (task == null) {
        throw Exception('任务不存在: $taskId');
      }
      
      final updatedPhotos = task.photos.map((photo) {
        if (photo.id == photoId) {
          return photo.copyWith(imagePath: imagePath);
        }
        return photo;
      }).toList();
      
      final updatedTask = task.copyWith(photos: updatedPhotos);
      await _repository.saveTask(updatedTask);
      
      AppLogger.info('任务照片更新成功: $taskId, 照片: $photoId', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('更新任务照片失败: $taskId, 照片: $photoId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 更新照片识别结果
  Future<void> updatePhotoRecognition(
    String taskId, 
    String photoId, 
    Map<String, dynamic> recognitionResult,
  ) async {
    try {
      final task = await _repository.getTask(taskId);
      if (task == null) {
        throw Exception('任务不存在: $taskId');
      }
      
      final updatedPhotos = task.photos.map((photo) {
        if (photo.id == photoId) {
          return photo.copyWith(
            recognitionResult: recognitionResult,
            isRecognitionCompleted: true,
          );
        }
        return photo;
      }).toList();
      
      final updatedTask = task.copyWith(photos: updatedPhotos);
      await _repository.saveTask(updatedTask);
      
      AppLogger.info('照片识别结果更新成功: $taskId, 照片: $photoId', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('更新照片识别结果失败: $taskId, 照片: $photoId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 生成任务ID
  String _generateTaskId() {
    return 'task_${DateTime.now().millisecondsSinceEpoch}';
  }
  
  /// 为模板创建照片配置
  List<PhotoItem> _createPhotosForTemplate(String template) {
    // 这里保持原有的照片创建逻辑
    // 从TaskService中迁移相关方法
    switch (template) {
      case '平板车':
        return _createFlatbedPhotos();
      case '集装箱':
        return _createContainerPhotos();
      default:
        return [];
    }
  }
  
  /// 创建平板车照片配置
  List<PhotoItem> _createFlatbedPhotos() {
    return [
      PhotoItem(
        id: 'flatbed_front',
        label: '车头照片',
        isRequired: true,
        needRecognition: false,
      ),
      PhotoItem(
        id: 'flatbed_cargo',
        label: '货物照片',
        isRequired: true,
        needRecognition: true,
      ),
      PhotoItem(
        id: 'flatbed_label',
        label: '标签照片',
        isRequired: true,
        needRecognition: true,
      ),
    ];
  }
  
  /// 创建集装箱照片配置
  List<PhotoItem> _createContainerPhotos() {
    return [
      PhotoItem(
        id: 'container_number',
        label: '箱号照片',
        isRequired: true,
        needRecognition: true,
      ),
      PhotoItem(
        id: 'container_seal',
        label: '铅封照片',
        isRequired: true,
        needRecognition: false,
      ),
      PhotoItem(
        id: 'container_cargo',
        label: '货物照片',
        isRequired: true,
        needRecognition: true,
      ),
    ];
  }
}

/// 任务统计Provider
final taskStatsProvider = Provider<TaskStats>((ref) {
  final taskListAsync = ref.watch(taskListProvider);
  
  return taskListAsync.when(
    data: (tasks) => TaskStats.fromTasks(tasks),
    loading: () => TaskStats.empty(),
    error: (error, stack) => TaskStats.empty(),
  );
});

/// 任务统计数据
class TaskStats {
  final int totalTasks;
  final int completedTasks;
  final int pendingTasks;
  final double completionRate;
  
  const TaskStats({
    required this.totalTasks,
    required this.completedTasks,
    required this.pendingTasks,
    required this.completionRate,
  });
  
  factory TaskStats.fromTasks(List<TaskModel> tasks) {
    final total = tasks.length;
    final completed = tasks.where((task) => task.isCompleted).length;
    final pending = total - completed;
    final rate = total > 0 ? completed / total : 0.0;
    
    return TaskStats(
      totalTasks: total,
      completedTasks: completed,
      pendingTasks: pending,
      completionRate: rate,
    );
  }
  
  factory TaskStats.empty() {
    return const TaskStats(
      totalTasks: 0,
      completedTasks: 0,
      pendingTasks: 0,
      completionRate: 0.0,
    );
  }
}
