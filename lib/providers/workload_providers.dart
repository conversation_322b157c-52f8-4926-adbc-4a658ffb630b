import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/workload_models.dart';
import '../repositories/workload_repository.dart';
import '../services/hive_storage_service.dart';
import '../services/shared_preferences_data_source.dart';
import '../services/workload_calculation_service.dart';
import '../utils/app_logger.dart';

/// WorkloadRepository Provider
final workloadRepositoryProvider = Provider<WorkloadRepository>((ref) {
  final hiveStorage = HiveStorageService();
  final backupStorage = SharedPreferencesDataSource();
  
  return WorkloadRepositoryImpl(
    hiveStorage: hiveStorage,
    backupStorage: backupStorage,
  );
});

/// 工作量统计状态管理
final workloadStatisticsProvider = StreamProvider<List<WorkloadStatistics>>((ref) {
  final repository = ref.watch(workloadRepositoryProvider);
  return repository.watchWorkloadStatistics();
});

/// 工作量概览状态管理
final workloadOverviewProvider = FutureProvider<WorkloadOverview>((ref) async {
  final repository = ref.watch(workloadRepositoryProvider);
  return await repository.getWorkloadOverview();
});

/// 工作量过滤器状态
final workloadFilterProvider = StateNotifierProvider<WorkloadFilterNotifier, WorkloadFilter>((ref) {
  return WorkloadFilterNotifier();
});

/// 工作量过滤器
class WorkloadFilter {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? warehouse;
  final String? group;
  final String? workerId;

  const WorkloadFilter({
    this.startDate,
    this.endDate,
    this.warehouse,
    this.group,
    this.workerId,
  });

  WorkloadFilter copyWith({
    DateTime? startDate,
    DateTime? endDate,
    String? warehouse,
    String? group,
    String? workerId,
    bool clearStartDate = false,
    bool clearEndDate = false,
    bool clearWarehouse = false,
    bool clearGroup = false,
    bool clearWorkerId = false,
  }) {
    return WorkloadFilter(
      startDate: clearStartDate ? null : (startDate ?? this.startDate),
      endDate: clearEndDate ? null : (endDate ?? this.endDate),
      warehouse: clearWarehouse ? null : (warehouse ?? this.warehouse),
      group: clearGroup ? null : (group ?? this.group),
      workerId: clearWorkerId ? null : (workerId ?? this.workerId),
    );
  }
}

/// 工作量过滤器状态管理
class WorkloadFilterNotifier extends StateNotifier<WorkloadFilter> {
  WorkloadFilterNotifier() : super(const WorkloadFilter());

  void setDateRange(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(
      startDate: startDate,
      endDate: endDate,
      clearStartDate: startDate == null,
      clearEndDate: endDate == null,
    );
  }

  void setWarehouse(String? warehouse) {
    state = state.copyWith(
      warehouse: warehouse,
      clearWarehouse: warehouse == null,
    );
  }

  void setGroup(String? group) {
    state = state.copyWith(
      group: group,
      clearGroup: group == null,
    );
  }

  void setWorkerId(String? workerId) {
    state = state.copyWith(
      workerId: workerId,
      clearWorkerId: workerId == null,
    );
  }

  void clearFilters() {
    state = const WorkloadFilter();
  }
}

/// 过滤后的工作量统计
final filteredWorkloadStatisticsProvider = Provider<AsyncValue<List<WorkloadStatistics>>>((ref) {
  final statisticsAsync = ref.watch(workloadStatisticsProvider);
  final filter = ref.watch(workloadFilterProvider);
  
  return statisticsAsync.when(
    data: (statistics) {
      // 应用过滤器
      final filtered = statistics.where((stat) {
        // 时间过滤
        if (filter.startDate != null && stat.lastUpdated.isBefore(filter.startDate!)) {
          return false;
        }
        if (filter.endDate != null && stat.lastUpdated.isAfter(filter.endDate!)) {
          return false;
        }
        
        // 仓库过滤
        if (filter.warehouse != null && stat.warehouse != filter.warehouse) {
          return false;
        }
        
        // 小组过滤
        if (filter.group != null && stat.group != filter.group) {
          return false;
        }
        
        // 工人过滤
        if (filter.workerId != null && stat.workerId != filter.workerId) {
          return false;
        }
        
        return true;
      }).toList();
      
      return AsyncValue.data(filtered);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

/// 过滤后的工作量概览
final filteredWorkloadOverviewProvider = Provider<AsyncValue<WorkloadOverview>>((ref) {
  final filter = ref.watch(workloadFilterProvider);
  final statisticsAsync = ref.watch(filteredWorkloadStatisticsProvider);
  
  return statisticsAsync.when(
    data: (statistics) {
      if (statistics.isEmpty) {
        return const AsyncValue.data(WorkloadOverview(
          totalTasks: 0,
          completedTasks: 0,
          totalTonnage: 0.0,
          completedTonnage: 0.0,
          activeWorkers: 0,
          averageEfficiency: 0.0,
          completionRate: 0.0,
        ));
      }
      
      int totalTasks = 0;
      int completedTasks = 0;
      double totalTonnage = 0.0;
      double completedTonnage = 0.0;
      double totalEfficiency = 0.0;
      
      for (final stat in statistics) {
        totalTasks += stat.totalTasks;
        completedTasks += stat.completedTasks;
        totalTonnage += stat.assignedTonnage;
        completedTonnage += stat.completedTonnage;
        totalEfficiency += stat.efficiency;
      }
      
      final averageEfficiency = statistics.isNotEmpty 
          ? totalEfficiency / statistics.length 
          : 0.0;
      
      final completionRate = totalTasks > 0 
          ? completedTasks / totalTasks 
          : 0.0;
      
      final overview = WorkloadOverview(
        totalTasks: totalTasks,
        completedTasks: completedTasks,
        totalTonnage: totalTonnage,
        completedTonnage: completedTonnage,
        activeWorkers: statistics.length,
        averageEfficiency: averageEfficiency,
        completionRate: completionRate,
      );
      
      return AsyncValue.data(overview);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
  );
});

/// 工作量操作Provider
final workloadOperationsProvider = Provider<WorkloadOperations>((ref) {
  final repository = ref.watch(workloadRepositoryProvider);
  return WorkloadOperations(repository);
});

/// 工作量操作类
class WorkloadOperations {
  static const String _tagName = 'WorkloadOperations';
  
  final WorkloadRepository _repository;
  
  WorkloadOperations(this._repository);
  
  /// 保存工作量分配
  Future<void> saveWorkloadAssignment(String taskId, WorkloadAssignment assignment) async {
    try {
      await _repository.saveWorkloadAssignment(taskId, assignment);
      AppLogger.info('工作量分配保存成功: $taskId', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('保存工作量分配失败: $taskId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 获取任务的工作量分配
  Future<WorkloadAssignment?> getTaskWorkloadAssignment(String taskId) async {
    try {
      return await _repository.getTaskWorkloadAssignment(taskId);
    } catch (e, stackTrace) {
      AppLogger.error('获取任务工作量分配失败: $taskId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      return null;
    }
  }
  
  /// 删除工作量分配
  Future<void> deleteWorkloadAssignment(String taskId) async {
    try {
      await _repository.deleteWorkloadAssignment(taskId);
      AppLogger.info('工作量分配删除成功: $taskId', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('删除工作量分配失败: $taskId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
  
  /// 批量更新工作量数据
  Future<void> batchUpdateWorkloadData(Map<String, WorkloadAssignment> assignments) async {
    try {
      await _repository.batchUpdateWorkloadData(assignments);
      AppLogger.info('批量更新工作量数据成功: ${assignments.length}条', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('批量更新工作量数据失败: $e', tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }
}
