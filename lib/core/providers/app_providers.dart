import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';
import 'package:loadguard/services/performance_optimizer.dart';
import 'package:loadguard/services/app_security_service.dart';
import 'package:loadguard/models/task_model.dart';

// 临时注释掉，等代码生成完成后再启用
// part 'app_providers.g.dart';

/// 核心服务Providers - 确保单例模式和正确的生命周期管理

/// 任务服务Provider - 从Provider迁移到Riverpod
/// 修复：使用keepAlive确保服务不会被自动销毁
// @Riverpod(keepAlive: true)
final taskServiceProvider = Provider<TaskService>((ref) {
  final service = TaskService();

  // 修复：立即初始化服务
  service.initialize().catchError((e) {
    print('TaskService初始化失败: $e');
  });

  // 确保服务在Provider销毁时正确清理
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// ML Kit文本识别服务Provider
// @riverpod
final mlkitServiceProvider = Provider<MLKitTextRecognitionService>((ref) {
  final service = MLKitTextRecognitionService();

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// 性能优化服务Provider
// @riverpod
final performanceOptimizerProvider = Provider<PerformanceOptimizer>((ref) {
  final optimizer = PerformanceOptimizer();

  ref.onDispose(() {
    optimizer.dispose();
  });

  return optimizer;
});

/// 应用安全服务Provider
// @riverpod
final appSecurityServiceProvider = Provider<AppSecurityService>((ref) {
  return AppSecurityService();
});

/// 任务状态管理Provider
// @riverpod
class TaskNotifier extends AsyncNotifier<List<TaskModel>> {
  @override
  Future<List<TaskModel>> build() async {
    // 获取任务服务
    final taskService = ref.read(taskServiceProvider);

    // 加载任务列表
    await taskService.loadTasks();
    return taskService.tasks;
  }
  
  /// 创建新任务
  Future<void> createTask({
    required String template,
    required String productCode,
    required String batchNumber,
    required int quantity,
    List<String> participants = const [],
  }) async {
    state = const AsyncValue.loading();
    
    try {
      final taskService = ref.read(taskServiceProvider);
      
      await taskService.createTask(
        template: template,
        productCode: productCode,
        batchNumber: batchNumber,
        quantity: quantity,
        participants: participants,
      );
      
      // 重新加载任务列表
      state = AsyncValue.data([...taskService.tasks]);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  /// 更新任务
  Future<void> updateTask(TaskModel task) async {
    try {
      final taskService = ref.read(taskServiceProvider);
      await taskService.updateTask(task);
      
      // 更新状态
      state = AsyncValue.data([...taskService.tasks]);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  /// 删除任务
  Future<void> deleteTask(String taskId) async {
    try {
      final taskService = ref.read(taskServiceProvider);
      await taskService.deleteTask(taskId);
      
      // 更新状态
      state = AsyncValue.data([...taskService.tasks]);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  /// 获取特定任务
  TaskModel? getTask(String taskId) {
    return state.value?.cast<TaskModel?>().firstWhere(
      (task) => task?.id == taskId,
      orElse: () => null,
    );
  }
}

/// 🔄 当前任务Provider - 用于任务详情页面
@riverpod
class CurrentTaskNotifier extends _$CurrentTaskNotifier {
  @override
  TaskModel? build(String? taskId) {
    if (taskId == null) return null;
    
    // 从任务列表中获取当前任务
    final tasksAsync = ref.read(taskNotifierProvider);
    return tasksAsync.when(
      data: (tasks) => tasks.cast<TaskModel?>().firstWhere(
        (task) => task?.id == taskId,
        orElse: () => null,
      ),
      loading: () => null,
      error: (_, __) => null,
    );
  }
  
  /// 更新当前任务
  void updateCurrentTask(TaskModel task) {
    state = task;
    
    // 同时更新任务列表
    ref.read(taskNotifierProvider.notifier).updateTask(task);
  }
  
  /// 更新照片路径
  void updatePhotoPath(String photoId, String imagePath) {
    if (state == null) return;
    
    final updatedPhotos = state!.photos.map((photo) {
      if (photo.id == photoId) {
        return photo.copyWith(imagePath: imagePath);
      }
      return photo;
    }).toList();
    
    final updatedTask = state!.copyWith(photos: updatedPhotos);
    updateCurrentTask(updatedTask);
  }
}

/// 🎯 应用状态Provider - 全局应用状态管理
@riverpod
class AppStateNotifier extends _$AppStateNotifier {
  @override
  AppState build() {
    return const AppState(
      isLoading: false,
      isOnline: true,
      currentRoute: '/',
    );
  }
  
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }
  
  void setOnlineStatus(bool isOnline) {
    state = state.copyWith(isOnline: isOnline);
  }
  
  void setCurrentRoute(String route) {
    state = state.copyWith(currentRoute: route);
  }
}

/// 🎯 应用状态数据类
class AppState {
  final bool isLoading;
  final bool isOnline;
  final String currentRoute;
  
  const AppState({
    required this.isLoading,
    required this.isOnline,
    required this.currentRoute,
  });
  
  AppState copyWith({
    bool? isLoading,
    bool? isOnline,
    String? currentRoute,
  }) {
    return AppState(
      isLoading: isLoading ?? this.isLoading,
      isOnline: isOnline ?? this.isOnline,
      currentRoute: currentRoute ?? this.currentRoute,
    );
  }
}

/// 🔧 Provider桥接器 - 用于渐进式迁移
class ProviderBridge {
  static ProviderContainer? _container;
  
  static void initialize(ProviderContainer container) {
    _container = container;
  }
  
  static T read<T>(ProviderListenable<T> provider) {
    if (_container == null) {
      throw StateError('ProviderBridge not initialized');
    }
    return _container!.read(provider);
  }
  
  static void dispose() {
    _container?.dispose();
    _container = null;
  }
}

// TaskNotifier Provider定义
final taskNotifierProvider = AsyncNotifierProvider<TaskNotifier, List<TaskModel>>(() {
  return TaskNotifier();
});
