import 'dart:async';
import 'package:flutter/material.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🔄 生命周期管理Mixin - 适配新版本Flutter严格生命周期要求
/// 
/// 确保所有异步操作、Timer、StreamController等资源在Widget销毁时正确释放
mixin LifecycleMixin<T extends StatefulWidget> on State<T> {
  /// 活跃的Timer列表
  final List<Timer> _activeTimers = [];
  
  /// 活跃的StreamController列表
  final List<StreamController> _activeStreamControllers = [];
  
  /// 活跃的StreamSubscription列表
  final List<StreamSubscription> _activeStreamSubscriptions = [];
  
  /// Widget是否已经被销毁
  bool _isDisposed = false;
  
  /// 检查Widget是否仍然活跃
  bool get isActive => mounted && !_isDisposed;
  
  /// 🔧 安全的setState调用
  void safeSetState(VoidCallback fn) {
    if (isActive) {
      setState(fn);
    } else {
      AppLogger.warning('尝试在已销毁的Widget上调用setState: ${T.toString()}');
    }
  }
  
  /// 🕐 注册Timer，确保自动清理
  Timer registerTimer(Duration duration, VoidCallback callback) {
    late Timer timer;
    timer = Timer(duration, () {
      if (isActive) {
        callback();
      }
      _activeTimers.remove(timer);
    });
    _activeTimers.add(timer);
    return timer;
  }
  
  /// 🔄 注册周期性Timer
  Timer registerPeriodicTimer(Duration duration, void Function(Timer) callback) {
    late Timer timer;
    timer = Timer.periodic(duration, (t) {
      if (isActive) {
        callback(t);
      } else {
        t.cancel();
        _activeTimers.remove(timer);
      }
    });
    _activeTimers.add(timer);
    return timer;
  }
  
  /// 📡 注册StreamController
  StreamController<S> registerStreamController<S>({bool broadcast = false}) {
    final controller = broadcast 
        ? StreamController<S>.broadcast()
        : StreamController<S>();
    _activeStreamControllers.add(controller);
    return controller;
  }
  
  /// 📻 注册StreamSubscription
  StreamSubscription<S> registerStreamSubscription<S>(
    Stream<S> stream,
    void Function(S) onData, {
    Function? onError,
    void Function()? onDone,
    bool? cancelOnError,
  }) {
    final subscription = stream.listen(
      (data) {
        if (isActive) {
          onData(data);
        }
      },
      onError: onError,
      onDone: onDone,
      cancelOnError: cancelOnError,
    );
    _activeStreamSubscriptions.add(subscription);
    return subscription;
  }
  
  /// 🚀 安全的异步操作执行
  Future<R?> safeAsyncOperation<R>(Future<R> Function() operation) async {
    if (!isActive) {
      AppLogger.warning('尝试在已销毁的Widget上执行异步操作: ${T.toString()}');
      return null;
    }
    
    try {
      final result = await operation();
      
      if (!isActive) {
        AppLogger.warning('异步操作完成时Widget已销毁: ${T.toString()}');
        return null;
      }
      
      return result;
    } catch (e) {
      if (isActive) {
        AppLogger.error('异步操作失败: $e', tag: T.toString());
      }
      rethrow;
    }
  }
  
  /// 🧹 手动清理特定资源
  void cleanupTimer(Timer timer) {
    timer.cancel();
    _activeTimers.remove(timer);
  }
  
  void cleanupStreamController(StreamController controller) {
    if (!controller.isClosed) {
      controller.close();
    }
    _activeStreamControllers.remove(controller);
  }
  
  void cleanupStreamSubscription(StreamSubscription subscription) {
    subscription.cancel();
    _activeStreamSubscriptions.remove(subscription);
  }
  
  /// 🔄 生命周期钩子 - 子类可以重写
  @protected
  void onLifecycleInit() {
    // 子类可以重写此方法进行初始化
  }
  
  @protected
  void onLifecycleDispose() {
    // 子类可以重写此方法进行清理
  }
  
  @override
  void initState() {
    super.initState();
    onLifecycleInit();
    AppLogger.debug('Widget生命周期开始: ${T.toString()}');
  }
  
  @override
  void dispose() {
    _isDisposed = true;
    
    // 清理所有Timer
    for (final timer in _activeTimers) {
      timer.cancel();
    }
    _activeTimers.clear();
    
    // 清理所有StreamController
    for (final controller in _activeStreamControllers) {
      if (!controller.isClosed) {
        controller.close();
      }
    }
    _activeStreamControllers.clear();
    
    // 清理所有StreamSubscription
    for (final subscription in _activeStreamSubscriptions) {
      subscription.cancel();
    }
    _activeStreamSubscriptions.clear();
    
    // 调用子类清理钩子
    onLifecycleDispose();
    
    AppLogger.debug('Widget生命周期结束，资源已清理: ${T.toString()}');
    super.dispose();
  }
}
