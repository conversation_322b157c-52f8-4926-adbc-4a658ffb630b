class WorkerInfo {
  final String id;
  final String name;
  final String role;
  final String warehouse;
  final String group;

  const WorkerInfo({
    required this.id,
    required this.name,
    required this.role,
    required this.warehouse,
    required this.group,
  });
}

// 参与人员静态数据（共88人）
final List<WorkerInfo> allWorkers = [
  WorkerInfo(id: '1', name: '葛峰', role: '仓管', warehouse: '1号库', group: '1组'),
  WorkerInfo(id: '2', name: '栾晓伟', role: '叉车', warehouse: '1号库', group: '1组'),
  WorkerInfo(id: '3', name: '栾学林', role: '叉车', warehouse: '1号库', group: '1组'),
  WorkerInfo(id: '4', name: '林永佳', role: '仓管', warehouse: '1号库', group: '2组'),
  WorkerInfo(id: '5', name: '周泽恩', role: '叉车', warehouse: '1号库', group: '2组'),
  WorkerInfo(id: '6', name: '王占峰', role: '叉车', warehouse: '1号库', group: '2组'),
  WorkerInfo(id: '7', name: '赵朗', role: '仓管', warehouse: '1号库', group: '3组'),
  WorkerInfo(id: '8', name: '何文博', role: '叉车', warehouse: '1号库', group: '3组'),
  WorkerInfo(id: '9', name: '盛康伟', role: '叉车', warehouse: '1号库', group: '3组'),
  WorkerInfo(id: '10', name: '徐财刚', role: '叉车', warehouse: '1号库', group: '机动组'),
  WorkerInfo(id: '11', name: '杨少强', role: '仓管', warehouse: '1号库', group: '4组'),
  WorkerInfo(id: '12', name: '高中华', role: '叉车', warehouse: '1号库', group: '4组'),
  WorkerInfo(id: '13', name: '徐相君', role: '叉车', warehouse: '1号库', group: '4组'),
  WorkerInfo(id: '14', name: '杨佳伟', role: '仓管', warehouse: '1号库', group: '5组'),
  WorkerInfo(id: '15', name: '李铁君', role: '叉车', warehouse: '1号库', group: '5组'),
  WorkerInfo(id: '16', name: '姜岩', role: '叉车', warehouse: '1号库', group: '5组'),
  WorkerInfo(id: '17', name: '马铭泽', role: '仓管', warehouse: '1号库', group: '6组'),
  WorkerInfo(id: '18', name: '路学明', role: '叉车', warehouse: '1号库', group: '6组'),
  WorkerInfo(id: '19', name: '徐磊', role: '叉车', warehouse: '1号库', group: '6组'),
  WorkerInfo(id: '20', name: '崔洪磊', role: '仓管', warehouse: '1号库', group: '7组'),
  WorkerInfo(id: '21', name: '赵青林', role: '叉车', warehouse: '1号库', group: '7组'),
  WorkerInfo(id: '22', name: '韩春昌', role: '叉车', warehouse: '1号库', group: '7组'),
  WorkerInfo(id: '23', name: '李炎鹏', role: '主管', warehouse: '1号库', group: '管理'),
  WorkerInfo(id: '24', name: '季永超', role: '班长', warehouse: '1号库', group: '管理'),
  WorkerInfo(id: '25', name: '徐晓波', role: '班长', warehouse: '2号库', group: '管理'),
  WorkerInfo(id: '26', name: '陈泓宇', role: '仓管', warehouse: '2号库', group: '1组'),
  WorkerInfo(id: '27', name: '战世琳', role: '叉车', warehouse: '2号库', group: '1组'),
  WorkerInfo(id: '28', name: '姜进君', role: '叉车', warehouse: '2号库', group: '1组'),
  WorkerInfo(id: '29', name: '徐昊', role: '仓管', warehouse: '2号库', group: '2组'),
  WorkerInfo(id: '30', name: '路成强', role: '叉车', warehouse: '2号库', group: '2组'),
  WorkerInfo(id: '31', name: '宋春龙', role: '叉车', warehouse: '2号库', group: '2组'),
  WorkerInfo(id: '32', name: '王鹏亮', role: '仓管', warehouse: '2号库', group: '3组'),
  WorkerInfo(id: '33', name: '王殿波', role: '叉车', warehouse: '2号库', group: '3组'),
  WorkerInfo(id: '34', name: '郑瑞国', role: '叉车', warehouse: '2号库', group: '3组'),
  WorkerInfo(id: '35', name: '艳军', role: '仓管', warehouse: '2号库', group: '4组'),
  WorkerInfo(id: '36', name: '王方勇', role: '叉车', warehouse: '2号库', group: '4组'),
  WorkerInfo(id: '37', name: '高进强', role: '叉车', warehouse: '2号库', group: '4组'),
  WorkerInfo(id: '38', name: '赵芳静', role: '仓管', warehouse: '2号库', group: '5组'),
  WorkerInfo(id: '39', name: '殷守丽', role: '叉车', warehouse: '2号库', group: '5组'),
  WorkerInfo(id: '40', name: '林福源', role: '叉车', warehouse: '2号库', group: '5组'),
  WorkerInfo(id: '41', name: '吕有志', role: '仓管', warehouse: '2号库', group: '6组'),
  WorkerInfo(id: '42', name: '孙蓬波', role: '叉车', warehouse: '2号库', group: '6组'),
  WorkerInfo(id: '43', name: '崔树成', role: '叉车', warehouse: '2号库', group: '6组'),
  WorkerInfo(id: '44', name: '闫祥忠', role: '仓管', warehouse: '2号库', group: '7组'),
  WorkerInfo(id: '45', name: '张程旭', role: '叉车', warehouse: '2号库', group: '7组'),
  WorkerInfo(id: '46', name: '刘文俊', role: '叉车', warehouse: '2号库', group: '7组'),
  WorkerInfo(id: '47', name: '马千里', role: '仓管', warehouse: '3号库', group: '1组'),
  WorkerInfo(id: '48', name: '郑晓亮', role: '叉车', warehouse: '3号库', group: '1组'),
  WorkerInfo(id: '49', name: '郑丙龙', role: '叉车', warehouse: '3号库', group: '1组'),
  WorkerInfo(id: '50', name: '常珊珊', role: '仓管', warehouse: '3号库', group: '2组'),
  WorkerInfo(id: '51', name: '管春诚', role: '叉车', warehouse: '3号库', group: '2组'),
  WorkerInfo(id: '52', name: '乔曾明', role: '叉车', warehouse: '3号库', group: '2组'),
  WorkerInfo(id: '53', name: '高洋洋', role: '仓管', warehouse: '3号库', group: '3组'),
  WorkerInfo(id: '54', name: '赵许卫', role: '叉车', warehouse: '3号库', group: '3组'),
  WorkerInfo(id: '55', name: '朱世友', role: '叉车', warehouse: '3号库', group: '3组'),
  WorkerInfo(id: '56', name: '尹朋渤', role: '仓管', warehouse: '3号库', group: '4组'),
  WorkerInfo(id: '57', name: '杨文华', role: '叉车', warehouse: '3号库', group: '4组'),
  WorkerInfo(id: '58', name: '刘海龙', role: '叉车', warehouse: '3号库', group: '4组'),
  WorkerInfo(id: '59', name: '宫树兵', role: '班长', warehouse: '3号库', group: '管理'),
  WorkerInfo(id: '60', name: '于鑫雨', role: '班长', warehouse: '4号库', group: '管理'),
  WorkerInfo(id: '61', name: '王培宇', role: '仓管', warehouse: '4号库', group: '1组'),
  WorkerInfo(id: '62', name: '冯友模', role: '叉车', warehouse: '4号库', group: '1组'),
  WorkerInfo(id: '63', name: '梁永正', role: '叉车', warehouse: '4号库', group: '1组'),
  WorkerInfo(id: '64', name: '韩静', role: '仓管', warehouse: '4号库', group: '2组'),
  WorkerInfo(id: '65', name: '李乐豪', role: '叉车', warehouse: '4号库', group: '2组'),
  WorkerInfo(id: '66', name: '王国兴', role: '叉车', warehouse: '4号库', group: '2组'),
  WorkerInfo(id: '67', name: '韩明宝', role: '仓管', warehouse: '4号库', group: '3组'),
  WorkerInfo(id: '68', name: '于辉', role: '叉车', warehouse: '4号库', group: '3组'),
  WorkerInfo(id: '69', name: '张云', role: '叉车', warehouse: '4号库', group: '3组'),
  WorkerInfo(id: '70', name: '隋静', role: '仓管', warehouse: '4号库', group: '4组'),
  WorkerInfo(id: '71', name: '王日刚', role: '叉车', warehouse: '4号库', group: '4组'),
  WorkerInfo(id: '72', name: '王连臣', role: '叉车', warehouse: '4号库', group: '4组'),
  WorkerInfo(id: '73', name: '宋凯', role: '仓管', warehouse: '4号库', group: '5组'),
  WorkerInfo(id: '74', name: '牟富康', role: '叉车', warehouse: '4号库', group: '5组'),
  WorkerInfo(id: '75', name: '郭广圣', role: '仓管', warehouse: '4号库', group: '5组'),
  WorkerInfo(id: '76', name: '王博', role: '班长', warehouse: 'ABS库', group: '管理'),
  WorkerInfo(id: '77', name: '单炳坤', role: '无', warehouse: 'ABS库', group: ''),
  WorkerInfo(id: '78', name: '王倩', role: '无', warehouse: 'ABS库', group: ''),
  WorkerInfo(id: '79', name: '张佳怡', role: '无', warehouse: 'ABS库', group: ''),
  WorkerInfo(id: '80', name: '闫洪梅', role: '无', warehouse: 'ABS库', group: ''),
  WorkerInfo(id: '81', name: '房露', role: '无', warehouse: 'ABS库', group: ''),
  WorkerInfo(id: '82', name: '蔡创应', role: '无', warehouse: 'ABS库', group: ''),
  WorkerInfo(id: '83', name: '刘永波', role: '无', warehouse: 'ABS库', group: ''),
  WorkerInfo(id: '84', name: '郑绪建', role: '无', warehouse: 'ABS库', group: ''),
  WorkerInfo(id: '85', name: '曲文龙', role: '无', warehouse: 'ABS库', group: ''),
  WorkerInfo(id: '86', name: '吕芝超', role: '无', warehouse: 'ABS库', group: ''),
  WorkerInfo(id: '87', name: '邹举', role: '无', warehouse: 'ABS库', group: ''),
  WorkerInfo(id: '88', name: '李明华', role: '叉车', warehouse: '4号库', group: '5组'),
];
