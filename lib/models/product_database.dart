import 'package:flutter/material.dart';
import 'package:loadguard/services/logging_service.dart';

/// 🎯 产品牌号数据库
class ProductDatabase {
  // 常用牌号预设数据
  static const Map<String, ProductInfo> presetProducts = {
    // LLD系列 - 线性低密度聚乙烯
    'LLD-7042': ProductInfo(
      code: 'LLD-7042',
      name: '线性低密度聚乙烯',
      category: 'LLDPE',
      commonBatchPrefixes: ['250615', '250618', '250620'],
      priority: 10, // 最高优先级
    ),
    'LLD-7042-GD': ProductInfo(
      code: 'LLD-7042-GD',
      name: '线性低密度聚乙烯(过渡料)',
      category: 'LLDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 9,
    ),
    'LLD-7047': ProductInfo(
      code: 'LLD-7047',
      name: '线性低密度聚乙烯',
      category: 'LLDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 8,
    ),
    'LLD-7050': ProductInfo(
      code: 'LLD-7050',
      name: '线性低密度聚乙烯',
      category: 'LLDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 8,
    ),

    // HD系列 - 高密度聚乙烯
    'HD-5000S': ProductInfo(
      code: 'HD-5000S',
      name: '高密度聚乙烯',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 7,
    ),
    'HD-5000S-FP': ProductInfo(
      code: 'HD-5000S-FP',
      name: '高密度聚乙烯(副牌)',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 7,
    ),
    'HD-5000S-GD': ProductInfo(
      code: 'HD-5000S-GD',
      name: '高密度聚乙烯(过渡料)',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 7,
    ),
    // 🚀 新增：HDPE开工料系列
    'HDPE-开工料': ProductInfo(
      code: 'HDPE-开工料',
      name: '高密度聚乙烯开工料',
      category: 'HDPE',
      commonBatchPrefixes: ['250612', '250614', '250615'], // 基于图片中的250612
      priority: 8, // 高优先级，便于识别
    ),
    'HD-6098-GD': ProductInfo(
      code: 'HD-6098-GD',
      name: '高密度聚乙烯(过渡料)',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 6,
    ),
    'HD-6081H': ProductInfo(
      code: 'HD-6081H',
      name: '高密度聚乙烯',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 6,
    ),
    'HD-6081-GD': ProductInfo(
      code: 'HD-6081-GD',
      name: '高密度聚乙烯(过渡料)',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 6,
    ),

    // mPE系列 - 茂金属聚乙烯
    'mPE-1018': ProductInfo(
      code: 'mPE-1018',
      name: '茂金属聚乙烯',
      category: 'mPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 5,
    ),
    'mPE-1018-GD': ProductInfo(
      code: 'mPE-1018-GD',
      name: '茂金属聚乙烯(过渡料)',
      category: 'mPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 5,
    ),

    // 2500系列
    'PP-2500HY': ProductInfo(
      code: 'PP-2500HY',
      name: '聚丙烯',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618', '250620'],
      priority: 7,
    ),
    'PP-2500HY-GD': ProductInfo(
      code: 'PP-2500HY-GD',
      name: '聚丙烯(过渡料)',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618', '250620'],
      priority: 7,
    ),
    'PP-2500HW': ProductInfo(
      code: 'PP-2500HW',
      name: '聚丙烯',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618', '250620'],
      priority: 7,
    ),
    'PP-2500HW-GD': ProductInfo(
      code: 'PP-2500HW-GD',
      name: '聚丙烯(过渡料)',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618', '250620'],
      priority: 7,
    ),

    // K系列
    'PP-K1015': ProductInfo(
      code: 'PP-K1015',
      name: '聚丙烯',
      category: 'PP',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 6,
    ),
    'PP-K7930': ProductInfo(
      code: 'PP-K7930',
      name: '聚丙烯',
      category: 'PP',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 6,
    ),
    'PP-K7930-GD': ProductInfo(
      code: 'PP-K7930-GD',
      name: '聚丙烯(过渡料)',
      category: 'PP',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 6,
    ),
    'PP-K8003': ProductInfo(
      code: 'PP-K8003',
      name: '聚丙烯',
      category: 'PP',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 6,
    ),
    'PP-K8003-GD': ProductInfo(
      code: 'PP-K8003-GD',
      name: '聚丙烯(过渡料)',
      category: 'PP',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 6,
    ),
    'PP-K8009': ProductInfo(
      code: 'PP-K8009',
      name: '聚丙烯',
      category: 'PP',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 6,
    ),
    'PP-K8009-GD': ProductInfo(
      code: 'PP-K8009-GD',
      name: '聚丙烯(过渡料)',
      category: 'PP',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 6,
    ),

    // T系列
    'PP-T30F': ProductInfo(
      code: 'PP-T30F',
      name: '聚丙烯纤维料',
      category: 'PP',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 5,
    ),
    'PP-T30S': ProductInfo(
      code: 'PP-T30S',
      name: '聚丙烯纺丝料',
      category: 'PP',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 5,
    ),
    'PP-T30S-GD': ProductInfo(
      code: 'PP-T30S-GD',
      name: '聚丙烯纺丝料(过渡料)',
      category: 'PP',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 5,
    ),

    // A系列 - SAN树脂
    'A-2020': ProductInfo(
      code: 'A-2020',
      name: 'SAN树脂',
      category: 'SAN',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 4,
    ),
    'A-2020-GD': ProductInfo(
      code: 'A-2020-GD',
      name: 'SAN树脂(过渡料)',
      category: 'SAN',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 4,
    ),
    'A-2021': ProductInfo(
      code: 'A-2021',
      name: 'SAN树脂',
      category: 'SAN',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 4,
    ),
    'A-2021-GD': ProductInfo(
      code: 'A-2021-GD',
      name: 'SAN树脂(过渡料)',
      category: 'SAN',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 4,
    ),

    // S系列
    'S-0130': ProductInfo(
      code: 'S-0130',
      name: '苯乙烯',
      category: 'PS',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 3,
    ),
    'S-0130-FP': ProductInfo(
      code: 'S-0130-FP',
      name: '苯乙烯(副牌)',
      category: 'PS',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 3,
    ),
  };

  /// 🔍 智能牌号匹配 - 结合预设和模糊识别
  static List<ProductMatch> intelligentMatch(String recognizedText) {
    final matches = <ProductMatch>[];
    final cleanText =
        recognizedText.toUpperCase().replaceAll(RegExp(r'[^A-Z0-9-]'), '');

    // 1. 精确匹配预设牌号
    for (final product in presetProducts.values) {
      final similarity = _calculateSimilarity(cleanText, product.code);
      if (similarity >= 0.8) {
        // 80%以上相似度
        matches.add(ProductMatch(
          product: product,
          similarity: similarity,
          confidence: similarity * 100,
          matchType: similarity >= 0.95 ? MatchType.exact : MatchType.preset,
          matchedText: cleanText,
        ));
      }
    }

    // 2. 如果没有高置信度匹配，进行模糊匹配
    if (matches.isEmpty || matches.first.confidence < 90) {
      final fuzzyMatches = _fuzzyMatch(cleanText);
      matches.addAll(fuzzyMatches);
    }

    // 3. 按置信度和优先级排序
    matches.sort((a, b) {
      final confidenceDiff = b.confidence.compareTo(a.confidence);
      if (confidenceDiff != 0) return confidenceDiff;
      return (b.product?.priority ?? 0).compareTo(a.product?.priority ?? 0);
    });

    return matches.take(5).toList(); // 返回前5个最佳匹配
  }

  /// 📊 计算字符串相似度
  static double _calculateSimilarity(String a, String b) {
    if (a == b) return 1.0;
    if (a.isEmpty || b.isEmpty) return 0.0;

    final longer = a.length > b.length ? a : b;
    final shorter = a.length > b.length ? b : a;

    if (longer.isEmpty) return 1.0;

    final editDistance = _levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /// 📏 计算编辑距离
  static int _levenshteinDistance(String a, String b) {
    final matrix = List.generate(
      a.length + 1,
      (i) => List.generate(b.length + 1, (j) => 0),
    );

    for (int i = 0; i <= a.length; i++) {
      matrix[i][0] = i;
    }
    for (int j = 0; j <= b.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= a.length; i++) {
      for (int j = 1; j <= b.length; j++) {
        final cost = a[i - 1] == b[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1, // 删除
          matrix[i][j - 1] + 1, // 插入
          matrix[i - 1][j - 1] + cost, // 替换
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[a.length][b.length];
  }

  /// 🔎 模糊匹配
  static List<ProductMatch> _fuzzyMatch(String text) {
    final matches = <ProductMatch>[];

    // 增强的模糊匹配逻辑
    for (final product in presetProducts.values) {
      final partialMatches = <double>[];

      // 1. 检查完整产品代码的包含关系
      if (text.contains(product.code)) {
        partialMatches.add(0.95); // 完全包含，高置信度
      }

      // 2. 检查产品代码的主要部分匹配（去掉后缀）
      final mainCode = product.code.split('-')[0]; // 例如：HD-5000S-FP -> HD
      if (text.contains(mainCode)) {
        partialMatches.add(0.7);
      }

      // 3. 检查数字部分匹配
      final numbers =
          RegExp(r'\d+').allMatches(text).map((m) => m.group(0)).toList();
      final productNumbers = RegExp(r'\d+')
          .allMatches(product.code)
          .map((m) => m.group(0))
          .toList();

      for (final num in numbers) {
        for (final prodNum in productNumbers) {
          if (num == prodNum) {
            partialMatches.add(0.6);
          } else if (num!.length >= 3 && prodNum!.length >= 3) {
            // 数字部分相似度检查
            final similarity = _calculateSimilarity(num, prodNum);
            if (similarity >= 0.8) {
              partialMatches.add(similarity * 0.5);
            }
          }
        }
      }

      // 4. 检查字母前缀匹配
      final letters = RegExp(r'[A-Z]+')
          .allMatches(text.toUpperCase())
          .map((m) => m.group(0))
          .toList();
      final productLetters = RegExp(r'[A-Z]+')
          .allMatches(product.code.toUpperCase())
          .map((m) => m.group(0))
          .toList();

      for (final letter in letters) {
        for (final prodLetter in productLetters) {
          if (letter == prodLetter) {
            partialMatches.add(0.5);
          }
        }
      }

      // 5. 模糊字符串匹配（编辑距离）
      final similarity = _calculateSimilarity(text, product.code);
      if (similarity >= 0.6) {
        partialMatches.add(similarity * 0.8);
      }

      if (partialMatches.isNotEmpty) {
        // 使用最高匹配分数，并增加权重
        final maxConfidence = partialMatches.reduce((a, b) => a > b ? a : b);
        final avgConfidence =
            partialMatches.reduce((a, b) => a + b) / partialMatches.length;
        final finalConfidence =
            (maxConfidence * 0.7 + avgConfidence * 0.3) * 100;

        if (finalConfidence >= 40) {
          // 降低阈值，允许更多潜在匹配
          matches.add(ProductMatch(
            product: product,
            similarity: maxConfidence,
            confidence: finalConfidence,
            matchType: MatchType.fuzzy,
            matchedText: text,
          ));
        }
      }
    }

    return matches;
  }

  /// 🔥 获取热门/常用产品列表
  static List<ProductInfo> getPopularProducts({int limit = 0}) {
    // 返回高优先级且常用的产品，包括HDPE开工料
    final products = presetProducts.values
        .where((product) => product.priority >= 7)
        .toList()
      ..sort((a, b) => b.priority.compareTo(a.priority));

    // 如果指定了限制数量，返回前N个
    if (limit > 0) {
      return products.take(limit).toList();
    }

    return products;
  }

  /// 🔥 按类别获取热门/常用产品列表
  static List<ProductInfo> getPopularProductsByCategory(String category,
      {int limit = 0}) {
    final products = presetProducts.values.where((product) {
      // 精确匹配类别名称
      return product.category == category;
    }).toList()
      ..sort((a, b) => b.priority.compareTo(a.priority));
    if (limit > 0) {
      return products.take(limit).toList();
    }
    return products;
  }

  /// 🎨 获取类别颜色
  static Color getCategoryColor(String category) {
    switch (category) {
      case 'LLDPE':
        return const Color(0xFF4CAF50); // 绿色
      case 'HDPE':
        return const Color(0xFF2196F3); // 蓝色
      case 'PP':
        return const Color(0xFFFF9800); // 橙色
      case 'SAN':
        return const Color(0xFF9E9E9E); // 灰色
      case 'PS':
        return const Color(0xFF9C27B0); // 紫色
      case 'mPE':
        return const Color(0xFF00BCD4); // 青色
      default:
        return const Color(0xFF757575); // 默认灰色
    }
  }

  /// 📋 获取所有产品列表（按类别和优先级排序）
  static List<ProductInfo> getAllProducts() {
    // 定义类别顺序
    final categoryOrder = ['LLDPE', 'HDPE', 'mPE', 'PP', 'SAN', 'PS'];

    return presetProducts.values.toList()
      ..sort((a, b) {
        // 首先按类别排序
        final aCategoryIndex = categoryOrder.indexOf(a.category);
        final bCategoryIndex = categoryOrder.indexOf(b.category);

        // 如果类别不同，按类别顺序排序
        if (aCategoryIndex != bCategoryIndex) {
          return aCategoryIndex.compareTo(bCategoryIndex);
        }

        // 如果类别相同，按优先级排序（高优先级在前）
        return b.priority.compareTo(a.priority);
      });
  }

  /// 🔄 获取相关牌号建议
  static List<ProductInfo> getRelatedProducts(String productCode) {
    final product = presetProducts[productCode];
    if (product == null) return [];

    return presetProducts.values
        .where((p) => p.category == product.category && p.code != productCode)
        .toList()
      ..sort((a, b) => b.priority.compareTo(a.priority));
  }

  /// ➕ 动态添加新牌号
  static void addCustomProduct(ProductInfo product) {
    // 这里可以保存到本地数据库或服务器
    // 实现动态学习功能
    Log.i('添加新牌号: ${product.code}', tag: 'ProductDB');
  }

  /// 🔍 **检查产品是否在预设数据库中**
  static bool hasProduct(String productCode) {
    return presetProducts.containsKey(productCode.toUpperCase());
  }

  /// 通过OCR文本查找最佳产品匹配
  ProductMatch? findProductByOcr(String ocrText) {
    if (ocrText.isEmpty) return null;

    final List<ProductMatch> matches = [];
    final cleanOcr = ocrText.replaceAll(RegExp(r'[\s\W]'), '').toUpperCase();

    // 1. 优先从OCR文本中寻找精确的产品代码
    for (final product in presetProducts.values) {
      final cleanCode =
          product.code.replaceAll(RegExp(r'[\s\W]'), '').toUpperCase();
      if (cleanOcr.contains(cleanCode)) {
        matches.add(ProductMatch(
            product: product,
            similarity: 1.0,
            confidence: 100,
            matchType: MatchType.ocrContainsCode,
            matchedText: cleanCode));
      }
    }

    // 2. 如果找不到，进行模糊匹配
    if (matches.isEmpty) {
      for (final product in presetProducts.values) {
        final similarity = _calculateSimilarity(cleanOcr, product.code);
        if (similarity > 0.8) {
          matches.add(ProductMatch(
              product: product,
              similarity: similarity,
              confidence: similarity * 100,
              matchType: MatchType.fuzzy,
              matchedText: cleanOcr));
        }
      }
    }

    if (matches.isEmpty) {
      Log.w('在产品数据库中找不到匹配项: "$ocrText"', tag: 'ProductDB');
      return null;
    }

    // 排序和过滤
    matches.sort((a, b) {
      final typeDiff = a.matchType.index.compareTo(b.matchType.index);
      if (typeDiff != 0) return typeDiff;

      final priorityDiff =
          (b.product?.priority ?? 0).compareTo(a.product?.priority ?? 0);
      if (priorityDiff != 0) return priorityDiff;

      return b.similarity.compareTo(a.similarity);
    });

    final bestMatch = matches.first;
    Log.i(
        '✅ 产品数据库最佳匹配: ${bestMatch.product?.name ?? "未知产品"} (相似度: ${bestMatch.similarity})',
        tag: 'ProductDB');

    return bestMatch;
  }

  /// 检查特定产品代码是否存在
  bool isKnownProductCode(String productCode) {
    return presetProducts.containsKey(productCode.toUpperCase());
  }
}

/// 🏷️ 产品信息模型
class ProductInfo {
  final String code;
  final String name;
  final String category;
  final List<String> commonBatchPrefixes;
  final int priority; // 1-10，10为最高优先级

  const ProductInfo({
    required this.code,
    required this.name,
    required this.category,
    this.commonBatchPrefixes = const [],
    this.priority = 0,
  });

  /// 是否为火热产品（优先级9及以上）
  bool get isHot => priority >= 9;

  /// 🏷️ 获取带类别前缀的显示代码
  /// 例如：K7930 → PP-K7930, A-2020 → SAN-A-2020
  String get displayCode {
    // 如果代码已经包含前缀格式，直接返回
    if (code.contains('LLD-') ||
        code.contains('HD-') ||
        code.contains('mPE-') ||
        code.contains('PP-') ||
        code.contains('SAN-') ||
        code.contains('PS-') ||
        code.contains('开工料')) {
      return code;
    }

    // 对于没有前缀的产品代码，根据类别添加前缀
    switch (category) {
      case 'LLDPE':
      case 'HDPE':
      case 'mPE':
      case 'PP':
      case 'SAN':
      case 'PS':
        return '$category-$code';
      default:
        return code; // 其他情况直接返回原代码
    }
  }

  /// 🎨 获取类别显示颜色（用于UI显示）
  String get categoryColor {
    switch (category) {
      case 'LLDPE':
        return '#4CAF50'; // 绿色
      case 'HDPE':
        return '#2196F3'; // 蓝色
      case 'PP':
        return '#FF9800'; // 橙色
      case 'SAN':
        return '#9E9E9E'; // 灰色 - 恢复SAN类别
      case 'PS':
        return '#9C27B0'; // 紫色
      case 'mPE':
        return '#00BCD4'; // 青色
      default:
        return '#757575'; // 默认灰色
    }
  }
}

/// 🎯 产品匹配结果
class ProductMatch {
  final ProductInfo? product;
  final double similarity; // 相似度 (0.0 - 1.0)
  final MatchType matchType;
  final double confidence; // "伪"置信度
  final String matchedText; // 匹配到的文本

  const ProductMatch({
    this.product,
    this.similarity = 0.0,
    this.matchType = MatchType.fuzzy,
    this.confidence = 0.0,
    this.matchedText = '',
  });
}

/// 📊 匹配类型
enum MatchType {
  exact, // 精确匹配
  preset, // 预设匹配
  fuzzy, // 模糊匹配
  manual, // 手动输入
  exactCode,
  ocrContainsCode,
}
