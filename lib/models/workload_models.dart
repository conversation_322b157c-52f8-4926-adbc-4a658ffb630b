import 'package:freezed_annotation/freezed_annotation.dart';

// 注释掉生成的文件引用，避免编译错误
// part 'workload_models.freezed.dart';
// part 'workload_models.g.dart';

/// 工作量统计数据模型
class WorkloadStatistics {
  final String workerId;
  final String workerName;
  final String role;
  final String warehouse;
  final String group;
  final double assignedTonnage;
  final double completedTonnage;
  final int totalTasks;
  final int completedTasks;
  final double efficiency;
  final List<WorkloadDetail> taskDetails;
  final DateTime lastUpdated;

  const WorkloadStatistics({
    required this.workerId,
    required this.workerName,
    required this.role,
    required this.warehouse,
    required this.group,
    required this.assignedTonnage,
    required this.completedTonnage,
    required this.totalTasks,
    required this.completedTasks,
    required this.efficiency,
    required this.taskDetails,
    required this.lastUpdated,
  });

  factory WorkloadStatistics.fromJson(Map<String, dynamic> json) {
    return WorkloadStatistics(
      workerId: json['workerId'] ?? '',
      workerName: json['workerName'] ?? '',
      role: json['role'] ?? '',
      warehouse: json['warehouse'] ?? '',
      group: json['group'] ?? '',
      assignedTonnage: (json['assignedTonnage'] ?? 0.0).toDouble(),
      completedTonnage: (json['completedTonnage'] ?? 0.0).toDouble(),
      totalTasks: json['totalTasks'] ?? 0,
      completedTasks: json['completedTasks'] ?? 0,
      efficiency: (json['efficiency'] ?? 0.0).toDouble(),
      taskDetails: (json['taskDetails'] as List<dynamic>?)
          ?.map((item) => WorkloadDetail.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      lastUpdated: DateTime.parse(json['lastUpdated'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'workerId': workerId,
      'workerName': workerName,
      'role': role,
      'warehouse': warehouse,
      'group': group,
      'assignedTonnage': assignedTonnage,
      'completedTonnage': completedTonnage,
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'efficiency': efficiency,
      'taskDetails': taskDetails.map((detail) => detail.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

/// 工作量明细数据模型
class WorkloadDetail {
  final String taskId;
  final String productCode;
  final String batchNumber;
  final int quantity;
  final double allocatedTonnage;
  final bool isCompleted;
  final DateTime createTime;
  final DateTime? completedAt;
  final String template;

  const WorkloadDetail({
    required this.taskId,
    required this.productCode,
    required this.batchNumber,
    required this.quantity,
    required this.allocatedTonnage,
    required this.isCompleted,
    required this.createTime,
    this.completedAt,
    required this.template,
  });

  factory WorkloadDetail.fromJson(Map<String, dynamic> json) {
    return WorkloadDetail(
      taskId: json['taskId'] ?? '',
      productCode: json['productCode'] ?? '',
      batchNumber: json['batchNumber'] ?? '',
      quantity: json['quantity'] ?? 0,
      allocatedTonnage: (json['allocatedTonnage'] ?? 0.0).toDouble(),
      isCompleted: json['isCompleted'] ?? false,
      createTime: DateTime.parse(json['createTime'] ?? DateTime.now().toIso8601String()),
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      template: json['template'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'taskId': taskId,
      'productCode': productCode,
      'batchNumber': batchNumber,
      'quantity': quantity,
      'allocatedTonnage': allocatedTonnage,
      'isCompleted': isCompleted,
      'createTime': createTime.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'template': template,
    };
  }
}

/// 工作量概览数据模型
class WorkloadOverview {
  final int totalTasks;
  final int completedTasks;
  final double totalTonnage;
  final double completedTonnage;
  final int activeWorkers;
  final double averageEfficiency;
  final double completionRate;

  const WorkloadOverview({
    required this.totalTasks,
    required this.completedTasks,
    required this.totalTonnage,
    required this.completedTonnage,
    required this.activeWorkers,
    required this.averageEfficiency,
    required this.completionRate,
  });

  factory WorkloadOverview.fromJson(Map<String, dynamic> json) {
    return WorkloadOverview(
      totalTasks: json['totalTasks'] ?? 0,
      completedTasks: json['completedTasks'] ?? 0,
      totalTonnage: (json['totalTonnage'] ?? 0.0).toDouble(),
      completedTonnage: (json['completedTonnage'] ?? 0.0).toDouble(),
      activeWorkers: json['activeWorkers'] ?? 0,
      averageEfficiency: (json['averageEfficiency'] ?? 0.0).toDouble(),
      completionRate: (json['completionRate'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'totalTonnage': totalTonnage,
      'completedTonnage': completedTonnage,
      'activeWorkers': activeWorkers,
      'averageEfficiency': averageEfficiency,
      'completionRate': completionRate,
    };
  }

  Map<String, dynamic> toMap() => toJson();
}

/// 工人配置数据模型
class WorkerConfig {
  final String id;
  final String name;
  final String role;
  final String warehouse;
  final String group;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic> metadata;

  const WorkerConfig({
    required this.id,
    required this.name,
    required this.role,
    required this.warehouse,
    required this.group,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
    this.metadata = const {},
  });

  factory WorkerConfig.fromJson(Map<String, dynamic> json) {
    return WorkerConfig(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      role: json['role'] ?? '',
      warehouse: json['warehouse'] ?? '',
      group: json['group'] ?? '',
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'role': role,
      'warehouse': warehouse,
      'group': group,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// 仓库配置数据模型
class WarehouseConfig {
  final String id;
  final String name;
  final String description;
  final bool isActive;
  final List<String> supportedTemplates;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic> metadata;

  const WarehouseConfig({
    required this.id,
    required this.name,
    required this.description,
    required this.isActive,
    required this.supportedTemplates,
    required this.createdAt,
    this.updatedAt,
    this.metadata = const {},
  });

  factory WarehouseConfig.fromJson(Map<String, dynamic> json) {
    return WarehouseConfig(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      isActive: json['isActive'] ?? true,
      supportedTemplates: List<String>.from(json['supportedTemplates'] ?? []),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'isActive': isActive,
      'supportedTemplates': supportedTemplates,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// 小组配置数据模型
class GroupConfig {
  final String id;
  final String name;
  final String warehouse;
  final bool isActive;
  final List<String> memberIds;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic> metadata;

  const GroupConfig({
    required this.id,
    required this.name,
    required this.warehouse,
    required this.isActive,
    required this.memberIds,
    required this.createdAt,
    this.updatedAt,
    this.metadata = const {},
  });

  factory GroupConfig.fromJson(Map<String, dynamic> json) {
    return GroupConfig(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      warehouse: json['warehouse'] ?? '',
      isActive: json['isActive'] ?? true,
      memberIds: List<String>.from(json['memberIds'] ?? []),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'warehouse': warehouse,
      'isActive': isActive,
      'memberIds': memberIds,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }
}

/// 模板配置数据模型
class TemplateConfig {
  final String id;
  final String name;
  final String description;
  final bool isActive;
  final List<PhotoGroupConfig> photoGroups;
  final List<PhotoConfig> photoConfigs;
  final Map<String, dynamic> settings;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const TemplateConfig({
    required this.id,
    required this.name,
    required this.description,
    required this.isActive,
    required this.photoGroups,
    required this.photoConfigs,
    this.settings = const {},
    required this.createdAt,
    this.updatedAt,
  });

  factory TemplateConfig.fromJson(Map<String, dynamic> json) {
    return TemplateConfig(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      isActive: json['isActive'] ?? true,
      photoGroups: (json['photoGroups'] as List<dynamic>?)
          ?.map((item) => PhotoGroupConfig.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      photoConfigs: (json['photoConfigs'] as List<dynamic>?)
          ?.map((item) => PhotoConfig.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      settings: Map<String, dynamic>.from(json['settings'] ?? {}),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'isActive': isActive,
      'photoGroups': photoGroups.map((group) => group.toJson()).toList(),
      'photoConfigs': photoConfigs.map((config) => config.toJson()).toList(),
      'settings': settings,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
}

/// 照片组配置数据模型
class PhotoGroupConfig {
  final String id;
  final String name;
  final String description;
  final bool isRequired;
  final List<String> photoIds;
  final Map<String, dynamic> metadata;

  const PhotoGroupConfig({
    required this.id,
    required this.name,
    required this.description,
    required this.isRequired,
    required this.photoIds,
    this.metadata = const {},
  });

  factory PhotoGroupConfig.fromJson(Map<String, dynamic> json) {
    return PhotoGroupConfig(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      isRequired: json['isRequired'] ?? false,
      photoIds: List<String>.from(json['photoIds'] ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'isRequired': isRequired,
      'photoIds': photoIds,
      'metadata': metadata,
    };
  }
}

/// 照片配置数据模型
class PhotoConfig {
  final String id;
  final String label;
  final String description;
  final bool isRequired;
  final bool needRecognition;
  final String groupId;
  final Map<String, dynamic> recognitionSettings;
  final Map<String, dynamic> metadata;

  const PhotoConfig({
    required this.id,
    required this.label,
    required this.description,
    required this.isRequired,
    required this.needRecognition,
    required this.groupId,
    this.recognitionSettings = const {},
    this.metadata = const {},
  });

  factory PhotoConfig.fromJson(Map<String, dynamic> json) {
    return PhotoConfig(
      id: json['id'] ?? '',
      label: json['label'] ?? '',
      description: json['description'] ?? '',
      isRequired: json['isRequired'] ?? false,
      needRecognition: json['needRecognition'] ?? false,
      groupId: json['groupId'] ?? '',
      recognitionSettings: Map<String, dynamic>.from(json['recognitionSettings'] ?? {}),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'label': label,
      'description': description,
      'isRequired': isRequired,
      'needRecognition': needRecognition,
      'groupId': groupId,
      'recognitionSettings': recognitionSettings,
      'metadata': metadata,
    };
  }
}

/// 工作量分配记录（兼容现有代码）
class WorkloadRecord {
  final String workerId;
  final String workerName;
  final String role;
  final String warehouse;
  final String group;
  final double allocatedTonnage;
  final DateTime assignedAt;
  final bool isCompleted;
  final DateTime? completedAt;

  const WorkloadRecord({
    required this.workerId,
    required this.workerName,
    required this.role,
    required this.warehouse,
    required this.group,
    required this.allocatedTonnage,
    required this.assignedAt,
    required this.isCompleted,
    this.completedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'workerId': workerId,
      'workerName': workerName,
      'role': role,
      'warehouse': warehouse,
      'group': group,
      'allocatedTonnage': allocatedTonnage,
      'assignedAt': assignedAt.toIso8601String(),
      'isCompleted': isCompleted,
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  factory WorkloadRecord.fromMap(Map<String, dynamic> map) {
    return WorkloadRecord(
      workerId: map['workerId'] ?? '',
      workerName: map['workerName'] ?? '',
      role: map['role'] ?? '',
      warehouse: map['warehouse'] ?? '',
      group: map['group'] ?? '',
      allocatedTonnage: (map['allocatedTonnage'] ?? 0.0).toDouble(),
      assignedAt: DateTime.parse(map['assignedAt'] ?? DateTime.now().toIso8601String()),
      isCompleted: map['isCompleted'] ?? false,
      completedAt: map['completedAt'] != null 
          ? DateTime.parse(map['completedAt']) 
          : null,
    );
  }
}

/// 工作量分配（兼容现有代码）
class WorkloadAssignment {
  final List<WorkloadRecord> records;
  final double totalTonnage;
  final int palletCount;
  final DateTime assignedAt;
  final String assignedBy;

  const WorkloadAssignment({
    required this.records,
    required this.totalTonnage,
    required this.palletCount,
    required this.assignedAt,
    required this.assignedBy,
  });

  Map<String, dynamic> toMap() {
    return {
      'records': records.map((r) => r.toMap()).toList(),
      'totalTonnage': totalTonnage,
      'palletCount': palletCount,
      'assignedAt': assignedAt.toIso8601String(),
      'assignedBy': assignedBy,
    };
  }

  factory WorkloadAssignment.fromMap(Map<String, dynamic> map) {
    return WorkloadAssignment(
      records: (map['records'] as List<dynamic>?)
          ?.map((r) => WorkloadRecord.fromMap(r as Map<String, dynamic>))
          .toList() ?? [],
      totalTonnage: (map['totalTonnage'] ?? 0.0).toDouble(),
      palletCount: map['palletCount'] ?? 0,
      assignedAt: DateTime.parse(map['assignedAt'] ?? DateTime.now().toIso8601String()),
      assignedBy: map['assignedBy'] ?? '',
    );
  }
}
