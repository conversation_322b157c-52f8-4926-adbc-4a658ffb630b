import 'dart:async';
import '../models/workload_models.dart';
import '../models/task_model.dart';
import '../services/hive_storage_service.dart';
import '../services/shared_preferences_data_source.dart';
import '../utils/app_logger.dart';

/// 工作量数据仓库接口
abstract class WorkloadRepository {
  /// 监听工作量统计变化
  Stream<List<WorkloadStatistics>> watchWorkloadStatistics();
  
  /// 获取工人工作量统计
  Future<List<WorkloadStatistics>> getWorkerStatistics({
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// 获取工作量概览
  Future<WorkloadOverview> getWorkloadOverview({
    DateTime? startDate,
    DateTime? endDate,
  });
  
  /// 保存工作量分配
  Future<void> saveWorkloadAssignment(String taskId, WorkloadAssignment assignment);
  
  /// 获取任务的工作量分配
  Future<WorkloadAssignment?> getTaskWorkloadAssignment(String taskId);
  
  /// 删除工作量分配
  Future<void> deleteWorkloadAssignment(String taskId);
  
  /// 批量更新工作量数据
  Future<void> batchUpdateWorkloadData(Map<String, WorkloadAssignment> assignments);
}

/// 工作量数据仓库实现
class WorkloadRepositoryImpl implements WorkloadRepository {
  static const String _tagName = 'WorkloadRepository';
  
  final HiveStorageService _hiveStorage;
  final SharedPreferencesDataSource _backupStorage;
  final StreamController<List<WorkloadStatistics>> _statisticsController;
  
  // 缓存数据
  List<WorkloadStatistics> _cachedStatistics = [];
  Map<String, WorkloadAssignment> _cachedAssignments = {};
  bool _isInitialized = false;
  
  WorkloadRepositoryImpl({
    required HiveStorageService hiveStorage,
    required SharedPreferencesDataSource backupStorage,
  }) : _hiveStorage = hiveStorage,
       _backupStorage = backupStorage,
       _statisticsController = StreamController<List<WorkloadStatistics>>.broadcast();

  @override
  Stream<List<WorkloadStatistics>> watchWorkloadStatistics() {
    if (!_isInitialized) {
      _initialize();
    }
    return _statisticsController.stream;
  }

  @override
  Future<List<WorkloadStatistics>> getWorkerStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    try {
      // 这里应该从存储中加载工作量统计数据
      // 目前返回缓存的数据
      var statistics = List<WorkloadStatistics>.from(_cachedStatistics);
      
      // 应用时间过滤
      if (startDate != null || endDate != null) {
        statistics = statistics.where((stat) {
          if (startDate != null && stat.lastUpdated.isBefore(startDate)) {
            return false;
          }
          if (endDate != null && stat.lastUpdated.isAfter(endDate)) {
            return false;
          }
          return true;
        }).toList();
      }
      
      AppLogger.info('获取工人统计数据: ${statistics.length}条', tag: _tagName);
      return statistics;
    } catch (e, stackTrace) {
      AppLogger.error('获取工人统计数据失败: $e', tag: _tagName, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<WorkloadOverview> getWorkloadOverview({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final statistics = await getWorkerStatistics(
        startDate: startDate,
        endDate: endDate,
      );
      
      if (statistics.isEmpty) {
        return const WorkloadOverview(
          totalTasks: 0,
          completedTasks: 0,
          totalTonnage: 0.0,
          completedTonnage: 0.0,
          activeWorkers: 0,
          averageEfficiency: 0.0,
          completionRate: 0.0,
        );
      }
      
      int totalTasks = 0;
      int completedTasks = 0;
      double totalTonnage = 0.0;
      double completedTonnage = 0.0;
      double totalEfficiency = 0.0;
      
      for (final stat in statistics) {
        totalTasks += stat.totalTasks;
        completedTasks += stat.completedTasks;
        totalTonnage += stat.assignedTonnage;
        completedTonnage += stat.completedTonnage;
        totalEfficiency += stat.efficiency;
      }
      
      final averageEfficiency = statistics.isNotEmpty 
          ? totalEfficiency / statistics.length 
          : 0.0;
      
      final completionRate = totalTasks > 0 
          ? completedTasks / totalTasks 
          : 0.0;
      
      final overview = WorkloadOverview(
        totalTasks: totalTasks,
        completedTasks: completedTasks,
        totalTonnage: totalTonnage,
        completedTonnage: completedTonnage,
        activeWorkers: statistics.length,
        averageEfficiency: averageEfficiency,
        completionRate: completionRate,
      );
      
      AppLogger.info('计算工作量概览: ${overview.toString()}', tag: _tagName);
      return overview;
    } catch (e, stackTrace) {
      AppLogger.error('计算工作量概览失败: $e', tag: _tagName, stackTrace: stackTrace);
      return const WorkloadOverview(
        totalTasks: 0,
        completedTasks: 0,
        totalTonnage: 0.0,
        completedTonnage: 0.0,
        activeWorkers: 0,
        averageEfficiency: 0.0,
        completionRate: 0.0,
      );
    }
  }

  @override
  Future<void> saveWorkloadAssignment(String taskId, WorkloadAssignment assignment) async {
    try {
      AppLogger.info('保存工作量分配: $taskId', tag: _tagName);
      
      // 更新缓存
      _cachedAssignments[taskId] = assignment;
      
      // 保存到主存储（这里需要实现具体的存储逻辑）
      // await _hiveStorage.saveWorkloadAssignment(taskId, assignment);
      
      // 异步备份
      unawaited(_backupWorkloadAssignment(taskId, assignment));
      
      // 重新计算统计数据
      await _recalculateStatistics();
      
      AppLogger.info('工作量分配保存成功: $taskId', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('保存工作量分配失败: $taskId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<WorkloadAssignment?> getTaskWorkloadAssignment(String taskId) async {
    try {
      // 先从缓存获取
      if (_cachedAssignments.containsKey(taskId)) {
        return _cachedAssignments[taskId];
      }
      
      // 从存储加载（这里需要实现具体的加载逻辑）
      // final assignment = await _hiveStorage.loadWorkloadAssignment(taskId);
      // if (assignment != null) {
      //   _cachedAssignments[taskId] = assignment;
      //   return assignment;
      // }
      
      return null;
    } catch (e, stackTrace) {
      AppLogger.error('获取任务工作量分配失败: $taskId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      return null;
    }
  }

  @override
  Future<void> deleteWorkloadAssignment(String taskId) async {
    try {
      AppLogger.info('删除工作量分配: $taskId', tag: _tagName);
      
      // 从缓存移除
      _cachedAssignments.remove(taskId);
      
      // 从主存储删除
      // await _hiveStorage.deleteWorkloadAssignment(taskId);
      
      // 从备份存储删除
      // await _backupStorage.deleteWorkloadAssignment(taskId);
      
      // 重新计算统计数据
      await _recalculateStatistics();
      
      AppLogger.info('工作量分配删除成功: $taskId', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('删除工作量分配失败: $taskId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> batchUpdateWorkloadData(Map<String, WorkloadAssignment> assignments) async {
    try {
      AppLogger.info('批量更新工作量数据: ${assignments.length}条', tag: _tagName);
      
      // 批量更新缓存
      _cachedAssignments.addAll(assignments);
      
      // 批量保存到主存储
      for (final entry in assignments.entries) {
        // await _hiveStorage.saveWorkloadAssignment(entry.key, entry.value);
      }
      
      // 异步批量备份
      unawaited(_batchBackupAssignments(assignments));
      
      // 重新计算统计数据
      await _recalculateStatistics();
      
      AppLogger.info('批量更新工作量数据成功: ${assignments.length}条', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('批量更新工作量数据失败: $e', tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 初始化数据
  Future<void> _initialize() async {
    if (_isInitialized) return;
    
    try {
      AppLogger.info('初始化工作量仓库', tag: _tagName);
      
      // 从主存储加载数据
      // _cachedAssignments = await _hiveStorage.loadAllWorkloadAssignments();
      
      // 计算统计数据
      await _recalculateStatistics();
      
      _isInitialized = true;
      AppLogger.info('工作量仓库初始化完成', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('工作量仓库初始化失败: $e', tag: _tagName, stackTrace: stackTrace);
      _isInitialized = true; // 即使失败也标记为已初始化，避免重复尝试
    }
  }

  /// 重新计算统计数据
  Future<void> _recalculateStatistics() async {
    try {
      // 这里应该基于_cachedAssignments重新计算统计数据
      // 目前使用空列表
      _cachedStatistics = [];
      
      // 通知监听者
      _statisticsController.add(List.from(_cachedStatistics));
      
      AppLogger.debug('工作量统计数据重新计算完成', tag: _tagName);
    } catch (e) {
      AppLogger.warning('重新计算统计数据失败: $e', tag: _tagName);
    }
  }

  /// 异步备份工作量分配
  Future<void> _backupWorkloadAssignment(String taskId, WorkloadAssignment assignment) async {
    try {
      // 这里应该实现备份逻辑
      AppLogger.debug('工作量分配备份成功: $taskId', tag: _tagName);
    } catch (e) {
      AppLogger.warning('工作量分配备份失败: $taskId, 错误: $e', tag: _tagName);
    }
  }

  /// 异步批量备份
  Future<void> _batchBackupAssignments(Map<String, WorkloadAssignment> assignments) async {
    try {
      // 这里应该实现批量备份逻辑
      AppLogger.debug('批量工作量分配备份成功: ${assignments.length}条', tag: _tagName);
    } catch (e) {
      AppLogger.warning('批量工作量分配备份失败: $e', tag: _tagName);
    }
  }

  /// 释放资源
  void dispose() {
    _statisticsController.close();
    AppLogger.info('工作量仓库资源已释放', tag: _tagName);
  }
}
