import 'dart:async';
import '../models/task_model.dart';
import '../services/hive_storage_service.dart';
import '../services/shared_preferences_data_source.dart';
import '../utils/app_logger.dart';

/// 任务数据仓库接口
/// 提供统一的任务数据访问接口，解决数据一致性问题
abstract class TaskRepository {
  /// 监听任务列表变化
  Stream<List<TaskModel>> watchTasks();
  
  /// 获取单个任务
  Future<TaskModel?> getTask(String id);
  
  /// 获取所有任务
  Future<List<TaskModel>> getAllTasks();
  
  /// 保存单个任务
  Future<void> saveTask(TaskModel task);
  
  /// 批量保存任务
  Future<void> saveTasks(List<TaskModel> tasks);
  
  /// 删除任务
  Future<void> deleteTask(String id);
  
  /// 清空所有任务
  Future<void> clearAllTasks();
  
  /// 获取当前正在执行的任务
  TaskModel? get currentTask;
  
  /// 设置当前任务
  Future<void> setCurrentTask(TaskModel? task);
}

/// 任务数据仓库实现
/// 解决TaskService中_currentTask、_tasks列表和持久化存储的同步问题
class TaskRepositoryImpl implements TaskRepository {
  static const String _tagName = 'TaskRepository';
  
  final HiveStorageService _hiveStorage;
  final SharedPreferencesDataSource _backupStorage;
  final StreamController<List<TaskModel>> _tasksController;
  
  // 单一数据源缓存，确保数据一致性
  List<TaskModel> _cachedTasks = [];
  TaskModel? _currentTask;
  bool _isInitialized = false;
  
  TaskRepositoryImpl({
    required HiveStorageService hiveStorage,
    required SharedPreferencesDataSource backupStorage,
  }) : _hiveStorage = hiveStorage,
       _backupStorage = backupStorage,
       _tasksController = StreamController<List<TaskModel>>.broadcast();

  @override
  Stream<List<TaskModel>> watchTasks() {
    if (!_isInitialized) {
      _initialize();
    }
    return _tasksController.stream;
  }

  @override
  Future<List<TaskModel>> getAllTasks() async {
    if (!_isInitialized) {
      await _initialize();
    }
    return List.from(_cachedTasks);
  }

  @override
  Future<TaskModel?> getTask(String id) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    try {
      return _cachedTasks.firstWhere((task) => task.id == id);
    } catch (e) {
      AppLogger.warning('任务未找到: $id', tag: _tagName);
      return null;
    }
  }

  @override
  TaskModel? get currentTask => _currentTask;

  @override
  Future<void> setCurrentTask(TaskModel? task) async {
    _currentTask = task;
    AppLogger.info('设置当前任务: ${task?.id ?? 'null'}', tag: _tagName);
  }

  @override
  Future<void> saveTask(TaskModel task) async {
    try {
      AppLogger.info('保存任务: ${task.id}', tag: _tagName);
      
      // 1. 更新缓存 - 确保数据一致性
      final index = _cachedTasks.indexWhere((t) => t.id == task.id);
      if (index >= 0) {
        _cachedTasks[index] = task;
        AppLogger.debug('更新现有任务缓存: ${task.id}', tag: _tagName);
      } else {
        _cachedTasks.add(task);
        AppLogger.debug('添加新任务到缓存: ${task.id}', tag: _tagName);
      }
      
      // 2. 更新当前任务（如果是同一个任务）
      if (_currentTask?.id == task.id) {
        _currentTask = task;
        AppLogger.debug('更新当前任务: ${task.id}', tag: _tagName);
      }
      
      // 3. 保存到主存储（Hive）
      await _hiveStorage.saveTask(task);
      
      // 4. 异步备份到SharedPreferences（不阻塞主流程）
      unawaited(_backupToSharedPreferences(task));
      
      // 5. 通知监听者
      _tasksController.add(List.from(_cachedTasks));
      
      AppLogger.info('任务保存成功: ${task.id}', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('保存任务失败: ${task.id}, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> saveTasks(List<TaskModel> tasks) async {
    try {
      AppLogger.info('批量保存任务: ${tasks.length}个', tag: _tagName);
      
      // 1. 更新缓存
      _cachedTasks.clear();
      _cachedTasks.addAll(tasks);
      
      // 2. 批量保存到主存储
      await _hiveStorage.saveTasks(tasks);
      
      // 3. 异步备份
      unawaited(_backupAllToSharedPreferences(tasks));
      
      // 4. 通知监听者
      _tasksController.add(List.from(_cachedTasks));
      
      AppLogger.info('批量保存任务成功: ${tasks.length}个', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('批量保存任务失败: $e', tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> deleteTask(String id) async {
    try {
      AppLogger.info('删除任务: $id', tag: _tagName);
      
      // 1. 从缓存中移除
      _cachedTasks.removeWhere((task) => task.id == id);
      
      // 2. 清除当前任务（如果是同一个）
      if (_currentTask?.id == id) {
        _currentTask = null;
      }
      
      // 3. 从主存储删除
      await _hiveStorage.deleteTask(id);
      
      // 4. 从备份存储删除
      await _backupStorage.deleteTask(id);
      
      // 5. 通知监听者
      _tasksController.add(List.from(_cachedTasks));
      
      AppLogger.info('任务删除成功: $id', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('删除任务失败: $id, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> clearAllTasks() async {
    try {
      AppLogger.info('清空所有任务', tag: _tagName);
      
      // 1. 清空缓存
      _cachedTasks.clear();
      _currentTask = null;
      
      // 2. 清空主存储
      await _hiveStorage.clearAllTasks();
      
      // 3. 清空备份存储
      await _backupStorage.clearAllTasks();
      
      // 4. 通知监听者
      _tasksController.add(List.from(_cachedTasks));
      
      AppLogger.info('所有任务清空成功', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('清空任务失败: $e', tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 初始化数据
  Future<void> _initialize() async {
    if (_isInitialized) return;
    
    try {
      AppLogger.info('初始化任务仓库', tag: _tagName);
      
      // 从主存储加载数据
      final tasks = await _hiveStorage.loadTasks();
      _cachedTasks = tasks;
      
      // 发送初始数据
      _tasksController.add(List.from(_cachedTasks));
      
      _isInitialized = true;
      AppLogger.info('任务仓库初始化完成，加载${tasks.length}个任务', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('任务仓库初始化失败: $e', tag: _tagName, stackTrace: stackTrace);
      
      // 尝试从备份恢复
      try {
        final backupTasks = await _backupStorage.loadTasks();
        _cachedTasks = backupTasks;
        _tasksController.add(List.from(_cachedTasks));
        _isInitialized = true;
        AppLogger.info('从备份恢复${backupTasks.length}个任务', tag: _tagName);
      } catch (backupError) {
        AppLogger.error('从备份恢复也失败: $backupError', tag: _tagName);
        _cachedTasks = [];
        _tasksController.add([]);
        _isInitialized = true;
      }
    }
  }

  /// 异步备份到SharedPreferences
  Future<void> _backupToSharedPreferences(TaskModel task) async {
    try {
      await _backupStorage.saveTask(task);
      AppLogger.debug('任务备份成功: ${task.id}', tag: _tagName);
    } catch (e) {
      AppLogger.warning('任务备份失败: ${task.id}, 错误: $e', tag: _tagName);
    }
  }

  /// 异步批量备份
  Future<void> _backupAllToSharedPreferences(List<TaskModel> tasks) async {
    try {
      await _backupStorage.saveTasks(tasks);
      AppLogger.debug('批量任务备份成功: ${tasks.length}个', tag: _tagName);
    } catch (e) {
      AppLogger.warning('批量任务备份失败: $e', tag: _tagName);
    }
  }

  /// 释放资源
  void dispose() {
    _tasksController.close();
    AppLogger.info('任务仓库资源已释放', tag: _tagName);
  }
}
