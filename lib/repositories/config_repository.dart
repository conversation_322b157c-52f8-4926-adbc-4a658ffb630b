import 'dart:async';
import '../models/workload_models.dart';
import '../services/hive_storage_service.dart';
import '../services/shared_preferences_data_source.dart';
import '../utils/app_logger.dart';

/// 配置数据仓库接口
abstract class ConfigRepository {
  // 工人管理
  Future<List<WorkerConfig>> getWorkers({WorkerFilter? filter});
  Future<WorkerConfig?> getWorker(String workerId);
  Future<void> saveWorker(WorkerConfig worker);
  Future<void> deleteWorker(String workerId);
  Stream<List<WorkerConfig>> watchWorkers();
  
  // 仓库管理
  Future<List<WarehouseConfig>> getWarehouses();
  Future<WarehouseConfig?> getWarehouse(String warehouseId);
  Future<void> saveWarehouse(WarehouseConfig warehouse);
  Future<void> deleteWarehouse(String warehouseId);
  
  // 小组管理
  Future<List<GroupConfig>> getGroups({String? warehouseId});
  Future<GroupConfig?> getGroup(String groupId);
  Future<void> saveGroup(GroupConfig group);
  Future<void> deleteGroup(String groupId);
  
  // 模板管理
  Future<List<TemplateConfig>> getTemplates();
  Future<TemplateConfig?> getTemplate(String templateId);
  Future<void> saveTemplate(TemplateConfig template);
  Future<void> deleteTemplate(String templateId);
  
  // 配置同步
  Future<void> syncConfigurations();
  Future<void> exportConfigurations(String filePath);
  Future<void> importConfigurations(String filePath);
}

/// 工人过滤器
class WorkerFilter {
  final String? warehouse;
  final String? group;
  final String? role;
  final bool? isActive;

  const WorkerFilter({
    this.warehouse,
    this.group,
    this.role,
    this.isActive,
  });
}

/// 配置数据仓库实现
class ConfigRepositoryImpl implements ConfigRepository {
  static const String _tagName = 'ConfigRepository';
  
  final HiveStorageService _hiveStorage;
  final SharedPreferencesDataSource _backupStorage;
  final StreamController<List<WorkerConfig>> _workersController;
  
  // 缓存数据
  List<WorkerConfig> _cachedWorkers = [];
  List<WarehouseConfig> _cachedWarehouses = [];
  List<GroupConfig> _cachedGroups = [];
  List<TemplateConfig> _cachedTemplates = [];
  bool _isInitialized = false;
  
  ConfigRepositoryImpl({
    required HiveStorageService hiveStorage,
    required SharedPreferencesDataSource backupStorage,
  }) : _hiveStorage = hiveStorage,
       _backupStorage = backupStorage,
       _workersController = StreamController<List<WorkerConfig>>.broadcast();

  @override
  Stream<List<WorkerConfig>> watchWorkers() {
    if (!_isInitialized) {
      _initialize();
    }
    return _workersController.stream;
  }

  @override
  Future<List<WorkerConfig>> getWorkers({WorkerFilter? filter}) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    try {
      var workers = List<WorkerConfig>.from(_cachedWorkers);
      
      // 应用过滤器
      if (filter != null) {
        workers = workers.where((worker) {
          if (filter.warehouse != null && worker.warehouse != filter.warehouse) {
            return false;
          }
          if (filter.group != null && worker.group != filter.group) {
            return false;
          }
          if (filter.role != null && worker.role != filter.role) {
            return false;
          }
          if (filter.isActive != null && worker.isActive != filter.isActive) {
            return false;
          }
          return true;
        }).toList();
      }
      
      AppLogger.info('获取工人配置: ${workers.length}条', tag: _tagName);
      return workers;
    } catch (e, stackTrace) {
      AppLogger.error('获取工人配置失败: $e', tag: _tagName, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<WorkerConfig?> getWorker(String workerId) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    try {
      return _cachedWorkers.firstWhere((worker) => worker.id == workerId);
    } catch (e) {
      AppLogger.warning('工人配置未找到: $workerId', tag: _tagName);
      return null;
    }
  }

  @override
  Future<void> saveWorker(WorkerConfig worker) async {
    try {
      AppLogger.info('保存工人配置: ${worker.id}', tag: _tagName);
      
      // 更新缓存
      final index = _cachedWorkers.indexWhere((w) => w.id == worker.id);
      if (index >= 0) {
        _cachedWorkers[index] = worker;
      } else {
        _cachedWorkers.add(worker);
      }
      
      // 保存到主存储
      // await _hiveStorage.saveWorkerConfig(worker);
      
      // 异步备份
      unawaited(_backupWorkerConfig(worker));
      
      // 通知监听者
      _workersController.add(List.from(_cachedWorkers));
      
      AppLogger.info('工人配置保存成功: ${worker.id}', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('保存工人配置失败: ${worker.id}, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> deleteWorker(String workerId) async {
    try {
      AppLogger.info('删除工人配置: $workerId', tag: _tagName);
      
      // 从缓存移除
      _cachedWorkers.removeWhere((worker) => worker.id == workerId);
      
      // 从主存储删除
      // await _hiveStorage.deleteWorkerConfig(workerId);
      
      // 从备份存储删除
      // await _backupStorage.deleteWorkerConfig(workerId);
      
      // 通知监听者
      _workersController.add(List.from(_cachedWorkers));
      
      AppLogger.info('工人配置删除成功: $workerId', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('删除工人配置失败: $workerId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<WarehouseConfig>> getWarehouses() async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    try {
      AppLogger.info('获取仓库配置: ${_cachedWarehouses.length}条', tag: _tagName);
      return List.from(_cachedWarehouses);
    } catch (e, stackTrace) {
      AppLogger.error('获取仓库配置失败: $e', tag: _tagName, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<WarehouseConfig?> getWarehouse(String warehouseId) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    try {
      return _cachedWarehouses.firstWhere((warehouse) => warehouse.id == warehouseId);
    } catch (e) {
      AppLogger.warning('仓库配置未找到: $warehouseId', tag: _tagName);
      return null;
    }
  }

  @override
  Future<void> saveWarehouse(WarehouseConfig warehouse) async {
    try {
      AppLogger.info('保存仓库配置: ${warehouse.id}', tag: _tagName);
      
      // 更新缓存
      final index = _cachedWarehouses.indexWhere((w) => w.id == warehouse.id);
      if (index >= 0) {
        _cachedWarehouses[index] = warehouse;
      } else {
        _cachedWarehouses.add(warehouse);
      }
      
      // 保存到主存储
      // await _hiveStorage.saveWarehouseConfig(warehouse);
      
      // 异步备份
      unawaited(_backupWarehouseConfig(warehouse));
      
      AppLogger.info('仓库配置保存成功: ${warehouse.id}', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('保存仓库配置失败: ${warehouse.id}, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> deleteWarehouse(String warehouseId) async {
    try {
      AppLogger.info('删除仓库配置: $warehouseId', tag: _tagName);
      
      // 从缓存移除
      _cachedWarehouses.removeWhere((warehouse) => warehouse.id == warehouseId);
      
      // 从主存储删除
      // await _hiveStorage.deleteWarehouseConfig(warehouseId);
      
      AppLogger.info('仓库配置删除成功: $warehouseId', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('删除仓库配置失败: $warehouseId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<GroupConfig>> getGroups({String? warehouseId}) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    try {
      var groups = List<GroupConfig>.from(_cachedGroups);
      
      // 按仓库过滤
      if (warehouseId != null) {
        groups = groups.where((group) => group.warehouse == warehouseId).toList();
      }
      
      AppLogger.info('获取小组配置: ${groups.length}条', tag: _tagName);
      return groups;
    } catch (e, stackTrace) {
      AppLogger.error('获取小组配置失败: $e', tag: _tagName, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<GroupConfig?> getGroup(String groupId) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    try {
      return _cachedGroups.firstWhere((group) => group.id == groupId);
    } catch (e) {
      AppLogger.warning('小组配置未找到: $groupId', tag: _tagName);
      return null;
    }
  }

  @override
  Future<void> saveGroup(GroupConfig group) async {
    try {
      AppLogger.info('保存小组配置: ${group.id}', tag: _tagName);
      
      // 更新缓存
      final index = _cachedGroups.indexWhere((g) => g.id == group.id);
      if (index >= 0) {
        _cachedGroups[index] = group;
      } else {
        _cachedGroups.add(group);
      }
      
      // 保存到主存储
      // await _hiveStorage.saveGroupConfig(group);
      
      AppLogger.info('小组配置保存成功: ${group.id}', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('保存小组配置失败: ${group.id}, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> deleteGroup(String groupId) async {
    try {
      AppLogger.info('删除小组配置: $groupId', tag: _tagName);
      
      // 从缓存移除
      _cachedGroups.removeWhere((group) => group.id == groupId);
      
      // 从主存储删除
      // await _hiveStorage.deleteGroupConfig(groupId);
      
      AppLogger.info('小组配置删除成功: $groupId', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('删除小组配置失败: $groupId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<TemplateConfig>> getTemplates() async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    try {
      AppLogger.info('获取模板配置: ${_cachedTemplates.length}条', tag: _tagName);
      return List.from(_cachedTemplates);
    } catch (e, stackTrace) {
      AppLogger.error('获取模板配置失败: $e', tag: _tagName, stackTrace: stackTrace);
      return [];
    }
  }

  @override
  Future<TemplateConfig?> getTemplate(String templateId) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    try {
      return _cachedTemplates.firstWhere((template) => template.id == templateId);
    } catch (e) {
      AppLogger.warning('模板配置未找到: $templateId', tag: _tagName);
      return null;
    }
  }

  @override
  Future<void> saveTemplate(TemplateConfig template) async {
    try {
      AppLogger.info('保存模板配置: ${template.id}', tag: _tagName);
      
      // 更新缓存
      final index = _cachedTemplates.indexWhere((t) => t.id == template.id);
      if (index >= 0) {
        _cachedTemplates[index] = template;
      } else {
        _cachedTemplates.add(template);
      }
      
      // 保存到主存储
      // await _hiveStorage.saveTemplateConfig(template);
      
      AppLogger.info('模板配置保存成功: ${template.id}', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('保存模板配置失败: ${template.id}, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> deleteTemplate(String templateId) async {
    try {
      AppLogger.info('删除模板配置: $templateId', tag: _tagName);
      
      // 从缓存移除
      _cachedTemplates.removeWhere((template) => template.id == templateId);
      
      // 从主存储删除
      // await _hiveStorage.deleteTemplateConfig(templateId);
      
      AppLogger.info('模板配置删除成功: $templateId', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('删除模板配置失败: $templateId, 错误: $e', 
          tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> syncConfigurations() async {
    try {
      AppLogger.info('开始同步配置数据', tag: _tagName);
      
      // 这里应该实现配置同步逻辑
      // 例如：从服务器同步最新配置
      
      AppLogger.info('配置数据同步完成', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('配置数据同步失败: $e', tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> exportConfigurations(String filePath) async {
    try {
      AppLogger.info('导出配置数据到: $filePath', tag: _tagName);
      
      // 这里应该实现配置导出逻辑
      
      AppLogger.info('配置数据导出完成', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('配置数据导出失败: $e', tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> importConfigurations(String filePath) async {
    try {
      AppLogger.info('从文件导入配置数据: $filePath', tag: _tagName);
      
      // 这里应该实现配置导入逻辑
      
      AppLogger.info('配置数据导入完成', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('配置数据导入失败: $e', tag: _tagName, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 初始化数据
  Future<void> _initialize() async {
    if (_isInitialized) return;
    
    try {
      AppLogger.info('初始化配置仓库', tag: _tagName);
      
      // 从主存储加载数据
      // _cachedWorkers = await _hiveStorage.loadWorkerConfigs();
      // _cachedWarehouses = await _hiveStorage.loadWarehouseConfigs();
      // _cachedGroups = await _hiveStorage.loadGroupConfigs();
      // _cachedTemplates = await _hiveStorage.loadTemplateConfigs();
      
      // 发送初始数据
      _workersController.add(List.from(_cachedWorkers));
      
      _isInitialized = true;
      AppLogger.info('配置仓库初始化完成', tag: _tagName);
    } catch (e, stackTrace) {
      AppLogger.error('配置仓库初始化失败: $e', tag: _tagName, stackTrace: stackTrace);
      _isInitialized = true; // 即使失败也标记为已初始化
    }
  }

  /// 异步备份工人配置
  Future<void> _backupWorkerConfig(WorkerConfig worker) async {
    try {
      // 这里应该实现备份逻辑
      AppLogger.debug('工人配置备份成功: ${worker.id}', tag: _tagName);
    } catch (e) {
      AppLogger.warning('工人配置备份失败: ${worker.id}, 错误: $e', tag: _tagName);
    }
  }

  /// 异步备份仓库配置
  Future<void> _backupWarehouseConfig(WarehouseConfig warehouse) async {
    try {
      // 这里应该实现备份逻辑
      AppLogger.debug('仓库配置备份成功: ${warehouse.id}', tag: _tagName);
    } catch (e) {
      AppLogger.warning('仓库配置备份失败: ${warehouse.id}, 错误: $e', tag: _tagName);
    }
  }

  /// 释放资源
  void dispose() {
    _workersController.close();
    AppLogger.info('配置仓库资源已释放', tag: _tagName);
  }
}
