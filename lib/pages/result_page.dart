import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/core/providers/app_providers.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart'; // 暂时移除，等待安装依赖
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/services/pdf_service.dart';
import 'package:loadguard/models/task_model.dart';
import '../utils/theme_colors.dart';
import '../widgets/themed_card.dart';
import '../widgets/themed_button.dart';
import '../widgets/industrial_logo.dart';
import '../utils/simple_navigation_helper.dart';
import '../models/worker_info_data.dart';
import '../utils/app_logger.dart';

class ResultPage extends ConsumerStatefulWidget {
  final String taskId;

  const ResultPage({super.key, required this.taskId});

  @override
  ConsumerState<ResultPage> createState() => _ResultPageState();
}

class _ResultPageState extends ConsumerState<ResultPage> with TickerProviderStateMixin {
  TaskModel? _task;
  bool _isLoading = true;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTask();
    });
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _loadTask() {
    // 🔒 参数校验
    if (widget.taskId.isEmpty) {
      _showError('任务ID不能为空');
      return;
    }

    if (!RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(widget.taskId)) {
      _showError('任务ID格式无效');
      return;
    }

    final taskService = ref.read(taskServiceProvider);
    final task = taskService.getTaskById(widget.taskId);

    // 🔒 任务存在性校验
    if (task == null) {
      _showError('任务不存在或已被删除');
      return;
    }

    setState(() {
      _task = task;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: ThemeColors.primaryGradient,
          ),
          child: const Center(
            child: CircularProgressIndicator(color: Colors.white),
          ),
        ),
      );
    }

    if (_task == null) {
      return Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: ThemeColors.primaryGradient,
          ),
          child: Center(
            child: ThemedCard(
              type: CardType.glass,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: ThemeColors.textOnGradient,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '任务不存在',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                      color: ThemeColors.textOnGradient,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '请检查任务ID是否正确',
                    style: TextStyle(
                      fontSize: 16,
                      color: ThemeColors.textOnGradient.withOpacity(0.8),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ThemedButton(
                    text: '返回首页',
                    onPressed: () => context.go('/'),
                    gradient: ThemeColors.primaryButtonGradient,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    void _handleBackNavigation() async {
      // 结果页面提供明确的导航选择
      final choice = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('选择操作'),
          content: const Text('请选择您要进行的操作：'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop('home'),
              child: const Text('返回首页'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop('template'),
              child: const Text('继续识别'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop('stay'),
              child: const Text('留在此页'),
            ),
          ],
        ),
      );

      switch (choice) {
        case 'home':
          context.go('/');
          break;
        case 'template':
          context.go('/template-selection');
          break;
        case 'stay':
        default:
          // 什么都不做，留在当前页面
          break;
      }
    }

    return SimpleNavigationHelper.buildStandardPage(
      onBackPressed: _handleBackNavigation,
      enableSwipeBack: true,
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: ThemeColors.primaryGradient,
          ),
          child: SafeArea(
            child: Column(
              children: [
                _buildAppBar(),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return SlideTransition(
                          position: _slideAnimation,
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Column(
                              children: [
                                _buildTaskSummary(),
                                const SizedBox(height: 20),
                                _buildRecognitionSummary(),
                                const SizedBox(height: 20),
                                _buildPhotoResults(),
                                const SizedBox(height: 20),
                                _buildActionButtons(),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return ThemedCard(
      type: CardType.glass,
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IndustrialLogo(
            size: 32,
            primaryColor: ThemeColors.textOnGradient,
            accentColor: ThemeColors.primaryLight,
            showText: false,
          ),
        ],
      ),
    );
  }

  Widget _buildTaskSummary() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: _task!.isCompleted
                      ? ThemeColors.successButtonGradient
                      : ThemeColors.warningButtonGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  _task!.isCompleted
                      ? Icons.check_circle
                      : Icons.hourglass_empty,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '任务信息',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: ThemeColors.textOnGradient,
                      ),
                    ),
                    Text(
                      _task!.isCompleted ? '已完成' : '进行中',
                      style: TextStyle(
                        fontSize: 14,
                        color: ThemeColors.textOnGradient.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildInfoRow('模板类型', _task!.template, Icons.category),
          const SizedBox(height: 12),
          _buildInfoRow(
              '创建时间',
              DateFormat('yyyy-MM-dd HH:mm').format(_task!.createdAt),
              Icons.access_time),
          if (_task!.completedAt != null) ...[
            const SizedBox(height: 12),
            _buildInfoRow(
                '完成时间',
                DateFormat('yyyy-MM-dd HH:mm').format(_task!.completedAt!),
                Icons.done),
          ],
          if (_task!.participants.isNotEmpty) ...[
            const SizedBox(height: 12),
            _buildParticipantsSection(_task!),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: ThemeColors.textOnGradient.withOpacity(0.8),
          size: 20,
        ),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: ThemeColors.textOnGradient,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: ThemeColors.textOnGradient.withOpacity(0.9),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRecognitionSummary() {
    final totalPhotos = _task!.photos.length;
    final takenPhotos = _task!.photosTaken;
    final verifiedPhotos = _task!.photosVerified;
    final requiredPhotos = _task!.photos.where((p) => p.isRequired).length;
    final completedRequired =
        _task!.photos.where((p) => p.isRequired && p.imagePath != null).length;

    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.blueGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.analytics,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '拍照统计',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  '总照片',
                  '$takenPhotos/$totalPhotos',
                  ThemeColors.blueGradient,
                ),
              ),
              Expanded(
                child: _buildStatCard(
                  '必拍完成',
                  '$completedRequired/$requiredPhotos',
                  ThemeColors.orangeGradient,
                ),
              ),
              Expanded(
                child: _buildStatCard(
                  '验证通过',
                  '$verifiedPhotos',
                  ThemeColors.greenGradient,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 进度条
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LinearProgressIndicator(
                value: totalPhotos > 0 ? takenPhotos / totalPhotos : 0,
                backgroundColor: Colors.white.withOpacity(0.2),
                valueColor: AlwaysStoppedAnimation<Color>(
                  completedRequired == requiredPhotos
                      ? ThemeColors.success
                      : ThemeColors.warning,
                ),
                minHeight: 8,
              ),
              const SizedBox(height: 8),
              // 进度条说明文字
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '拍照进度',
                    style: TextStyle(
                      fontSize: 12,
                      color: ThemeColors.textOnGradient.withOpacity(0.8),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    completedRequired == requiredPhotos
                        ? '✅ 必拍项已完成'
                        : '⚠️ 请完成所有必拍项',
                    style: TextStyle(
                      fontSize: 11,
                      color: completedRequired == requiredPhotos
                          ? ThemeColors.success
                          : ThemeColors.warning,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, LinearGradient gradient) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.w800,
              color: ThemeColors.textOnGradient,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 6),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: ThemeColors.textOnGradient.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoResults() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.greenGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.photo_library,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '照片详情',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _task!.photos.length,
            separatorBuilder: (context, index) => const SizedBox(height: 8),
            itemBuilder: (context, index) {
              final photo = _task!.photos[index];
              return _buildPhotoItem(photo, index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoItem(PhotoItem photo, int index) {
    final hasPhoto = photo.imagePath != null;
    final hasResult = photo.recognitionResult != null;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: hasPhoto
                  ? (hasResult ? ThemeColors.success : ThemeColors.warning)
                  : ThemeColors.info.withOpacity(0.5),
              borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
            ),
            child: Center(
              child: hasPhoto
                  ? Icon(
                      hasResult ? Icons.check_circle : Icons.hourglass_empty,
                      color: ThemeColors.textOnGradient,
                      size: 20,
                    )
                  : Text(
                      '${index + 1}',
                      style: TextStyle(
                        color: ThemeColors.textOnGradient,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      photo.label,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: ThemeColors.textOnGradient,
                      ),
                    ),
                    if (photo.isRequired) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: ThemeColors.warning,
                          borderRadius:
                              BorderRadius.circular(ThemeColors.radiusSmall),
                        ),
                        child: Text(
                          '必拍',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: ThemeColors.textOnGradient,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                if (hasResult &&
                    photo.recognitionResult!.extractedProductCode != null)
                  Text(
                    '识别结果: ${photo.recognitionResult!.extractedProductCode}',
                    style: TextStyle(
                      fontSize: 12,
                      color: ThemeColors.textOnGradient.withOpacity(0.9),
                    ),
                  )
                else if (hasPhoto)
                  Text(
                    '已拍摄，等待识别',
                    style: TextStyle(
                      fontSize: 12,
                      color: ThemeColors.textOnGradient.withOpacity(0.7),
                    ),
                  )
                else
                  Text(
                    '未拍摄',
                    style: TextStyle(
                      fontSize: 12,
                      color: ThemeColors.textOnGradient.withOpacity(0.5),
                    ),
                  ),
              ],
            ),
          ),
          if (hasResult)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: photo.isVerified
                    ? ThemeColors.success.withOpacity(0.3)
                    : ThemeColors.warning.withOpacity(0.3),
                borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
              ),
              child: Text(
                photo.isVerified ? '已验证' : '待验证',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: ThemeColors.textOnGradient,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ThemedButton(
                text: '生成报告',
                onPressed: _generateReport,
                gradient: ThemeColors.primaryButtonGradient,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ThemedButton(
                text: '分享结果',
                onPressed: _shareResults,
                gradient: ThemeColors.successButtonGradient,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ThemedButton(
                text: '继续拍照',
                onPressed: _continuePhotoSession,
                gradient: ThemeColors.warningButtonGradient,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ThemedButton(
                text: '新建任务',
                onPressed: () => context.go('/template-selection'),
                gradient: ThemeColors.orangeGradient,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _generateReport() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                    strokeWidth: 2, color: Colors.white),
              ),
              const SizedBox(width: 12),
              const Text('正在生成完整报告...'),
            ],
          ),
          backgroundColor: ThemeColors.primary,
        ),
      );

      final pdfService = PdfService();

      // 🔧 修复：使用TaskService中的最新任务数据生成PDF
      final taskService = ref.read(taskServiceProvider);
      
      // 强制保存当前内存中的所有数据，确保数据一致性
      await taskService.forceSaveAllData();
      
      final latestTask = taskService.getTaskById(widget.taskId);
      
      if (latestTask == null) {
        throw Exception('无法获取最新任务数据');
      }
      
      AppLogger.info('PDF生成 - 使用最新任务数据: ${latestTask.template}');
      AppLogger.info('PDF生成 - 任务照片数量: ${latestTask.photos.length}');
      AppLogger.info('PDF生成 - 有效照片数量: ${latestTask.photos.where((p) => p.imagePath != null && p.imagePath!.isNotEmpty).length}');
      
      // 详细记录每张照片的状态
      for (int i = 0; i < latestTask.photos.length; i++) {
        final photo = latestTask.photos[i];
        AppLogger.info('照片${i + 1}: ${photo.label} - 路径: ${photo.imagePath} - 状态: ${photo.recognitionStatus}');
      }
      
      // 生成包含参与人员和工作量统计的完整PDF报告
      final pdfData = await pdfService.generateFullReportWithWorkerStats(
        [latestTask],
        '装运卫士任务报告 - ${latestTask.template}',
        includePhotos: true,
        includeStatistics: true,
        includeWorkerStatistics: true,
      );

      // 保存PDF文件
      final filename =
          'LoadGuard_Report_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final pdfPath = await pdfService.savePdfToFile(pdfData, filename);

      // 🔧 修复：PDF生成后同步更新任务状态
      await _updateTaskStatusAfterPDF();

      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text('完整报告已生成'),
              ],
            ),
            backgroundColor: ThemeColors.success,
            action: SnackBarAction(
              label: '分享',
              textColor: Colors.white,
              onPressed: () => pdfService.sharePdf(pdfPath,
                  subject: '装运卫士任务报告（包含参与人员和工作量统计）'),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('生成报告失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 🔧 PDF生成后更新任务状态和同步数据
  Future<void> _updateTaskStatusAfterPDF() async {
    try {
      final taskService = ref.read(taskServiceProvider);
      
      // 🔧 修复：使用最新的任务数据
      final latestTask = taskService.getTaskById(widget.taskId);
      if (latestTask == null) {
        AppLogger.error('无法获取最新任务数据进行状态更新');
        return;
      }

      // 1. 标记任务为已完成（如果还未完成）
      if (!latestTask.isCompleted) {
        await taskService.completeTask();
        AppLogger.info('✅ 任务已标记为完成状态');
      }

      // 2. 更新完成时间为PDF生成时间
      latestTask.completedAt = DateTime.now();

      // 3. 同步工作量分配状态（确保PDF中的工作量数据与任务状态一致）
      if (latestTask.recognitionMetadata != null &&
          latestTask.recognitionMetadata!['workload'] != null) {
        try {
                      final workloadMap =
                latestTask.recognitionMetadata!['workload'] as Map<String, dynamic>;
          final completedAt = DateTime.now().toIso8601String();

          // 标记所有工作量记录为已完成
          if (workloadMap['records'] != null) {
            final records = workloadMap['records'] as List;
            for (var record in records) {
              record['isCompleted'] = true;
              record['completedAt'] = completedAt;
            }

            // 更新工作量分配完成状态
            workloadMap['completedAt'] = completedAt;
            workloadMap['status'] = 'completed';

            // print('✅ 工作量分配状态已同步更新: ${records.length}条记录');
          }
        } catch (e) {
          // print('⚠️ 同步工作量状态失败: $e');
        }
      }

      // 4. 保存更新后的任务状态
      await taskService.updateTask(latestTask);

      // 5. 刷新本地状态
      setState(() {});

      // print('🎯 PDF生成后状态同步完成');
    } catch (e) {
      // print('❌ PDF生成后状态更新失败: $e');
      // 不抛出异常，避免影响PDF生成成功的用户体验
    }
  }

  Future<void> _shareResults() async {
    try {
      final summary = '''
装运卫士 ML Kit V2专业版 - 识别结果

任务ID: ${_task!.id}
模板类型: ${_task!.template}
创建时间: ${DateFormat('yyyy-MM-dd HH:mm').format(_task!.createdAt)}
${_task!.completedAt != null ? '完成时间: ${DateFormat('yyyy-MM-dd HH:mm').format(_task!.completedAt!)}' : ''}

📊 统计信息:
- 总照片数: ${_task!.photos.length}
- 已拍摄: ${_task!.photosTaken}
- 已验证: ${_task!.photosVerified}

🎯 高准确率识别 (Google ML Kit专业版生成)

---
由装运卫士ML Kit V2专业版生成''';

      await Share.share(
        summary,
        subject: '装运卫士识别结果 -  ${_task!.template}',
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('分享失败: $e')),
      );
    }
  }

  void _continuePhotoSession() {
    context.go('/task/${_task!.id}/photos');
  }

  /// 🚨 显示错误并返回主页
  void _showError(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: '返回',
          textColor: Colors.white,
          onPressed: () {
            context.go('/home');
          },
        ),
      ),
    );

    // 设置加载状态为false并延迟返回
    setState(() {
      _isLoading = false;
    });

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        context.go('/home');
      }
    });
  }
}

Widget _buildParticipantsSection(TaskModel task) {
  if (task.participants.isEmpty) return SizedBox.shrink();
  final workers = task.participants
      .map((id) => allWorkers.firstWhere((w) => w.id == id,
          orElse: () =>
              WorkerInfo(id: id, name: id, role: '', warehouse: '', group: '')))
      .toList();
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        children: [
          Icon(Icons.people, color: ThemeColors.primary, size: 18),
          const SizedBox(width: 6),
          Text('参与人员（${workers.length}人）',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15)),
        ],
      ),
      const SizedBox(height: 8),
      Wrap(
        spacing: 8,
        runSpacing: 4,
        children: workers
            .map((w) => Chip(
                  label: Text(
                      '${w.name}${w.role.isNotEmpty ? '（${w.role}）' : ''}'),
                  backgroundColor: ThemeColors.primary.withOpacity(0.08),
                ))
            .toList(),
      ),
    ],
  );
}
