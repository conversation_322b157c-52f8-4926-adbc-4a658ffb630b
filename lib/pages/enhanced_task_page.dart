import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/core/providers/app_providers.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/template_config.dart';
import 'package:loadguard/pages/task/task_form_view.dart';
import 'package:loadguard/pages/task/task_mixed_view.dart';

import 'package:loadguard/pages/task/task_photo_view.dart';
import '../utils/theme_colors.dart';
import 'package:loadguard/services/logging_service.dart';
import '../utils/simple_navigation_helper.dart';
import 'package:loadguard/models/worker_info_data.dart';

/// 🚀 增强的任务页面 - 实现您的所有需求
/// 专门优化：继续拍照逻辑、悬浮完成按钮、识别结果展示、分享功能、混装批次优化
/// 基于Google ML Kit V2的化工标签专业识别系统
class EnhancedTaskPage extends ConsumerStatefulWidget {
  final String? taskId;
  final String? template;
  final String? type;

  const EnhancedTaskPage({
    super.key,
    this.taskId,
    this.template,
    this.type,
  });

  @override
  ConsumerState<EnhancedTaskPage> createState() {
    return _EnhancedTaskPageState();
  }
}

class _EnhancedTaskPageState extends ConsumerState<EnhancedTaskPage> {
  TaskModel? _currentTask;
  bool _isLoading = false;
  final ImagePicker _picker = ImagePicker();
  final MLKitTextRecognitionService _recognitionService =
      MLKitTextRecognitionService();
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    // 生命周期安全：build 后再加载任务
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTaskIfNeeded();
    });
  }

  /// 🔄 加载任务并保留已有数据 - 解决继续拍照问题
  void _loadTaskIfNeeded() async {

    Log.i('=== 任务加载开始 ===', tag: 'TaskPage');
    Log.i('widget.taskId: ${widget.taskId}', tag: 'TaskPage');
    Log.i('widget.type: ${widget.type}', tag: 'TaskPage');
    
    try {
      // 新建任务分支，兼容 taskId == null 或 'new'
      if (widget.taskId == null || widget.taskId == 'new') {
        // 从queryParameters获取模板信息
        final uri = GoRouter.of(context).routeInformationProvider.value.uri;
        final template = uri.queryParameters['template'];
        Log.i('URI查询参数: ${uri.queryParameters}', tag: 'TaskPage');
        // 🔒 模板参数校验
        if (template == null || template.isEmpty) {
          _showError('创建新任务需要指定模板');
          if (mounted) context.go('/template-selection');
          return;
        }
        Log.i('创建新任务，模板: $template', tag: 'TaskPage');
        // 🔧 修复：确保即使是新任务也调用setState触发重绘
        if (mounted) {
          setState(() {
            // 触发build()重绘
          });
        }
        return;
      }
      
      // 🔒 现有任务ID格式校验
      if (widget.taskId!.isEmpty ||
          !RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(widget.taskId!)) {
        _showError('任务ID格式无效');
        if (mounted) context.go('/template-selection');
        return;
      }
      
      Log.i('开始加载任务，taskId: ${widget.taskId}', tag: 'TaskPage');
      
      if (widget.taskId != null) {
        setState(() => _isLoading = true);
        
        try {
          if (!mounted) {
            return;
          }
          
          final taskService = ref.read(taskServiceProvider);

          Log.i('正在加载任务: ${widget.taskId}', tag: 'TaskPage');

          final task = taskService.getTaskById(widget.taskId!);
          
          if (task != null) {
            Log.i('任务加载成功: ${task.template} - ${task.productCode}', tag: 'TaskPage');
            Log.i('任务照片数量: ${task.photos.length}', tag: 'TaskPage');
            Log.i('任务照片详情: ${task.photos.map((p) => '${p.label}:${p.imagePath != null ? "有路径" : "无路径"}').join(', ')}', tag: 'TaskPage');
            
            await taskService.setCurrentTask(task);
            if (mounted) {
              setState(() {
                _currentTask = task;
              });
            }

            // 🔧 修复：确保照片配置完整
            _ensurePhotoConfiguration();

            Log.i('_currentTask已设置: ${_currentTask?.template}', tag: 'TaskPage');
          } else {
            Log.e('任务不存在: ${widget.taskId}', tag: 'TaskPage');
            Log.e('TaskService中的任务列表: ${taskService.tasks.map((t) => t.id).join(', ')}', tag: 'TaskPage');
            
            // 🔧 修复：先尝试刷新任务数据，再判断任务是否存在
            try {
              await taskService.refreshData();
              final refreshedTask = taskService.getTaskById(widget.taskId!);
              if (refreshedTask != null) {
                Log.i('刷新后任务加载成功: ${refreshedTask.template}', tag: 'TaskPage');
                await taskService.setCurrentTask(refreshedTask);
                if (mounted) {
                  setState(() {
                    _currentTask = refreshedTask;
                  });
                }

                // 🔧 修复：确保照片配置完整
                _ensurePhotoConfiguration();

                return;
              }
            } catch (e) {
              Log.e('刷新任务数据失败: $e', tag: 'TaskPage');
            }
            
            // 🔧 修复：任务确实不存在时，等待一段时间后再次尝试
            Log.w('任务不存在，等待500ms后重试...', tag: 'TaskPage');
            await Future.delayed(const Duration(milliseconds: 500));

            // 再次尝试加载任务
            final retryTask = taskService.getTaskById(widget.taskId!);
            if (retryTask != null) {
              Log.i('重试后任务加载成功: ${retryTask.template}', tag: 'TaskPage');
              await taskService.setCurrentTask(retryTask);
              if (mounted) {
                setState(() {
                  _currentTask = retryTask;
                });
              }
              _ensurePhotoConfiguration();
              return;
            }

            // 最终失败，显示错误信息
            _showError('任务不存在或已被删除');

            // 🔧 修复：即使任务不存在，也尝试从路由参数重建最小任务对象用于显示
            if (widget.taskId != null && widget.taskId!.isNotEmpty && widget.taskId != 'new') {
              try {
                // 创建一个临时的最小任务对象，避免完全的空白页面
                final fallbackTask = TaskModel(
                  id: widget.taskId!,
                  template: '未知模板',
                  productCode: '未知',
                  batchNumber: '未知',
                  quantity: 0,
                  photos: [],
                );

                if (mounted) {
                  setState(() {
                    _currentTask = fallbackTask;
                  });
                }

                Log.i('已创建临时任务对象用于显示: ${widget.taskId}', tag: 'TaskPage');
              } catch (e) {
                Log.e('创建临时任务对象失败: $e', tag: 'TaskPage');
              }
            }
          }
          
          // 确保照片配置完整
          if (widget.type == 'photos' && _currentTask != null) {
            _ensurePhotoConfiguration();
          }
        } catch (e) {
          Log.e('加载任务失败: $e', tag: 'TaskPage');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('加载任务失败: $e')),
            );
            context.go('/template-selection');
          }
        } finally {
          if (mounted) {
            setState(() {
              _isLoading = false;
            });
          }
        }
      }
    } catch (e) {
      Log.e('_loadTaskIfNeeded() 发生异常: $e', tag: 'TaskPage');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// 📸 确保照片配置完整 - 保留已有照片和识别结果
  void _ensurePhotoConfiguration() {
    if (_currentTask == null) return;

    // 🎯 使用正确的模板配置（来自TemplateConfig）
    final template = _currentTask!.template;
    final photoConfigs = TemplateConfig.getPhotoConfigs(template);

    // 检查并补充缺失的照片配置，保留已有数据
    for (final config in photoConfigs) {
      final existingIndex =
          _currentTask!.photos.indexWhere((p) => p.label == config.label);
      if (existingIndex == -1) {
        // 🔧 修复：使用完整的配置信息创建PhotoItem
        _currentTask!.photos.add(PhotoItem(
          label: config.label,
          configId: config.id,
          isRequired: config.isRequired,
          needRecognition: config.needRecognition,
          description: config.description,
        ));
      } else {
        // 🔧 修复：更新现有照片的配置信息
        final existingPhoto = _currentTask!.photos[existingIndex];
        existingPhoto.configId = config.id;
        existingPhoto.needRecognition = config.needRecognition;
        existingPhoto.description = config.description;
      }
    }

    Log.i('模板[$template]照片配置完成，共${photoConfigs.length}张照片', tag: 'TaskPage');
    Log.i('需要识别的照片数量: ${_currentTask!.photos.where((p) => p.needRecognition).length}', tag: 'TaskPage');
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _handleBackNavigation() async {
    // 🆕 自动保存当前任务状态，无弹窗直接返回
    if (_currentTask != null && !_currentTask!.isCompleted) {
      await _autoSaveTaskProgress();
      LoggingService.info('📱 任务状态已自动保存，支持稍后继续', tag: 'TaskNavigation');
    }

    // 执行导航返回
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    } else {
      context.go('/home');
    }
  }

  /// 🔄 自动保存任务进度
  Future<void> _autoSaveTaskProgress() async {
    try {
      final taskService = ref.read(taskServiceProvider);
      if (_currentTask != null) {
        await taskService.updateTask(_currentTask!);
        LoggingService.info('✅ 任务进度自动保存成功: ${_currentTask!.id}',
            tag: 'TaskNavigation');
      }
    } catch (e) {
      LoggingService.error('❌ 任务进度保存失败', error: e, tag: 'TaskNavigation');
    }
  }

  @override
  Widget build(BuildContext context) {
    
    Log.i('=== EnhancedTaskPage.build() ===', tag: 'TaskPage');
    Log.i('widget.taskId: ${widget.taskId}', tag: 'TaskPage');
    Log.i('widget.type: ${widget.type}', tag: 'TaskPage');
    Log.i('_currentTask: ${_currentTask?.id ?? "null"}', tag: 'TaskPage');
    Log.i('_isLoading: $_isLoading', tag: 'TaskPage');
    
    return SimpleNavigationHelper.buildStandardPage(
      onBackPressed: _handleBackNavigation,
      enableSwipeBack: true,
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: ThemeColors.primaryGradient,
          ),
          child: SafeArea(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(color: Colors.white))
                : _buildContent(),
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    
    Log.i('=== _buildContent开始 ===', tag: 'TaskPage');
    Log.i('widget.taskId: ${widget.taskId}', tag: 'TaskPage');
    Log.i('widget.type: ${widget.type}', tag: 'TaskPage');
    Log.i('_currentTask: ${_currentTask?.template}', tag: 'TaskPage');
    
    // 🎯 核心路由逻辑：根据taskId和type决定显示哪个视图
    if (widget.taskId == 'new') {
      final uri = GoRouter.of(context).routeInformationProvider.value.uri;
      final template = uri.queryParameters['template'];
      if (uri.queryParameters['type'] == 'single') {
        return TaskFormView(template: template);
      } else if (uri.queryParameters['type'] == 'mixed') {
        return TaskMixedView(template: template);
      }
    }

    // 🔧 修复：检查widget.type或查询参数中的type
    final uri = GoRouter.of(context).routeInformationProvider.value.uri;
    final typeFromQuery = uri.queryParameters['type'];
    final currentType = widget.type ?? typeFromQuery;
    
    Log.i('URI查询参数: ${uri.queryParameters}', tag: 'TaskPage');
    Log.i('typeFromQuery: $typeFromQuery', tag: 'TaskPage');
    Log.i('currentType: $currentType', tag: 'TaskPage');

    // 修复：当type为'photos'时，直接返回TaskPhotoView
    if (currentType == 'photos') {
      Log.i('返回TaskPhotoView', tag: 'TaskPage');
      Log.i('TaskPhotoView参数 - taskId: ${widget.taskId}, currentTask: ${_currentTask?.id ?? "null"}', tag: 'TaskPage');
      return TaskPhotoView(
        taskId: widget.taskId,
        currentTask: _currentTask,
      );
    }

    // 🔧 修复：当type为'details'或有任务ID时，显示任务详情页面
    if (currentType == 'details' || (widget.taskId != null && widget.taskId != 'new')) {
      Log.i('返回_buildTaskDetails', tag: 'TaskPage');
      return _buildTaskDetails();
    }

    // 其他情况返回任务详情页面
    Log.i('返回默认_buildTaskDetails', tag: 'TaskPage');
    return _buildTaskDetails();
  }

  /// 📄 任务详情页面（保持原有功能）
  Widget _buildTaskDetails() {
    if (_currentTask == null) {
      Log.e('_currentTask为null，无法显示任务详情', tag: 'TaskPage');
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.white.withOpacity(0.7),
            ),
            const SizedBox(height: 16),
            Text(
              '任务加载失败',
              style: TextStyle(
                fontSize: 20,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '任务ID: ${widget.taskId ?? "未知"}',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withOpacity(0.8),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                // 重新尝试加载任务
                _loadTaskIfNeeded();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('重新加载'),
              style: ElevatedButton.styleFrom(
                backgroundColor: ThemeColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
            const SizedBox(height: 12),
            TextButton(
              onPressed: () {
                context.go('/home');
              },
              child: Text(
                '返回首页',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                ),
              ),
            ),
          ],
        ),
      );
    }
    
    // 🔧 修复：检查是否为临时fallback任务
    final isTemporaryTask = _currentTask!.template == '未知模板';
    
    Log.i('显示任务详情: ${_currentTask!.template} - ${_currentTask!.productCode}', tag: 'TaskPage');
    
    return Column(
      children: [
        // _buildAppBar('${_currentTask!.template}任务详情'), // 已迁移到 TaskPhotoView 或可直接用 AppBar
        // 如需标题栏请在 Scaffold 外层实现
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 🔧 新增：如果是临时任务，显示警告卡片
                if (isTemporaryTask) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.only(bottom: 20),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.orange.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.warning_amber, color: Colors.orange, size: 24),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '任务数据加载异常',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.orange,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '无法加载完整的任务信息，显示基本信息。请尝试重新加载或联系技术支持。',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.orange.withOpacity(0.8),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                _buildTaskStatusCard(),
                const SizedBox(height: 20),
                _buildTaskActionsCard(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 📊 任务状态卡片 - 实时数据同步
  Widget _buildTaskStatusCard() {
    return Consumer(
      builder: (context, ref, child) {
        final taskService = ref.watch(taskServiceProvider);
        final task = taskService.currentTask ?? _currentTask;
        if (task == null) {
          return const SizedBox();
        }
        
        // 🔧 修复：获取实时数据，不缓存_currentTask
        final currentPhotosTaken = task.photosTaken;
        final currentPhotosVerified = task.photosVerified;
        final currentTotalPhotos = _getTotalPhotosRequired(task);
        
        return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题栏
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.assignment,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    '任务状态',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // 详细信息列表 - 使用实时数据
              _buildStatusItem('模板类型', task.template),
              _buildStatusItem('创建时间', _formatDateTime(task.createdAt)),
              _buildStatusItem('任务状态', '${_getTaskStatusIconForTask(task)} ${_getTaskStatusTextForTask(task)}', highlight: true),
              _buildStatusItem('任务类型', _getTaskTypeText(task)),
              _buildStatusItem('总计数量', '${task.quantity}托'),
              _buildStatusItem('拍照进度', '$currentPhotosTaken/$currentTotalPhotos张'),
              _buildStatusItem('验证通过', '$currentPhotosVerified张'),
              
              // 批次信息（如果有多个批次）
              if (task.batches.isNotEmpty) ...[
                const SizedBox(height: 8),
                ...task.batches.asMap().entries.map((entry) {
                  final index = entry.key;
                  final batch = entry.value;
                  // 🔧 动态更新批次识别数量
                  batch.updateRecognizedQuantity(task.photos);
                  return _buildStatusItem(
                    '批次${index + 1}',
                    '牌号${displayCode(batch.productCode)} 批号${batch.batchNumber} (${batch.recognizedQuantity}/${batch.plannedQuantity})',
                  );
                }),
              ],
              
              // 识别精度
              const SizedBox(height: 8),
              _buildStatusItem('识别精度', '90% (MLKit引擎)'),
            ],
          ),
        );
      },
    );
  }

  /// 🎬 任务操作卡片
  Widget _buildTaskActionsCard() {
    // 🔧 修复：检查是否为临时fallback任务
    final isTemporaryTask = _currentTask?.template == '未知模板';
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.play_circle_outline,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '任务操作',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 操作按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                context.push('/task-detail/${_currentTask!.id}?type=photos');
              },
              icon: const Icon(Icons.camera_alt, size: 20),
              label: const Text('继续拍照（专业版）'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.withOpacity(0.8),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem(String label, String value,
      {bool highlight = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  fontWeight: FontWeight.w500),
            ),
          ),
          const Text(': ', style: TextStyle(color: Colors.white54)),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 15,
                fontWeight: highlight ? FontWeight.bold : FontWeight.w500,
                color: highlight ? ThemeColors.success : Colors.white,
                letterSpacing: 0.5,
                shadows: highlight
                    ? [
                        Shadow(
                            color: ThemeColors.success.withOpacity(0.18),
                            blurRadius: 8)
                      ]
                    : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getTaskStatusText() {
    if (_currentTask == null) return '未知';

    // 🔧 修复：检查是否为临时fallback任务
    if (_currentTask!.template == '未知模板') {
      return '⚠️ 数据加载异常';
    }

    final requiredCount = _getRequiredCount();
    final completedCount = _getCompletedRequiredCount();

    if (completedCount >= requiredCount) {
      return '已完成 ($completedCount/$requiredCount)';
    } else {
      return '进行中 ($completedCount/$requiredCount)';
    }
  }

  String _getTaskStatusTextForTask(TaskModel task) {
    // 🔧 修复：检查是否为临时fallback任务
    if (task.template == '未知模板') {
      return '⚠️ 数据加载异常';
    }

    final requiredCount = task.photos.where((p) => p.isRequired).length;
    final completedCount = task.photos.where((p) => p.isRequired && p.imagePath != null).length;

    if (completedCount >= requiredCount) {
      return '已完成 ($completedCount/$requiredCount)';
    } else {
      return '进行中 ($completedCount/$requiredCount)';
    }
  }

  String _getTaskStatusIcon() {
    if (_currentTask == null) return '❓';
    
    final requiredCount = _getRequiredCount();
    final completedCount = _getCompletedRequiredCount();
    
    if (completedCount >= requiredCount) {
      return '✅';
    } else {
      return '🔄';
    }
  }

  String _getTaskStatusIconForTask(TaskModel task) {
    final requiredCount = task.photos.where((p) => p.isRequired).length;
    final completedCount = task.photos.where((p) => p.isRequired && p.imagePath != null).length;
    
    if (completedCount >= requiredCount) {
      return '✅';
    } else {
      return '🔄';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _getTaskTypeText(TaskModel task) {
    if (task.batches.length > 1) {
      return '混装任务 (${task.batches.length}个批次)';
    } else {
      return '单一任务';
    }
  }

  int _getTotalPhotosRequired(TaskModel task) {
    return task.photos.where((p) => p.isRequired).length;
  }

  /// 📊 统计方法
  int _getRequiredCount() {
    if (_currentTask == null) return 0;
    return _currentTask!.photos.where((p) => p.isRequired).length;
  }

  int _getCompletedRequiredCount() {
    if (_currentTask == null) return 0;
    return _currentTask!.photos
        .where((p) => p.isRequired && p.imagePath != null)
        .length;
  }

  // 在渲染产品牌号时，将牌号字符串做前缀替换
  String displayCode(String code) {
    if (code.startsWith('SAN-')) return code.replaceFirst('SAN-', '');
    if (code.startsWith('PS-')) return code.replaceFirst('PS-', '');
    return code;
  }

  /// 🚨 显示错误信息（不自动跳转）
  void _showError(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: '返回首页',
          textColor: Colors.white,
          onPressed: () {
            context.go('/home');
          },
        ),
      ),
    );
  }
}
