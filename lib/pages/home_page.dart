import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/rendering.dart';
import 'dart:ui';
import 'package:go_router/go_router.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/task_service.dart';
import '../services/enterprise_license_service.dart';
import '../models/task_model.dart';
import '../utils/theme_colors.dart';
import '../utils/gradient_extensions.dart';
import '../utils/responsive_helper.dart';
import '../utils/simple_navigation_helper.dart';
import '../widgets/themed_card.dart';
import '../widgets/premium_glass_card.dart';
import '../widgets/industrial_logo.dart';
import '../widgets/responsive_scaffold.dart';
import '../core/lifecycle_mixin.dart';
import '../core/providers/app_providers.dart';
import '../utils/gesture_navigation.dart';
import 'performance_stats_page.dart';
import 'about_page.dart';

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage>
    with TickerProviderStateMixin, WidgetsBindingObserver, LifecycleMixin<HomePage> {
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;

  final _licenseService = EnterpriseLicenseService();
  bool _isAdmin = false;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    // 初始化动画
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _slideAnimation = Tween<double>(begin: 30.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );

    // 添加应用生命周期监听器
    WidgetsBinding.instance.addObserver(this);

    // 初始化任务服务和许可证检查
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(taskServiceProvider).initialize();
      _animationController.forward();
      _pulseController.repeat(reverse: true);
      _checkAdminStatus();
    });
  }

  Future<void> _checkAdminStatus() async {
    final isSuperAdmin = await _licenseService.isSuperAdmin();
    if (mounted) {
      setState(() {
        _isAdmin = isSuperAdmin;
      });
    }
  }

  @override
  void onLifecycleDispose() {
    WidgetsBinding.instance.removeObserver(this);
    _animationController.dispose();
    _pulseController.dispose();
    _scaleController.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 当应用从后台返回前台时刷新数据
    if (state == AppLifecycleState.resumed) {
      _refreshData();
    }
  }

  /// 刷新主页数据
  Future<void> _refreshData() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      // 刷新任务服务数据
      await ref.read(taskServiceProvider).refreshData();

      // 重新检查管理员状态
      await _checkAdminStatus();

      // print('🔄 主页数据刷新完成');
    } catch (e) {
      // print('❌ 主页数据刷新失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureNavigation.wrapWithGesture(
      context: context,
      child: SimpleNavigationHelper.buildRootPage(
        exitMessage: '再按一次退出装运卫士',
        child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: ThemeColors.softGradient,
          ),
          child: RefreshIndicator(
            onRefresh: _refreshData,
            color: ThemeColors.primary,
            backgroundColor: ThemeColors.cardBackground,
            child: CustomScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              slivers: [
                _buildModernAppBar(context),
                SliverToBoxAdapter(
                  child: AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(0, _slideAnimation.value * 1.5),
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: _buildProfessionalBanner(),
                        ),
                      );
                    },
                  ),
                ),
                SliverToBoxAdapter(
                  child: AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(0, _slideAnimation.value * 2),
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: Stack(
                            children: [
                              _buildQuickStats(),
                              if (_isRefreshing)
                                Positioned.fill(
                                  child: Container(
                                    color: ThemeColors.glassBackground,
                                    child: const Center(
                                      child: CircularProgressIndicator(
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                ThemeColors.primary),
                                        strokeWidth: 3,
                                        backgroundColor: Colors.white24,
                                        strokeCap: StrokeCap.round,
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                SliverToBoxAdapter(
                  child: AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(0, _slideAnimation.value * 2.3),
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: _buildWorkloadManagement(),
                        ),
                      );
                    },
                  ),
                ),
                SliverToBoxAdapter(
                  child: AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(0, _slideAnimation.value * 2.5),
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: _buildRecentTasks(),
                        ),
                      );
                    },
                  ),
                ),
                const SliverToBoxAdapter(
                  child: SizedBox(height: 100),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: _buildThemedFAB(),
        ),
      ),
    );
  }

  // 🎨 响应式AppBar
  Widget _buildModernAppBar(BuildContext context) {
    final isDesktop = ResponsiveHelper.isDesktop(context);
    final responsiveHeight = ResponsiveHelper.isMobile(context) ? 120.0 : 140.0;

    return SliverAppBar(
      expandedHeight: responsiveHeight,
      floating: true,
      pinned: true,
      elevation: 0,
      backgroundColor: ThemeColors.primary,
      foregroundColor: ThemeColors.textOnGradient,
      centerTitle: !isDesktop, // 桌面端左对齐
      leadingWidth: 0,
      leading: null,
      actions: [
        // 🔐 安全管理中心入口（所有用户可见）
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            color: ThemeColors.info.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: ThemeColors.info.withOpacity(0.3)),
          ),
          child: IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              context.push('/security-management');
            },
            icon: Icon(Icons.security,
                color: ThemeColors.info,
                size: ResponsiveHelper.getResponsiveFontSize(context, 24)),
            tooltip: '安全管理中心',
          ),
        ),

        // 🛡️ 超级管理员控制面板（仅超级管理员可见）
        if (_isAdmin)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.withOpacity(0.3)),
            ),
            child: IconButton(
              onPressed: () {
                context.push('/admin-management');
              },
              icon: Icon(Icons.admin_panel_settings,
                  color: Colors.orange[300],
                  size: ResponsiveHelper.getResponsiveFontSize(context, 24)),
              tooltip: '超级管理员控制面板',
            ),
          ),

        // 删除手动刷新按钮，数据实时同步

        // 📊 性能检测入口（始终可见）
        IconButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const PerformanceStatsPage(),
              ),
            );
          },
          icon: Icon(Icons.speed,
              color: ThemeColors.iconLight,
              size: ResponsiveHelper.getResponsiveFontSize(context, 24)),
          tooltip: '性能统计',
        ),

        // 📖 关于装运卫士入口（始终可见）
        IconButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const AboutPage(),
              ),
            );
          },
          icon: Icon(Icons.info_outline,
              color: ThemeColors.iconLight,
              size: ResponsiveHelper.getResponsiveFontSize(context, 24)),
          tooltip: '关于装运卫士',
        ),
      ],
      flexibleSpace: LayoutBuilder(
        builder: (context, constraints) {
          final isCollapsed = constraints.maxHeight <= 80;
          return FlexibleSpaceBar(
            centerTitle: false,
            titlePadding: EdgeInsets.only(
              bottom: isCollapsed ? 16 : 20,
              left: 16,
              right: 16,
            ),
            title: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IndustrialLogo(
                  size: ResponsiveHelper.getResponsiveFontSize(context, 40),
                  primaryColor: ThemeColors.iconLight,
                  accentColor: ThemeColors.primaryLight,
                  showText: !ResponsiveHelper.isMobile(context), // 移动端隐藏文字
                ),
                SizedBox(
                    width: ResponsiveHelper.getResponsiveSpacing(context, 12)),
                ResponsiveText(
                  '装运卫士',
                  baseFontSize: 22,
                  fontWeight: FontWeight.w800,
                  color: ThemeColors.textOnGradient,
                  style: const TextStyle(letterSpacing: 0.5),
                ),
              ],
            ),
            background: Container(
              decoration: const BoxDecoration(
                gradient: ThemeColors.primaryGradient,
              ),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  decoration: BoxDecoration(
                    color: ThemeColors.glassBackground,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // 🏆 ML Kit V2专业版横幅 - 响应式设计
  Widget _buildProfessionalBanner() {
    return ResponsiveContainer(
      padding: ResponsiveHelper.getResponsivePadding(
          context, const EdgeInsets.fromLTRB(24, 16, 24, 16)),
      maxWidth: ResponsiveHelper.getResponsiveMaxWidth(
        context,
        mobileMaxWidth: double.infinity,
        tabletMaxWidth: 600,
        desktopMaxWidth: 800,
      ),
      child: PremiumGlassCard(
        type: GlassCardType.enterprise,
        padding: ResponsiveHelper.getResponsivePadding(
            context, const EdgeInsets.all(20)),
        applyInnerShadow: true,
        child: Column(
          children: [
            // 标题区域
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: ThemeColors.blueGradient,
                    borderRadius:
                        BorderRadius.circular(ThemeColors.radiusMedium),
                  ),
                  child: Icon(
                    Icons.workspace_premium,
                    color: ThemeColors.textOnGradient,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ResponsiveText(
                        'ML Kit V2专业版',
                        baseFontSize: 20,
                        fontWeight: FontWeight.bold,
                        style: GradientExtensions.createMetallicTextStyle(
                          fontSize: ResponsiveHelper.getResponsiveFontSize(
                              context, 20),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(
                          height: ResponsiveHelper.getResponsiveSpacing(
                              context, 4)),
                      ResponsiveText(
                        'Google AI引擎 | 12种专业算法',
                        baseFontSize: 12,
                        color: ThemeColors.textOnGradient.withOpacity(0.8),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 特性标签 - 基于真实系统优势
            Column(
              children: [
                Row(
                  children: [
                    Expanded(
                        child: _buildFeatureBadge(
                            '🎯 ML Kit V2引擎', ThemeColors.blueGradient)),
                    const SizedBox(width: 8),
                    Expanded(
                        child: _buildFeatureBadge(
                            '💎 12种专业算法', ThemeColors.greenGradient)),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                        child: _buildFeatureBadge(
                            '📱 完全本地处理', ThemeColors.orangeGradient)),
                    const SizedBox(width: 8),
                    Expanded(
                        child: _buildFeatureBadge(
                            '⚡ 化工专业优化', ThemeColors.tealGradient)),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 统计数据 - 使用微渐变数字
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _StatItem(value: '高精度识别', label: 'AI智能引擎', useGradient: true),
                _StatItem(value: '本地识别', label: '完全离线', useGradient: true),
                _StatItem(value: '<2s', label: '平均响应', useGradient: true),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureBadge(String text, LinearGradient gradient) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: BorderRadius.circular(ThemeColors.radiusLarge),
      ),
      child: Text(
        text,
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w600,
          color: ThemeColors.textOnGradient,
        ),
      ),
    );
  }

  // 🚀 响应式悬浮按钮
  Widget _buildThemedFAB() {
    final isMobile = ResponsiveHelper.isMobile(context);

    return Container(
      decoration: BoxDecoration(
        gradient: ThemeColors.primaryButtonGradient,
        borderRadius: BorderRadius.circular(ThemeColors.radiusRound),
        boxShadow: ThemeColors.floatingButtonShadow,
      ),
      child: isMobile
          ? FloatingActionButton(
              onPressed: _navigateToTemplateSelection,
              backgroundColor: Colors.transparent,
              elevation: 0,
              child: Icon(
                Icons.photo_camera,
                color: ThemeColors.textOnGradient,
                size: ResponsiveHelper.getResponsiveFontSize(context, 28),
              ),
            )
          : FloatingActionButton.extended(
              onPressed: _navigateToTemplateSelection,
              backgroundColor: Colors.transparent,
              elevation: 0,
              label: ResponsiveText(
                '新建任务',
                baseFontSize: 16,
                fontWeight: FontWeight.w600,
                color: ThemeColors.textOnGradient,
              ),
              icon: Icon(
                Icons.photo_camera,
                color: ThemeColors.textOnGradient,
                size: ResponsiveHelper.getResponsiveFontSize(context, 24),
              ),
            ),
    );
  }

  // 📊 快速统计 - 响应式设计
  Widget _buildQuickStats() {
    return Consumer(
      builder: (context, ref, child) {
        final tasksAsync = ref.watch(taskNotifierProvider);

        return tasksAsync.when(
          data: (tasks) {
            final completedTasks = tasks.where((t) => t.isCompleted).length;
            final totalPhotos =
                tasks.fold<int>(0, (sum, task) => sum + task.photosTaken);
            final verifiedPhotos =
                tasks.fold<int>(0, (sum, task) => sum + task.photosVerified);

            // 🔥 ML Kit V2专业版 - 优化的响应式统计卡片布局
            return Container(
          margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Row(
            children: [
              Expanded(
                child: _buildStatsCard(
                  '已完成任务',
                  '$completedTasks',
                  Icons.check_circle_outline,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatsCard(
                  '总拍照片',
                  '$totalPhotos',
                  Icons.photo_camera_outlined,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatsCard(
                  '验证通过',
                  '$verifiedPhotos',
                  Icons.verified_outlined,
                  Colors.orange,
                ),
              ),
            ],
          ),
        );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('加载失败: $error'),
          ),
        );
      },
    );
  }

  Widget _buildStatsCard(String title, String value, IconData icon, Color color,
      {VoidCallback? onTap}) {
    Widget cardContent = Container(
      height: 85, // 减少高度以适应手机屏幕
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A3E).withOpacity(0.8),
        borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
        border: Border.all(color: color.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(6.0), // 减少内边距
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 20, // 减少图标大小
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 14, // 减少字体大小
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
            const SizedBox(height: 2),
            Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 9, // 减少字体大小
                color: ThemeColors.textOnGradient.withOpacity(0.8),
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ],
        ),
      ),
    );

    if (onTap != null) {
      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              cardContent,
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    Icons.touch_app,
                    size: 12,
                    color: color,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return cardContent;
  }

  // 📋 最近任务 - 新主题设计
  Widget _buildRecentTasks() {
    return Consumer(
      builder: (context, ref, child) {
        final tasksAsync = ref.watch(taskNotifierProvider);

        return tasksAsync.when(
          data: (tasks) {
            final taskList = tasks.take(3).toList();

        if (taskList.isEmpty) {
          return Container(
            margin: const EdgeInsets.fromLTRB(24, 0, 24, 16),
            child: ThemedCard(
              type: CardType.glass,
              child: Column(
                children: [
                  Icon(
                    Icons.assignment_outlined,
                    size: 48,
                    color: ThemeColors.iconSecondary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '暂无任务',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: ThemeColors.textOnGradient,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '点击下方按钮创建第一个任务',
                    style: TextStyle(
                      fontSize: 14,
                      color: ThemeColors.textOnGradient.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return Container(
          margin: const EdgeInsets.fromLTRB(24, 0, 24, 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 4, bottom: 12),
                child: Text(
                  '最近任务',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
              ...taskList.map((task) => _buildTaskCard(task)),
            ],
          ),
        );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('加载失败: $error'),
          ),
        );
      },
    );
  }

  Widget _buildTaskCard(TaskModel task) {
    final statusColor =
        task.isCompleted ? ThemeColors.success : ThemeColors.warning;
    final statusText = task.isCompleted ? '已完成' : '进行中';
    final progress =
        task.photos.isNotEmpty ? task.photosTaken / task.photos.length : 0.0;

    return ThemedCard(
      type: CardType.glass,
      margin: const EdgeInsets.only(bottom: 12),
      onTap: () => _navigateToTaskPage(task),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.getStatusGradient(
                      task.isCompleted ? 'success' : 'warning'),
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.local_shipping_outlined,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            task.template,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: ThemeColors.textOnGradient,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: statusColor.withOpacity(0.2),
                            borderRadius:
                                BorderRadius.circular(ThemeColors.radiusMedium),
                          ),
                          child: Text(
                            statusText,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: statusColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Text(
                      '${task.productCode} | ${task.batchNumber}',
                      style: TextStyle(
                        fontSize: 14,
                        color: ThemeColors.textOnGradient.withOpacity(0.8),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                Icons.access_time,
                color: ThemeColors.iconSecondary,
                size: 16,
              ),
              const SizedBox(width: 6),
              Text(
                task.createdAt.toString().split(' ')[0],
                style: TextStyle(
                  fontSize: 12,
                  color: ThemeColors.textOnGradient.withOpacity(0.8),
                ),
              ),
              const SizedBox(width: 12),
              Icon(
                Icons.photo_camera,
                color: ThemeColors.iconSecondary,
                size: 16,
              ),
              const SizedBox(width: 6),
              Text(
                '${task.photosTaken}件',
                style: TextStyle(
                  fontSize: 12,
                  color: ThemeColors.textOnGradient.withOpacity(0.8),
                ),
              ),
              const Spacer(),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: ThemeColors.glassBackground,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Text(
                  '${(progress * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 🚀 导航方法
  void _navigateToTemplateSelection() {
    HapticFeedback.mediumImpact(); // 添加触觉反馈
    context.push('/template-selection');
  }

  void _navigateToTaskPage(TaskModel task) {
    HapticFeedback.lightImpact(); // 添加触觉反馈
    print('🚀 [HomePage] 点击任务，准备跳转');
    print('🚀 [HomePage] 任务ID: ${task.id}');
    print('🚀 [HomePage] 任务模板: ${task.template}');
    print('🚀 [HomePage] 跳转路径: /task-detail/${task.id}?type=details');
    // 🔧 修复：添加type参数，确保能正确显示任务状态页面
    context.push('/task-detail/${task.id}?type=details');
  }

  // 📊 数据统计模块
  Widget _buildWorkloadManagement() {
    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 4, bottom: 12),
            child: Text(
              '数据统计',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: ThemeColors.textOnGradient,
              ),
            ),
          ),
          _buildNavigationCard(
            '工作量统计',
            Icons.analytics_outlined,
            Colors.green,
            double.infinity,
            onTap: () async {
              // 在跳转前刷新数据确保统计准确
              await _refreshData();
              if (mounted) {
                context.push('/workload-statistics');
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationCard(
      String title, IconData icon, Color color, double width,
      {VoidCallback? onTap}) {
    Widget cardContent = Container(
      height: 72,
      width: width,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color.withOpacity(0.18), color.withOpacity(0.08)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.10),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 18),
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: color.withOpacity(0.18),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 28,
            ),
          ),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: ThemeColors.textOnGradient,
              ),
            ),
          ),
          InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: onTap,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Text(
                    '查看详情',
                    style: TextStyle(
                      fontSize: 14,
                      color: color,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(Icons.arrow_forward_ios, size: 16, color: color),
                ],
              ),
            ),
          ),
        ],
      ),
    );
    return Material(
      color: Colors.transparent,
      child: cardContent,
    );
  }
}

// 统计项组件 - 新主题设计
class _StatItem extends StatelessWidget {
  final String value;
  final String label;
  final Color? color;
  final bool useGradient;

  const _StatItem({
    required this.value,
    required this.label,
    this.color,
    this.useGradient = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        useGradient
            ? Text(
                value,
                style: GradientExtensions.createTechDataTextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
              )
            : Text(
                value,
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: color ?? Colors.white,
                ),
              ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: ThemeColors.textOnGradient.withOpacity(0.8),
          ),
        ),
      ],
    );
  }
}
