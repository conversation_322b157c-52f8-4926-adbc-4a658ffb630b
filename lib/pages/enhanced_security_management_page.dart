import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/app_security_service.dart';
import '../services/enterprise_license_service.dart';
import '../utils/theme_colors.dart';
import '../utils/simple_navigation_helper.dart';
import '../widgets/themed_button.dart';
import '../widgets/themed_card.dart';
import '../widgets/industrial_logo.dart';
import '../core/lifecycle_mixin.dart';
import '../core/providers/app_providers.dart';

/// 🔧 装运卫士 - 便利化安全管理中心
/// 统一UI主题，便利的管理员激活和设备授权管理
class EnhancedSecurityManagementPage extends ConsumerStatefulWidget {
  const EnhancedSecurityManagementPage({super.key});

  @override
  ConsumerState<EnhancedSecurityManagementPage> createState() =>
      _EnhancedSecurityManagementPageState();
}

class _EnhancedSecurityManagementPageState
    extends ConsumerState<EnhancedSecurityManagementPage>
    with LifecycleMixin<EnhancedSecurityManagementPage> {
  final TextEditingController _activationCodeController =
      TextEditingController();
  final TextEditingController _deviceLabelController = TextEditingController();
  final TextEditingController _customDaysController = TextEditingController();

  bool _isLoading = false;
  Map<String, dynamic> _licenseStatus = {};
  String _deviceId = '';
  UserRole _currentUserRole = UserRole.trial;

  // 授权期限选项
  final List<AuthorizationPeriod> _periodOptions = [
    AuthorizationPeriod(
        days: 30, label: '30天标准版', color: Colors.green, description: '适合短期项目'),
    AuthorizationPeriod(
        days: 60, label: '60天加强版', color: Colors.blue, description: '适合中期合作'),
    AuthorizationPeriod(
        days: 90, label: '90天专业版', color: Colors.orange, description: '适合季度授权'),
    AuthorizationPeriod(
        days: 180,
        label: '半年企业版',
        color: Colors.deepOrange,
        description: '适合长期合作'),
    AuthorizationPeriod(
        days: 365, label: '一年旗舰版', color: Colors.red, description: '适合年度协议'),
    AuthorizationPeriod(
        days: -1, label: '自定义设置', color: Colors.grey, description: '灵活天数设置'),
  ];

  AuthorizationPeriod _selectedPeriod = AuthorizationPeriod(
      days: 90, label: '90天专业版', color: Colors.orange, description: '适合季度授权');
  String _generatedActivationCode = '';
  String _todayAdminCode = '';
  List<Map<String, dynamic>> _recentActivations = [];
  Map<String, dynamic> _deviceStats = {};

  @override
  void initState() {
    super.initState();
    _loadSecurityInfo();
    _generateTodayAdminCode();
  }

  @override
  void onLifecycleDispose() {
    _activationCodeController.dispose();
    _deviceLabelController.dispose();
    _customDaysController.dispose();
  }

  Future<void> _loadSecurityInfo() async {
    setState(() => _isLoading = true);

    try {
      final deviceId = await AppSecurityService.getDeviceId();
      final licenseStatus = await AppSecurityService.checkLicenseStatus();
      final userRole = await AppSecurityService.getUserRole();
      final activationHistory = await AppSecurityService.getActivationHistory();

      setState(() {
        _deviceId = deviceId;
        _licenseStatus = licenseStatus;
        _currentUserRole = userRole;
        _recentActivations = activationHistory;
        _deviceStats = {
          'totalActivations': activationHistory.length,
          'activeDevices':
              activationHistory.where((a) => !a.containsKey('error')).length,
        };
      });
    } catch (e) {
      _showMessage('加载安全信息失败: $e', false);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _generateTodayAdminCode() {
    // 🔐 修复：不再自动生成管理员码，改为显示提示
    final adminCodeHint = AppSecurityService.getMasterCodeHint();
    setState(() {
      _todayAdminCode = adminCodeHint;
    });
  }

  Future<void> _activateWithCode() async {
    final code = _activationCodeController.text.trim();
    if (code.isEmpty) {
      _showMessage('请输入激活码', false);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final success = await AppSecurityService.validateActivationCode(code);
      if (success) {
        _showMessage('激活成功！正在刷新状态...', true);
        await _loadSecurityInfo();
        _activationCodeController.clear();
      } else {
        _showMessage('激活码无效或已过期', false);
      }
    } catch (e) {
      _showMessage('激活失败: $e', false);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateActivationCode() async {
    final deviceLabel = _deviceLabelController.text.trim();
    if (deviceLabel.isEmpty) {
      _showMessage('请输入设备标识', false);
      return;
    }

    int days = _selectedPeriod.days;
    if (days == -1) {
      // 自定义天数
      final customDays = int.tryParse(_customDaysController.text.trim());
      if (customDays == null || customDays <= 0) {
        _showMessage('请输入有效的自定义天数', false);
        return;
      }
      days = customDays;
    }

    setState(() => _isLoading = true);

    try {
      // 使用新的带期限激活码生成方法
      final activationCode =
          await AppSecurityService.generateTimedActivationCode(
        days: days,
        customLabel: deviceLabel,
      );

      setState(() {
        _generatedActivationCode = activationCode;
      });

      _showMessage('激活码生成成功！有效期：$days天', true);
      await _loadSecurityInfo(); // 刷新历史记录
    } catch (e) {
      _showMessage('生成激活码失败: $e', false);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showMessage(String message, bool isSuccess) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isSuccess ? Icons.check_circle : Icons.error,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: isSuccess ? ThemeColors.success : Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    _showMessage('已复制到剪贴板', true);
  }

  void _shareQRCode(String code) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('激活码二维码'),
        content: SizedBox(
          width: 200,
          height: 200,
          child: QrImageView(
            data: code,
            version: QrVersions.auto,
            size: 200.0,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
          TextButton(
            onPressed: () {
              _copyToClipboard(code);
              Navigator.of(context).pop();
            },
            child: const Text('复制'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SimpleNavigationHelper.buildStandardPage(
      onBackPressed: () {
        HapticFeedback.lightImpact();
        if (Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
        } else {
          context.go('/home');
        }
      },
      enableSwipeBack: true,
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: ThemeColors.primaryGradient,
          ),
          child: SafeArea(
            child: Column(
              children: [
                _buildAppBar(),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        _buildCurrentStatusCard(),
                        const SizedBox(height: 16),
                        if (_currentUserRole != UserRole.superAdmin) ...[
                          // 试用期和激活用户界面：只显示个人信息
                          _buildUserInfoCard(),
                          const SizedBox(height: 16),
                          _buildActivationCard(),
                          const SizedBox(height: 16),
                          _buildDeviceDetailsCard(),
                          const SizedBox(height: 16),
                          _buildUserLimitationNotice(),
                        ] else ...[
                          // 超级管理员界面：完整管理功能
                          _buildAdminDashboard(),
                          const SizedBox(height: 16),
                          _buildDeviceAuthorizationCard(),
                          const SizedBox(height: 16),
                          _buildActivationHistoryCard(),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return ThemedCard(
      type: CardType.glass,
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: ThemeColors.primaryButtonGradient,
              borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
            ),
            child: Icon(
              Icons.security,
              color: ThemeColors.textOnGradient,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '🔧 装运卫士安全中心',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w800,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
                Text(
                  '便利化安全管理和设备授权',
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeColors.textOnGradient.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          IndustrialLogo(
            size: 32,
            primaryColor: ThemeColors.textOnGradient,
            accentColor: ThemeColors.primaryLight,
            showText: false,
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentStatusCard() {
    final isValid = _licenseStatus['isValid'] ?? false;
    final role = _currentUserRole;
    final statusMessage = _licenseStatus['message'] ?? '状态未知';
    final remainingDays = _licenseStatus['remainingDays'] ?? 0;

    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: role == UserRole.superAdmin
                      ? ThemeColors.successButtonGradient
                      : (isValid
                          ? ThemeColors.blueGradient
                          : ThemeColors.warningButtonGradient),
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  role == UserRole.superAdmin
                      ? Icons.admin_panel_settings
                      : (isValid ? Icons.verified_user : Icons.warning),
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '当前状态',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: ThemeColors.textOnGradient,
                      ),
                    ),
                    Text(
                      _getUserRoleDisplay(role),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: role == UserRole.superAdmin
                            ? ThemeColors.success
                            : ThemeColors.textOnGradient.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
              border: Border.all(
                color: isValid
                    ? Colors.green.withOpacity(0.3)
                    : Colors.orange.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  statusMessage,
                  style: TextStyle(
                    color: ThemeColors.textOnGradient,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (remainingDays > 0) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.access_time,
                          color: ThemeColors.textOnGradient.withOpacity(0.8),
                          size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '剩余 $remainingDays 天',
                        style: TextStyle(
                          fontSize: 12,
                          color: ThemeColors.textOnGradient.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.smartphone,
                        color: ThemeColors.textOnGradient.withOpacity(0.8),
                        size: 16),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        '设备ID: ${_deviceId.isNotEmpty ? _deviceId.substring(0, 8) : "未知"}...',
                        style: TextStyle(
                          fontSize: 12,
                          fontFamily: 'monospace',
                          color: ThemeColors.textOnGradient.withOpacity(0.8),
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _copyToClipboard(_deviceId),
                      icon: Icon(
                        Icons.copy,
                        color: ThemeColors.textOnGradient.withOpacity(0.8),
                        size: 16,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivationCard() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.yellowGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.vpn_key,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '🔑 激活码输入',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
              border: Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: TextField(
              controller: _activationCodeController,
              style: TextStyle(color: ThemeColors.textOnGradient),
              decoration: InputDecoration(
                hintText: '输入激活码：USER-XXXXX-XXXXX-XXXXX-XXXXX',
                hintStyle: TextStyle(
                  color: ThemeColors.textOnGradient.withOpacity(0.6),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
                suffixIcon: Icon(
                  Icons.key,
                  color: ThemeColors.textOnGradient.withOpacity(0.8),
                ),
              ),
              onSubmitted: (_) => _activateWithCode(),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ThemedButton(
              text: _isLoading ? '激活中...' : '🚀 激活应用',
              onPressed: _isLoading ? null : _activateWithCode,
              gradient: ThemeColors.primaryButtonGradient,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            '💡 激活码类型说明：\n'
            '• 🔑 管理员码：ADMIN-XXXXX-XXXXX（永久管理权限）\n'
            '• 👤 用户码：USER-XXXXX-XXXXX...（期限制用户权限）\n'
            '• 🔄 试用码：自动获得7天免费试用',
            style: TextStyle(
              fontSize: 12,
              color: ThemeColors.textOnGradient.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTodayAdminCodeCard() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.warningButtonGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.admin_panel_settings,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '👑 管理员激活说明',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: ThemeColors.warning.withOpacity(0.2),
              borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
              border: Border.all(color: ThemeColors.warning.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.security, color: ThemeColors.warning, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      '🔐 安全提示：',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: ThemeColors.textOnGradient,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  '• 管理员激活码格式：${_todayAdminCode}\n'
                  '• 只有专属的主激活码才能激活管理员权限\n'
                  '• 每个应用只能有一个主管理设备\n'
                  '• 请在上方激活码输入框中输入正确的主激活码\n'
                  '• 激活后即可为其他设备生成用户激活码',
                  style: TextStyle(
                    fontSize: 13,
                    color: ThemeColors.textOnGradient.withOpacity(0.9),
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '⚠️ 重要：主激活码只能使用一次激活管理员，请妥善保管！',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: ThemeColors.textOnGradient,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdminDashboard() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.successButtonGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.dashboard,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '👑 管理员控制台',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 快速操作按钮
          Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  icon: Icons.shield_outlined,
                  label: '查看主设备\n信息',
                  onPressed: () => _showMasterDeviceInfo(),
                  gradient: ThemeColors.successButtonGradient,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickActionButton(
                  icon: Icons.group_add,
                  label: '批量生成\n用户码',
                  onPressed: () => _showBatchGenerationDialog(),
                  gradient: ThemeColors.blueGradient,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickActionButton(
                  icon: Icons.devices,
                  label: '管理已\n授权设备',
                  onPressed: () => context.push('/admin-management'),
                  gradient: ThemeColors.purpleGradient,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 统计概览
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '📊 概览统计',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem(
                        '已授权设备', '${_deviceStats['totalActivations'] ?? 0}台'),
                    _buildStatItem(
                        '活跃设备', '${_deviceStats['activeDevices'] ?? 0}台'),
                    _buildStatItem('今日管理员码', _todayAdminCode.split('-').last),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Gradient gradient,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
        ),
        child: Column(
          children: [
            Icon(icon, color: ThemeColors.textOnGradient, size: 20),
            const SizedBox(height: 4),
            Text(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 10,
                color: ThemeColors.textOnGradient,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ThemeColors.textOnGradient,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: ThemeColors.textOnGradient.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildDeviceAuthorizationCard() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.orangeGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.devices,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '📱 设备授权中心',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 设备标识输入
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
              border: Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: TextField(
              controller: _deviceLabelController,
              style: TextStyle(color: ThemeColors.textOnGradient),
              decoration: InputDecoration(
                hintText: '为其他设备生成激活码：输入目标设备的识别标识（如：张三的手机、办公室平板等）',
                hintStyle: TextStyle(
                  color: ThemeColors.textOnGradient.withOpacity(0.6),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
                prefixIcon: Icon(
                  Icons.devices_other,
                  color: ThemeColors.textOnGradient.withOpacity(0.8),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 授权期限选择
          Text(
            '⏰ 授权期限选择：',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ThemeColors.textOnGradient,
            ),
          ),

          const SizedBox(height: 8),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _periodOptions.map((period) {
              final isSelected = _selectedPeriod.days == period.days;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedPeriod = period;
                  });
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? period.color.withOpacity(0.3)
                        : Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected
                          ? period.color
                          : Colors.white.withOpacity(0.3),
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Text(
                    period.label,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      color: ThemeColors.textOnGradient,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          // 自定义天数输入
          if (_selectedPeriod.days == -1) ...[
            const SizedBox(height: 12),
            Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                border: Border.all(color: Colors.white.withOpacity(0.3)),
              ),
              child: TextField(
                controller: _customDaysController,
                keyboardType: TextInputType.number,
                style: TextStyle(color: ThemeColors.textOnGradient),
                decoration: InputDecoration(
                  hintText: '输入自定义天数（1-999天）',
                  hintStyle: TextStyle(
                    color: ThemeColors.textOnGradient.withOpacity(0.6),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                  prefixIcon: Icon(
                    Icons.edit_calendar,
                    color: ThemeColors.textOnGradient.withOpacity(0.8),
                  ),
                ),
              ),
            ),
          ],

          const SizedBox(height: 16),

          // 生成按钮
          SizedBox(
            width: double.infinity,
            child: ThemedButton(
              text: _isLoading ? '生成中...' : '🎯 生成激活码',
              onPressed: _isLoading ? null : _generateActivationCode,
              gradient: ThemeColors.orangeGradient,
            ),
          ),

          // 生成的激活码显示
          if (_generatedActivationCode.isNotEmpty) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ThemeColors.success.withOpacity(0.2),
                borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
                border: Border.all(color: ThemeColors.success.withOpacity(0.3)),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle,
                          color: ThemeColors.success, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        '🎯 已生成设备激活码（请发送给目标设备用户）：',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: ThemeColors.textOnGradient,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Text(
                          _generatedActivationCode,
                          style: TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: ThemeColors.textOnGradient,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            ThemedButton(
                              text: '📋 复制',
                              onPressed: () =>
                                  _copyToClipboard(_generatedActivationCode),
                              type: ButtonType.secondary,
                            ),
                            ThemedButton(
                              text: '📤 分享',
                              onPressed: () =>
                                  _shareQRCode(_generatedActivationCode),
                              type: ButtonType.secondary,
                            ),
                            ThemedButton(
                              text: '🔗 二维码',
                              onPressed: () =>
                                  _shareQRCode(_generatedActivationCode),
                              type: ButtonType.secondary,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActivationHistoryCard() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.tealGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.history,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '📋 最近授权记录',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
              IconButton(
                onPressed: _loadSecurityInfo,
                icon: Icon(
                  Icons.refresh,
                  color: ThemeColors.textOnGradient.withOpacity(0.8),
                ),
                tooltip: '刷新记录',
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_recentActivations.isEmpty) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
              ),
              child: Text(
                '暂无授权记录',
                style: TextStyle(
                  color: ThemeColors.textOnGradient.withOpacity(0.7),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ] else ...[
            Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
                border: Border.all(color: Colors.white.withOpacity(0.2)),
              ),
              child: ListView.separated(
                padding: const EdgeInsets.all(8),
                itemCount: _recentActivations.take(5).length,
                separatorBuilder: (context, index) => Divider(
                  color: Colors.white.withOpacity(0.2),
                  height: 1,
                ),
                itemBuilder: (context, index) {
                  final record = _recentActivations[index];
                  if (record.containsKey('error')) {
                    return ListTile(
                      leading:
                          const Icon(Icons.error, color: Colors.red, size: 20),
                      title: Text(
                        record['error'],
                        style: const TextStyle(color: Colors.red, fontSize: 12),
                      ),
                    );
                  }

                  return ListTile(
                    dense: true,
                    leading: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: ThemeColors.success.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Icon(
                        Icons.vpn_key,
                        color: ThemeColors.success,
                        size: 16,
                      ),
                    ),
                    title: Text(
                      '设备: ${record['deviceId']?.toString().substring(0, 8) ?? 'Unknown'}...',
                      style: TextStyle(
                        color: ThemeColors.textOnGradient,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: Text(
                      '${record['licenseType'] ?? '未知类型'} | ${record['role'] ?? '未知角色'}',
                      style: TextStyle(
                        color: ThemeColors.textOnGradient.withOpacity(0.7),
                        fontSize: 11,
                      ),
                    ),
                    trailing: Text(
                      DateTime.tryParse(record['timestamp'] ?? '')
                              ?.toString()
                              .split(' ')[0] ??
                          '未知日期',
                      style: TextStyle(
                        color: ThemeColors.textOnGradient.withOpacity(0.6),
                        fontSize: 10,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showMasterDeviceInfo() async {
    final prefs = await SharedPreferences.getInstance();
    final masterDevice = prefs.getString('master_device_id') ?? '未设置';
    final currentDevice = await AppSecurityService.getDeviceId();
    final isCurrentMaster = masterDevice == currentDevice;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🔐 主管理设备信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('主管理设备ID：'),
            Text(
              masterDevice.length > 8
                  ? '${masterDevice.substring(0, 8)}...'
                  : masterDevice,
              style: const TextStyle(
                  fontFamily: 'monospace', fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text('当前设备状态：'),
            Text(
              isCurrentMaster ? '✅ 本机是主管理设备' : '❌ 本机不是主管理设备',
              style: TextStyle(
                color: isCurrentMaster ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '💡 提示：只有主管理设备才能为其他设备生成激活码',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showBatchGenerationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('批量生成激活码'),
        content: const Text('批量生成功能正在开发中，敬请期待！\n\n目前请使用单个设备授权功能。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  String _getUserRoleDisplay(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return '👑 超级管理员';
      case UserRole.activated:
        return '👤 激活用户';
      case UserRole.trial:
        return '🔄 试用用户';
      default:
        return '❓ 未知角色';
    }
  }

  /// 🔒 构建用户权限限制提示
  Widget _buildUserLimitationNotice() {
    return ThemedCard(
      type: CardType.glass,
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: ThemeColors.info,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '权限说明',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ThemeColors.textHighContrast,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ThemeColors.info.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: ThemeColors.info.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '当前权限范围：',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: ThemeColors.textHighContrast,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildPermissionItem('✅ 查看个人激活信息', true),
                  _buildPermissionItem('✅ 查看设备详情', true),
                  _buildPermissionItem('✅ 使用应用完整功能', true),
                  const SizedBox(height: 8),
                  Text(
                    '受限功能：',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: ThemeColors.textHighContrast,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildPermissionItem('❌ 为其他设备生成激活码', false),
                  _buildPermissionItem('❌ 查看其他设备激活记录', false),
                  _buildPermissionItem('❌ 管理设备授权', false),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: ThemeColors.warning.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.lightbulb_outline,
                          color: ThemeColors.warning,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '如需管理功能，请联系超级管理员升级权限',
                            style: TextStyle(
                              fontSize: 12,
                              color: ThemeColors.textOnGradient.withOpacity(0.9),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionItem(String text, bool allowed) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 12,
                color: allowed
                    ? ThemeColors.success
                    : ThemeColors.textOnGradient.withOpacity(0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.blueGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.person,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '👤 我的设备信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 设备ID信息
          _buildInfoRow('设备ID', _deviceId),
          const SizedBox(height: 8),
          _buildInfoRow(
              '角色', _currentUserRole == UserRole.trial ? '试用用户' : '激活用户'),
          const SizedBox(height: 8),
          _buildInfoRow('剩余天数', '${_licenseStatus['remainingDays'] ?? 0}天'),

          if (_currentUserRole == UserRole.trial) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.orange.withOpacity(0.3),
                ),
              ),
              child: Text(
                '💡 试用期提示：您正在使用7天免费试用期，到期后请联系管理员获取激活码',
                style: TextStyle(
                  fontSize: 13,
                  color: ThemeColors.textOnGradient,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDeviceDetailsCard() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.purpleGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.smartphone,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '📱 激活详情',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 激活信息
          _buildInfoRow(
              '激活状态', _licenseStatus['isValid'] ?? false ? '✅ 已激活' : '❌ 未激活'),
          const SizedBox(height: 8),
          _buildInfoRow('许可类型', _licenseStatus['licenseType'] ?? '未知'),
          const SizedBox(height: 8),
          _buildInfoRow('激活时间', _formatDate(_licenseStatus['activationTime'])),
          const SizedBox(height: 8),
          _buildInfoRow('到期时间', _formatDate(_licenseStatus['expiryTime'])),

          const SizedBox(height: 16),

          if (!(_licenseStatus['isValid'] ?? false)) ...[
            ThemedButton(
              text: '联系管理员',
              icon: Icons.support_agent,
              onPressed: () {
                _showMessage('请联系您的管理员获取激活码', false);
              },
              gradient: ThemeColors.orangeGradient,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: ThemeColors.textOnGradient.withOpacity(0.7),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: ThemeColors.textOnGradient,
          ),
        ),
      ],
    );
  }

  String _formatDate(dynamic timestamp) {
    if (timestamp == null) return '未知';
    try {
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp as int);
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    } catch (e) {
      return '未知';
    }
  }
}

/// 授权期限选项数据类
class AuthorizationPeriod {
  final int days;
  final String label;
  final Color color;
  final String description;

  AuthorizationPeriod({
    required this.days,
    required this.label,
    required this.color,
    required this.description,
  });
}
