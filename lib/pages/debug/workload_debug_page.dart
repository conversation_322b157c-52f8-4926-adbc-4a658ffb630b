import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/task_service.dart';
import '../../services/workload_statistics_service.dart';
import '../../services/workload_calculation_service.dart';
import '../../repositories/task_repository.dart';
import '../../providers/task_providers.dart';
import '../../models/worker_info_data.dart';
import '../../utils/theme_colors.dart';

/// 工作量调试页面
/// 用于测试和验证工作量统计修复效果
class WorkloadDebugPage extends ConsumerStatefulWidget {
  const WorkloadDebugPage({super.key});

  @override
  ConsumerState<WorkloadDebugPage> createState() => _WorkloadDebugPageState();
}

class _WorkloadDebugPageState extends ConsumerState<WorkloadDebugPage> {
  final TaskService _taskService = TaskService();
  final WorkloadStatisticsService _statisticsService = WorkloadStatisticsService();
  
  Map<String, dynamic> _debugInfo = {};
  bool _isLoading = true;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      // 加载任务数据
      await _taskService.loadTasks();
      final tasks = _taskService.tasks;

      // 🔧 测试新的工作量计算服务
      final workerStatistics = WorkloadCalculationService.calculateWorkerStatistics(tasks);
      final workloadOverview = WorkloadCalculationService.calculateWorkloadOverview(tasks);

      // 获取传统统计数据进行对比
      final traditionalStats = await _statisticsService.getWorkerStatistics(_taskService);
      final traditionalOverview = await _statisticsService.getWorkloadOverview(_taskService);

      // 分析任务中的工作量数据
      final tasksWithWorkload = <Map<String, dynamic>>[];
      final tasksWithoutWorkload = <Map<String, dynamic>>[];

      for (final task in tasks) {
        final taskInfo = {
          'id': task.id,
          'createTime': task.createTime.toString(),
          'productCode': task.productCode,
          'quantity': task.quantity,
          'isCompleted': task.isCompleted,
          'participants': task.participants,
          'hasWorkloadData': task.recognitionMetadata?.containsKey('workload') ?? false,
          'workloadData': task.recognitionMetadata?['workload'],
        };

        if (task.recognitionMetadata?.containsKey('workload') == true) {
          tasksWithWorkload.add(taskInfo);
        } else {
          tasksWithoutWorkload.add(taskInfo);
        }
      }

      setState(() {
        _debugInfo = {
          'totalTasks': tasks.length,
          'tasksWithWorkload': tasksWithWorkload.length,
          'tasksWithoutWorkload': tasksWithoutWorkload.length,
          'activeWorkers': workerStatistics.length,
          'totalWorkers': allWorkers.length,
          // 新计算服务结果
          'newWorkerStats': workerStatistics,
          'newOverview': workloadOverview.toMap(),
          // 传统计算结果
          'traditionalStats': traditionalStats,
          'traditionalOverview': traditionalOverview,
          // 详细数据
          'tasksWithWorkloadDetails': tasksWithWorkload,
          'tasksWithoutWorkloadDetails': tasksWithoutWorkload,
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('工作量统计调试'),
        backgroundColor: ThemeColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDebugInfo,
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: ThemeColors.primaryGradient,
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _error.isNotEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error, color: Colors.red, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          '加载失败: $_error',
                          style: const TextStyle(color: Colors.white),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadDebugInfo,
                          child: const Text('重试'),
                        ),
                      ],
                    ),
                  )
                : SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSummaryCard(),
                        const SizedBox(height: 16),
                        _buildComparisonCard(),
                        const SizedBox(height: 16),
                        _buildTasksAnalysisCard(),
                        const SizedBox(height: 16),
                        _buildWorkerStatsCard(),
                        const SizedBox(height: 16),
                        _buildActionButtons(),
                      ],
                    ),
                  ),
      ),
    );
  }

  Widget _buildSummaryCard() {
    return Card(
      color: Colors.white.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '📊 数据概览',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('总任务数', '${_debugInfo['totalTasks']}'),
            _buildInfoRow('有工作量数据的任务', '${_debugInfo['tasksWithWorkload']}'),
            _buildInfoRow('缺少工作量数据的任务', '${_debugInfo['tasksWithoutWorkload']}'),
            _buildInfoRow('活跃工人数', '${_debugInfo['activeWorkers']}'),
            _buildInfoRow('总工人数', '${_debugInfo['totalWorkers']}'),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonCard() {
    final newOverview = _debugInfo['newOverview'] as Map<String, dynamic>? ?? {};
    final traditionalOverview = _debugInfo['traditionalOverview'] as Map<String, dynamic>? ?? {};
    
    return Card(
      color: Colors.white.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🔧 新旧计算对比',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '新计算服务结果:',
              style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
            ),
            _buildInfoRow('总任务数', '${newOverview['totalTasks'] ?? 0}'),
            _buildInfoRow('完成任务数', '${newOverview['completedTasks'] ?? 0}'),
            _buildInfoRow('总吨数', '${newOverview['totalTonnage']?.round() ?? 0}'),
            _buildInfoRow('活跃工人数', '${newOverview['activeWorkers'] ?? 0}'),
            const SizedBox(height: 8),
            const Text(
              '传统计算结果:',
              style: TextStyle(color: Colors.orange, fontWeight: FontWeight.bold),
            ),
            _buildInfoRow('总任务数', '${traditionalOverview['totalTasks'] ?? 0}'),
            _buildInfoRow('总吨数', '${traditionalOverview['totalTonnage'] ?? 0}'),
            _buildInfoRow('活跃工人数', '${traditionalOverview['activeWorkers'] ?? 0}'),
            _buildInfoRow('完成率', '${traditionalOverview['completionRate'] ?? 0}%'),
          ],
        ),
      ),
    );
  }

  Widget _buildTasksAnalysisCard() {
    return Card(
      color: Colors.white.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '📝 任务分析',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '有工作量数据的任务 (${_debugInfo['tasksWithWorkload']}个):',
              style: const TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
            ),
            ..._buildTasksList(_debugInfo['tasksWithWorkloadDetails'] as List? ?? []),
            const SizedBox(height: 8),
            Text(
              '缺少工作量数据的任务 (${_debugInfo['tasksWithoutWorkload']}个):',
              style: const TextStyle(color: Colors.orange, fontWeight: FontWeight.bold),
            ),
            ..._buildTasksList(_debugInfo['tasksWithoutWorkloadDetails'] as List? ?? []),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkerStatsCard() {
    final newStats = _debugInfo['newWorkerStats'] as Map<String, WorkloadStatistics>? ?? {};
    
    return Card(
      color: Colors.white.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '👷 工人统计 (${newStats.length}人)',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            if (newStats.isEmpty) ...[
              const Text(
                '当前没有工人统计数据',
                style: TextStyle(color: Colors.orange),
              ),
            ] else ...[
              ...newStats.values.take(10).map((stats) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    '• ${stats.workerName}: ${stats.totalTasks}任务, ${stats.assignedTonnage.toStringAsFixed(1)}吨',
                    style: const TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                );
              }).toList(),
              if (newStats.length > 10) ...[
                Text(
                  '... 还有${newStats.length - 10}人',
                  style: const TextStyle(color: Colors.white70, fontSize: 12),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  List<Widget> _buildTasksList(List<dynamic> tasks) {
    if (tasks.isEmpty) {
      return [
        const Padding(
          padding: EdgeInsets.only(left: 16),
          child: Text('无', style: TextStyle(color: Colors.white70)),
        ),
      ];
    }

    return tasks.take(5).map((task) {
      return Padding(
        padding: const EdgeInsets.only(left: 16, top: 4),
        child: Text(
          '• ${task['id']}: ${task['productCode']} (${task['quantity']}托)',
          style: const TextStyle(color: Colors.white70, fontSize: 12),
        ),
      );
    }).toList();
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _testNewCalculationService,
            icon: const Icon(Icons.calculate),
            label: const Text('测试新计算服务'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _testRepositoryPattern,
            icon: const Icon(Icons.storage),
            label: const Text('测试Repository模式'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.white70),
          ),
          Text(
            value,
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Future<void> _testNewCalculationService() async {
    try {
      setState(() => _isLoading = true);

      final tasks = _taskService.tasks;
      final startTime = DateTime.now();
      
      // 测试新的计算服务
      final workerStats = WorkloadCalculationService.calculateWorkerStatistics(tasks);
      final overview = WorkloadCalculationService.calculateWorkloadOverview(tasks);
      
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('新计算服务测试完成\n'
                      '活跃工人: ${workerStats.length}人\n'
                      '总任务: ${overview.totalTasks}个\n'
                      '耗时: ${duration.inMilliseconds}ms'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );

      await _loadDebugInfo();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('测试新计算服务失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testRepositoryPattern() async {
    try {
      setState(() => _isLoading = true);

      // 测试Repository模式
      final taskOperations = ref.read(taskOperationsProvider);
      final currentTaskNotifier = ref.read(currentTaskProvider.notifier);
      
      // 测试获取任务列表
      final taskListAsync = ref.read(taskListProvider);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Repository模式测试完成\n'
                      '任务列表状态: ${taskListAsync.when(
                        data: (tasks) => '${tasks.length}个任务',
                        loading: () => '加载中',
                        error: (e, _) => '错误: $e',
                      )}'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );

      await _loadDebugInfo();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('测试Repository模式失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
      setState(() => _isLoading = false);
    }
  }
}
