import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/workload_statistics_service.dart';
import '../services/personal_workload_history_service.dart';
import '../services/task_service.dart';
import '../utils/theme_colors.dart';
import '../utils/app_logger.dart';
import '../widgets/premium_glass_card.dart';
import '../widgets/workload_chart_widgets.dart';
import '../core/lifecycle_mixin.dart';
import '../core/providers/app_providers.dart';

class WorkloadStatisticsPage extends ConsumerStatefulWidget {
  const WorkloadStatisticsPage({super.key});

  @override
  ConsumerState<WorkloadStatisticsPage> createState() => _WorkloadStatisticsPageState();
}

class _WorkloadStatisticsPageState extends ConsumerState<WorkloadStatisticsPage>
    with LifecycleMixin<WorkloadStatisticsPage> {
  final WorkloadStatisticsService _statisticsService =
      WorkloadStatisticsService();
  final TaskService _taskService = TaskService();

  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedWarehouse = '全部';
  String _selectedWorkerId = '';

  Map<String, Map<String, dynamic>> _filteredData = {};
  Map<String, dynamic> _overviewData = {};
  List<Map<String, dynamic>> _efficiencyRanking = [];
  List<String> _actualWarehouses = ['全部'];
  List<String> _actualWorkers = [];
  bool _isLoading = true;
  String _error = '';

  // 🎯 新增：个人工作量历史数据
  Map<String, Map<String, double>> _personalWorkloadTrends = {};
  Map<String, dynamic> _teamComparisonData = {};
  bool _showPersonalHistory = false;
  bool _isLoadingPersonalHistory = false; // 🔧 新增：个人历史加载状态
  String _selectedPeriod = 'daily';
  String _selectedWorkerForHistory = '';

  // 🎯 新增：专业分析数据
  Map<String, dynamic> _recognitionAnalysis = {};
  Map<String, dynamic> _cargoFlowAnalysis = {};
  Map<String, dynamic> _qualityControlAnalysis = {};
  Map<String, dynamic> _realTimeOperationData = {};
  Map<String, dynamic> _costBenefitAnalysis = {};
  bool _showAdvancedAnalysis = false;

  // �� 新增：专业分析展开状态
  Map<String, bool> _expandedSections = {
    '识别效率分析': true,
    '货物流转分析': true,
    '质量控制分析': true,
    '实时作业监控': true,
    '成本效益分析': true,
  };

  // 🎯 新增：当前选中的工作人员
  Map<String, dynamic>? _selectedWorker;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      // 现有数据加载
      final workerStats = await _statisticsService.getWorkerStatistics(
        _taskService,
        startDate: _startDate,
        endDate: _endDate,
      );

      final overview = await _statisticsService.getWorkloadOverview(
        _taskService,
        startDate: _startDate,
        endDate: _endDate,
      );

      final ranking = await _statisticsService.getEfficiencyRanking(
        _taskService,
        startDate: _startDate,
        endDate: _endDate,
      );

      // 🎯 新增：加载个人工作量历史数据
      final personalTrends = await _loadPersonalWorkloadTrends();
      final teamComparison = await PersonalWorkloadHistoryService.getTeamWorkloadComparison(
        startDate: _startDate,
        endDate: _endDate,
      );

      // 🎯 新增：加载专业分析数据
      final recognitionAnalysis = await _statisticsService
          .getRecognitionEfficiencyAnalysis(_taskService);
      final cargoFlowAnalysis =
          await _statisticsService.getCargoFlowAnalysis(_taskService);
      final qualityControlAnalysis =
          await _statisticsService.getQualityControlAnalysis(_taskService);
      final realTimeOperationData =
          await _statisticsService.getRealTimeOperationData(_taskService);
      final costBenefitAnalysis =
          await _statisticsService.getCostBenefitAnalysis(_taskService);

      // 获取实际仓库列表
      final actualWarehouses = <String>{'全部'};
      for (final stat in workerStats.values) {
        if ((stat['totalTasks'] as int) > 0) {
          actualWarehouses.add(stat['warehouse'] as String);
        }
      }

      // 过滤数据
      final filteredData = <String, Map<String, dynamic>>{};
      for (final entry in workerStats.entries) {
        final stat = entry.value;
        if ((stat['totalTasks'] as int) > 0) {
          if (_selectedWarehouse == '全部' ||
              stat['warehouse'] == _selectedWarehouse) {
            filteredData[entry.key] = stat;
          }
        }
      }

      setState(() {
        _filteredData = filteredData;
        _overviewData = overview;
        _efficiencyRanking = ranking;
        _actualWarehouses = actualWarehouses.toList()..sort();
        _personalWorkloadTrends = personalTrends;
        _teamComparisonData = teamComparison;
        _recognitionAnalysis = recognitionAnalysis;
        _cargoFlowAnalysis = cargoFlowAnalysis;
        _qualityControlAnalysis = qualityControlAnalysis;
        _realTimeOperationData = realTimeOperationData;
        _costBenefitAnalysis = costBenefitAnalysis;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = '加载数据失败: $e';
        _isLoading = false;
      });
    }
  }

  /// 🎯 优化：独立加载个人历史数据
  Future<void> _loadPersonalHistoryData() async {
    if (_isLoadingPersonalHistory) return; // 防止重复加载

    setState(() {
      _isLoadingPersonalHistory = true;
    });

    try {
      final personalTrends = await _loadPersonalWorkloadTrends();
      final teamComparison = await PersonalWorkloadHistoryService.getTeamWorkloadComparison(
        startDate: _startDate,
        endDate: _endDate,
      );

      setState(() {
        _personalWorkloadTrends = personalTrends;
        _teamComparisonData = teamComparison;
        _isLoadingPersonalHistory = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingPersonalHistory = false;
      });
      AppLogger.error('加载个人历史数据失败: $e');
    }
  }

  /// 🎯 加载个人工作量趋势数据
  Future<Map<String, Map<String, double>>> _loadPersonalWorkloadTrends() async {
    try {
      final trends = <String, Map<String, double>>{};
      
      // 为每个活跃工人加载趋势数据
      for (final workerId in _filteredData.keys) {
        final workerTrends = await PersonalWorkloadHistoryService.getPersonalWorkloadTrends(
          workerId: workerId,
          period: _selectedPeriod,
          startDate: _startDate,
          endDate: _endDate,
        );
        
        if (workerTrends['trendData'] != null) {
          trends[workerId] = Map<String, double>.from(workerTrends['trendData']);
        }
      }
      
      return trends;
    } catch (e) {
      AppLogger.error('加载个人工作量趋势失败: $e');
      return {};
    }
  }

  /// 🔧 新增：调试面板
  void _showDebugInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🐛 调试信息'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('数据统计时间范围:'),
              Text('开始: ${_startDate.toString().split(' ')[0]}'),
              Text('结束: ${_endDate.toString().split(' ')[0]}'),
              const SizedBox(height: 16),
              Text('数据统计结果:'),
              Text('活跃工人: ${_filteredData.length}人'),
              Text('有效仓库: ${_actualWarehouses.length - 1}个'), // 除去"全部"
              Text('效率排名: ${_efficiencyRanking.length}人'),
              const SizedBox(height: 16),
              Text('概览数据:'),
              Text('总任务: ${_overviewData['totalTasks'] ?? 0}'),
              Text('已完成: ${_overviewData['completedTasks'] ?? 0}'),
              Text(
                  '总吨数: ${(_overviewData['totalTonnage'] ?? 0.0).toStringAsFixed(1)}'),
              Text(
                  '完成率: ${((_overviewData['completionRate'] ?? 0.0) * 100).toStringAsFixed(1)}%'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _loadData(); // 刷新数据
            },
            child: const Text('刷新'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: ThemeColors.primaryGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              _buildFilters(),
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _error.isNotEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.error, color: Colors.red, size: 48),
                                const SizedBox(height: 16),
                                Text(
                                  _error,
                                  style: const TextStyle(color: Colors.white),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: _loadData,
                                  child: const Text('重试'),
                                ),
                              ],
                            ),
                          )
                        : _buildContent(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 移除冗余的返回按钮，使用手势导航
          const SizedBox(width: 8),
          const Text(
            '工作量统计',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const Spacer(),
          // 🔧 新增：调试按钮
          IconButton(
            onPressed: _showDebugInfo,
            icon: const Icon(Icons.bug_report, color: Colors.white70),
            tooltip: '调试信息',
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.file_download, color: Colors.white),
            onPressed: _exportData,
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadData,
          ),
          IconButton(
            icon: const Icon(Icons.bug_report, color: Colors.white),
            onPressed: _showDebugInfo,
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: _buildDateFilter(),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildWarehouseFilter(),
          ),
        ],
      ),
    );
  }

  Widget _buildDateFilter() {
    return GestureDetector(
      onTap: _selectDateRange,
      child: Container(
        height: 44,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          color: ThemeColors.surfaceContainerHighest.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: ThemeColors.outline.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            const Icon(Icons.date_range, color: Colors.white, size: 16),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                '${_startDate.month}/${_startDate.day} - ${_endDate.month}/${_endDate.day}',
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarehouseFilter() {
    return Container(
      height: 44,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: ThemeColors.surfaceContainerHighest.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: ThemeColors.outline.withValues(alpha: 0.3)),
      ),
      child: DropdownButton<String>(
        value: _selectedWarehouse,
        isExpanded: true,
        underline: Container(),
        dropdownColor: Colors.grey[800],
        style: const TextStyle(color: Colors.white, fontSize: 14),
        items: _actualWarehouses.map((String value) {
          return DropdownMenuItem<String>(
            value: value,
            child: Text(value),
          );
        }).toList(),
        onChanged: (String? newValue) {
          if (newValue != null) {
            setState(() {
              _selectedWarehouse = newValue;
            });
            _loadData();
          }
        },
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildOverviewCards(),
          const SizedBox(height: 16),
          _buildRankingSection(),
          const SizedBox(height: 16),
          _buildChartsSection(),
          const SizedBox(height: 16),
          _buildWorkerListSection(),
          const SizedBox(height: 16),
          // 🎯 新增：个人工作量历史切换按钮
          _buildPersonalHistoryToggle(),
          if (_showPersonalHistory) ...[ 
            const SizedBox(height: 16),
            _buildPersonalHistorySection(),
          ],
          const SizedBox(height: 16),
          // 🎯 新增：专业分析切换按钮
          _buildAdvancedAnalysisToggle(),
          if (_showAdvancedAnalysis) ...[
            const SizedBox(height: 16),
            _buildAdvancedAnalysisSection(),
          ],
        ],
      ),
    );
  }

  Widget _buildOverviewCards() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 1.2,
      children: [
        _buildOverviewCard(
          '总任务数',
          '${_overviewData['totalTasks'] ?? 0}',
          Icons.assignment,
          Colors.blue,
        ),
        _buildOverviewCard(
          '总吨数',
          '${_overviewData['totalTonnage'] ?? 0}',
          Icons.fitness_center,
          Colors.green,
        ),
        _buildOverviewCard(
          '参与人数',
          '${_overviewData['totalWorkers'] ?? 0}',
          Icons.people,
          Colors.orange,
        ),
        _buildOverviewCard(
          '完成率',
          '${_overviewData['completionRate'] ?? 0}%',
          Icons.check_circle,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildOverviewCard(
      String title, String value, IconData icon, Color color) {
    return PremiumGlassCard(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(icon, color: color, size: 32),
              // 如果是选中了某个工作人员，显示重置按钮
              if (_selectedWorker != null && title == '总任务数') ...[
                GestureDetector(
                  onTap: _resetToAllData,
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: ThemeColors.surfaceContainerHighest.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.refresh,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
          // 显示当前数据状态
          if (_selectedWorker != null && title == '总任务数') ...[
            const SizedBox(height: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: Text(
                '${_selectedWorker!['name']}的数据',
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 🎯 重置到全部数据显示
  void _resetToAllData() {
    setState(() {
      _selectedWorker = null;
    });

    // 重新加载全部数据
    _loadData();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已恢复显示全部工作人员数据'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  Widget _buildRankingSection() {
    return PremiumGlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.leaderboard, color: Colors.amber, size: 20),
              const SizedBox(width: 8),
              const Text(
                '工作量排行榜',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildTopPerformers(),
        ],
      ),
    );
  }

  Widget _buildTopPerformers() {
    if (_efficiencyRanking.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Text(
            '暂无工作量数据',
            style: TextStyle(color: Colors.white70),
          ),
        ),
      );
    }

    return Column(
      children: _efficiencyRanking.take(5).map((worker) {
        final index = _efficiencyRanking.indexOf(worker);
        return _buildRankingItem(worker, index + 1);
      }).toList(),
    );
  }

  Widget _buildRankingItem(Map<String, dynamic> worker, int rank) {
    Color rankColor = Colors.white;
    if (rank == 1)
      rankColor = Colors.amber;
    else if (rank == 2)
      rankColor = Colors.grey.shade300;
    else if (rank == 3) rankColor = Colors.orange.shade300;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ThemeColors.surfaceContainerHighest.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border:
            rank <= 3 ? Border.all(color: rankColor.withOpacity(0.3)) : null,
      ),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: rankColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                '$rank',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: rankColor,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: GestureDetector(
              onTap: () => _updateMainPageData(worker),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    worker['name'] ?? '未知',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    '${worker['role'] ?? '未知'} | ${worker['warehouse'] ?? '未知'}',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${worker['tasks'] ?? 0}个任务',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                '${worker['tonnage'] ?? 0}吨',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
            ],
          ),
          const SizedBox(width: 12),
          // 图表图标按钮
          GestureDetector(
            onTap: () => _showWorkerChart(worker),
            child: Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.2),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: const Icon(
                Icons.bar_chart,
                color: Colors.blue,
                size: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 🎯 更新主页面数据显示
  void _updateMainPageData(Map<String, dynamic> worker) {
    setState(() {
      // 更新概览数据为该工作人员的数据
      _overviewData = {
        'totalTasks': worker['tasks'] ?? 0,
        'totalTonnage': worker['tonnage'] ?? 0,
        'totalWorkers': 1, // 单个工作人员
        'completionRate': worker['completionRate'] ?? 0.0,
      };

      // 更新当前选中的工作人员
      _selectedWorker = worker;
    });

    // 显示选中提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已切换到 ${worker['name']} 的工作数据'),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 🎯 显示工作人员图表 - 优化布局防止溢出
  void _showWorkerChart(Map<String, dynamic> worker) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(16),
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          decoration: BoxDecoration(
            gradient: ThemeColors.primaryGradient,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: ThemeColors.outline.withOpacity(0.2)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 头部 - 固定高度
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: Colors.blue.withOpacity(0.2),
                      child: Text(
                        (worker['name'] ?? '未知').substring(0, 1),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            worker['name'] ?? '未知',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            '${worker['role'] ?? '未知'} | ${worker['warehouse'] ?? '未知'}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white.withOpacity(0.7),
                            ),
                          ),
                          Text(
                            '数据范围: ${_startDate.month}/${_startDate.day} - ${_endDate.month}/${_endDate.day}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.yellow.withOpacity(0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              const Divider(color: Colors.white12, height: 1),
              // 图表内容 - 可滚动
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: _buildWorkerTrendChart(worker),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🎯 构建工作人员趋势图表 - 优化布局
  Widget _buildWorkerTrendChart(Map<String, dynamic> worker) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 统计卡片 - 使用LayoutBuilder获取实际容器宽度，实现2行2列布局
        LayoutBuilder(
          builder: (context, constraints) {
            final cardWidth = (constraints.maxWidth - 8) / 2; // 减去中间间距
            return Column(
              children: [
                // 第一行：总任务数 + 总吨数
                Row(
                  children: [
                    SizedBox(
                      width: cardWidth,
                      child: _buildChartMetricCard(
                        '总任务数',
                        '${worker['tasks'] ?? 0}',
                        Icons.assignment,
                        Colors.blue,
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: cardWidth,
                      child: _buildChartMetricCard(
                        '总吨数',
                        '${worker['tonnage'] ?? 0}',
                        Icons.scale,
                        Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // 第二行：完成率 + 效率
                Row(
                  children: [
                    SizedBox(
                      width: cardWidth,
                      child: _buildChartMetricCard(
                        '完成率',
                        '${((worker['completionRate'] ?? 0.0) * 100).toStringAsFixed(1)}%',
                        Icons.check_circle,
                        Colors.purple,
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: cardWidth,
                      child: _buildChartMetricCard(
                        '效率',
                        '${((worker['efficiency'] ?? 0.0) * 100).toStringAsFixed(1)}%',
                        Icons.speed,
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
        const SizedBox(height: 20),
        // 图表标题
        Text(
          '工作量趋势分析 (${_startDate.month}/${_startDate.day} - ${_endDate.month}/${_endDate.day})',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),
        // 趋势图表 - 固定高度
        Container(
          height: 250,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: ThemeColors.surfaceContainerHighest.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: ThemeColors.outline.withOpacity(0.2)),
          ),
          child: _buildWorkerBarChart(worker),
        ),
      ],
    );
  }

  /// 🎯 图表指标卡片 - 优化布局
  Widget _buildChartMetricCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.white.withOpacity(0.8),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// 🎯 工作人员柱状图
  Widget _buildWorkerBarChart(Map<String, dynamic> worker) {
    // 模拟7天的工作量数据
    final chartData = _generateWorkerChartData(worker);

    return WorkloadBarChart(
      data: chartData,
      height: double.infinity,
      showLabels: true,
    );
  }

  /// 🎯 生成工作人员图表数据 - 确保与时间范围一致
  Map<String, double> _generateWorkerChartData(Map<String, dynamic> worker) {
    final totalTasks = (worker['tasks'] ?? 0) as int;
    final totalTonnage = (worker['tonnage'] ?? 0) as int;

    // 基于选定的时间范围生成数据分布
    final Map<String, double> chartData = {};
    final daysDiff = _endDate.difference(_startDate).inDays;

    // 根据时间范围决定显示方式
    if (daysDiff <= 7) {
      // 显示每日数据
      for (int i = 0; i <= daysDiff; i++) {
        final date = _startDate.add(Duration(days: i));
        final dayName = '${date.month}/${date.day}';

        // 基于总工作量模拟每日分布
        double dailyTasks = 0;
        if (totalTasks > 0) {
          final isWeekend = date.weekday == 6 || date.weekday == 7;
          final baseRatio = isWeekend ? 0.3 : 1.0;
          final randomFactor = 0.7 + (i * 0.05);
          dailyTasks = (totalTasks / (daysDiff + 1)) * baseRatio * randomFactor;
        }

        chartData[dayName] = dailyTasks;
      }
    } else if (daysDiff <= 30) {
      // 显示周数据
      final weeks = (daysDiff / 7).ceil();
      for (int i = 0; i < weeks; i++) {
        final weekStart = _startDate.add(Duration(days: i * 7));
        final weekName = '第${i + 1}周';

        double weeklyTasks = 0;
        if (totalTasks > 0) {
          final randomFactor = 0.8 + (i * 0.1);
          weeklyTasks = (totalTasks / weeks) * randomFactor;
        }

        chartData[weekName] = weeklyTasks;
      }
    } else {
      // 显示月数据
      final months = (daysDiff / 30).ceil();
      for (int i = 0; i < months; i++) {
        final monthStart = _startDate.add(Duration(days: i * 30));
        final monthName = '${monthStart.month}月';

        double monthlyTasks = 0;
        if (totalTasks > 0) {
          final randomFactor = 0.9 + (i * 0.05);
          monthlyTasks = (totalTasks / months) * randomFactor;
        }

        chartData[monthName] = monthlyTasks;
      }
    }

    return chartData;
  }

  /// 🎯 获取星期名称
  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return '周一';
      case 2:
        return '周二';
      case 3:
        return '周三';
      case 4:
        return '周四';
      case 5:
        return '周五';
      case 6:
        return '周六';
      case 7:
        return '周日';
      default:
        return '未知';
    }
  }

  Widget _buildChartsSection() {
    return Column(
      children: [
        // 第一行：工作量分析（雷达图）
        PremiumGlassCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '工作量分析',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              // 增大雷达图显示区域
              SizedBox(
                height: 320, // 增加高度
                child: _buildRadarChart(),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // 第二行：人员分布（饼图）
        PremiumGlassCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '人员分布',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              // 增大饼图显示区域
              SizedBox(
                height: 320, // 增加高度
                child: _buildPieChart(),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // 第三行：缺陷类型分析（如果有数据）
        if (_recognitionAnalysis.isNotEmpty &&
            _recognitionAnalysis['errorTypeDistribution'] != null) ...[
          PremiumGlassCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '缺陷类型分析',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 320, // 统一高度
                  child: _buildDefectTypeChart(),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
        // 第四行：设备状态分析
        PremiumGlassCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.devices, color: Colors.green, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    '设备状态分析',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SizedBox(
                height: 320, // 统一高度
                child: _buildDeviceStatusChart(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildWorkerListSection() {
    return PremiumGlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.group, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              const Text(
                '工作人员详情',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildWorkerList(),
        ],
      ),
    );
  }

  Widget _buildWorkerList() {
    if (_filteredData.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Text(
            '暂无工作人员数据',
            style: TextStyle(color: Colors.white70),
          ),
        ),
      );
    }

    return Column(
      children: _filteredData.entries.map((entry) {
        final workload = entry.value;
        return _buildWorkerListItem(workload);
      }).toList(),
    );
  }

  Widget _buildWorkerListItem(Map<String, dynamic> workload) {
    return GestureDetector(
      onTap: () => _updateMainPageData(workload),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: ThemeColors.surfaceContainerHighest.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: ThemeColors.outline.withOpacity(0.1)),
        ),
        child: Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor:
                  _getRoleColor(workload['role'] ?? '').withOpacity(0.2),
              child: Text(
                (workload['name'] ?? '未知').substring(0, 1),
                style: TextStyle(
                  color: _getRoleColor(workload['role'] ?? ''),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    workload['name'] ?? '未知',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${workload['role'] ?? '未知'} | ${workload['warehouse'] ?? '未知'}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${workload['tasks'] ?? 0}个任务',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
                Text(
                  '${workload['tonnage'] ?? 0}吨',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.white.withOpacity(0.7),
                  ),
                ),
              ],
            ),
            const SizedBox(width: 12),
            // 图表图标按钮
            GestureDetector(
              onTap: () => _showWorkerChart(workload),
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.blue.withOpacity(0.3)),
                ),
                child: const Icon(
                  Icons.bar_chart,
                  color: Colors.blue,
                  size: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRadarChart() {
    final data = _getRadarChartData();
    return WorkloadRadarChart(
      data: data,
      size: 280, // 增大雷达图尺寸
    );
  }

  Widget _buildPieChart() {
    final data = _getPieChartData();
    return WorkloadPieChart(
      data: data,
      size: 280, // 增大饼图尺寸
    );
  }

  /// 新增：缺陷类型分析图表
  Widget _buildDefectTypeChart() {
    if (_recognitionAnalysis['errorTypeDistribution'] == null) {
      return const Center(
        child: Text(
          '暂无缺陷数据',
          style: TextStyle(color: Colors.white54, fontSize: 14),
        ),
      );
    }

    final errorData = Map<String, double>.from(
      (_recognitionAnalysis['errorTypeDistribution'] as Map<String, int>)
          .map((key, value) => MapEntry(key, value.toDouble())),
    );

    return WorkloadPieChart(
      data: errorData,
      size: 280, // 统一尺寸
    );
  }

  /// 新增：设备状态分析图表
  Widget _buildDeviceStatusChart() {
    // 模拟设备状态数据
    final deviceStatusData = {
      '正常': 85.0,
      '维护中': 10.0,
      '故障': 3.0,
      '离线': 2.0,
    };

    return Row(
      children: [
        // 左侧：设备状态饼图
        Expanded(
          flex: 2,
          child: WorkloadPieChart(
            data: deviceStatusData,
            size: 240,
          ),
        ),
        // 右侧：设备状态详情
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildDeviceStatusItem('正常设备', '85%', Colors.green),
              const SizedBox(height: 12),
              _buildDeviceStatusItem('维护中', '10%', Colors.orange),
              const SizedBox(height: 12),
              _buildDeviceStatusItem('故障设备', '3%', Colors.red),
              const SizedBox(height: 12),
              _buildDeviceStatusItem('离线设备', '2%', Colors.grey),
            ],
          ),
        ),
      ],
    );
  }

  /// 设备状态项
  Widget _buildDeviceStatusItem(String label, String value, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(6),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 13,
            ),
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 13,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Map<String, double> _getRadarChartData() {
    if (_filteredData.isEmpty) {
      return {
        '任务量': 0,
        '完成率': 0,
        '效率': 0,
        '质量': 0,
        '稳定性': 0,
      };
    }

    double totalTasks = 0;
    double totalCompletion = 0;
    double totalEfficiency = 0;
    double totalQuality = 0;
    double totalStability = 0;

    for (final workload in _filteredData.values) {
      totalTasks += (workload['tasks'] ?? 0) as num;
      totalCompletion += (workload['completionRate'] ?? 0) as num;
      totalEfficiency += (workload['efficiency'] ?? 0) as num;
      totalQuality += (workload['quality'] ?? 85) as num;
      totalStability += (workload['stability'] ?? 88) as num;
    }

    final count = _filteredData.length;
    return {
      '任务量': totalTasks / count,
      '完成率': (totalCompletion / count) * 100,
      '效率': totalEfficiency / count,
      '质量': totalQuality / count,
      '稳定性': totalStability / count,
    };
  }

  Map<String, double> _getPieChartData() {
    if (_filteredData.isEmpty) {
      return {'暂无数据': 100};
    }

    final Map<String, double> roleData = {};
    for (final workload in _filteredData.values) {
      final role = workload['role'] ?? '未知';
      final tonnage = (workload['tonnage'] ?? 0) as num;
      roleData[role] = (roleData[role] ?? 0) + tonnage.toDouble();
    }

    return roleData;
  }

  Color _getRoleColor(String role) {
    switch (role) {
      case '装卸工':
        return Colors.blue;
      case '司机':
        return Colors.green;
      case '调度员':
        return Colors.orange;
      case '质检员':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
      _loadData();
    }
  }

  void _exportData() async {
    try {
      final buffer = StringBuffer();

      // CSV头部
      buffer.writeln('日期,姓名,库区,角色,任务数,吨数,完成率');

      // 数据行
      for (final workload in _filteredData.values) {
        final name = workload['name'] ?? '未知';
        final warehouse = workload['warehouse'] ?? '未知';
        final role = workload['role'] ?? '未知';
        final tasks = workload['tasks'] ?? 0;
        final tonnage = workload['tonnage'] ?? 0;
        final completionRate =
            ((workload['completionRate'] ?? 0.0) * 100).round();

        buffer.writeln(
            '${_startDate.year}-${_startDate.month}-${_startDate.day}至${_endDate.year}-${_endDate.month}-${_endDate.day},$name,$warehouse,$role,$tasks,$tonnage,$completionRate%');
      }

      // 复制到剪贴板
      await Clipboard.setData(ClipboardData(text: buffer.toString()));

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('工作量数据已复制到剪贴板'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('导出失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 🎯 优化：个人工作量历史切换按钮 - 统一风格
  Widget _buildPersonalHistoryToggle() {
    return PremiumGlassCard(
      child: InkWell(
        onTap: () async {
          setState(() {
            _showPersonalHistory = !_showPersonalHistory;
          });
          if (_showPersonalHistory && _personalWorkloadTrends.isEmpty) {
            await _loadPersonalHistoryData(); // 🔧 优化：仅在需要时加载
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.person_search,
                color: ThemeColors.primary, // 🔧 修复：使用主题颜色
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '个人工作量历史',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '每日、每周、每月个人工作量趋势和团队对比分析',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                _showPersonalHistory ? Icons.expand_less : Icons.expand_more,
                color: Colors.white70,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🎯 优化：个人工作量历史展示区域 - 添加加载状态
  Widget _buildPersonalHistorySection() {
    if (_isLoadingPersonalHistory) {
      return PremiumGlassCard(
        child: Container(
          height: 200,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(ThemeColors.primary),
                ),
                const SizedBox(height: 16),
                Text(
                  '正在加载个人工作量历史数据...',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Column(
      children: [
        // 时间周期选择器
        _buildPeriodSelector(),
        const SizedBox(height: 16),
        // 团队工作量对比
        _buildTeamComparisonCard(),
        const SizedBox(height: 16),
        // 个人趋势图表
        _buildPersonalTrendsGrid(),
      ],
    );
  }

  /// 🎯 优化：时间周期选择器 - 统一过滤器风格
  Widget _buildPeriodSelector() {
    return PremiumGlassCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.calendar_month, color: ThemeColors.primary, size: 20), // 🔧 使用主题颜色
                const SizedBox(width: 8),
                Text(
                  '时间周期选择',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 🔧 优化：使用与现有过滤器一致的风格
            Row(
              children: [
                Expanded(
                  child: _buildPeriodFilter('daily', '每日', Icons.today),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildPeriodFilter('weekly', '每周', Icons.view_week),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildPeriodFilter('monthly', '每月', Icons.calendar_view_month),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 🔧 新增：统一风格的周期过滤器
  Widget _buildPeriodFilter(String period, String label, IconData icon) {
    final isSelected = _selectedPeriod == period;
    
    return GestureDetector(
      onTap: () async {
        if (_selectedPeriod != period) {
          setState(() {
            _selectedPeriod = period;
          });
          await _loadPersonalHistoryData(); // 🔧 优化：使用独立加载方法
        }
      },
      child: Container(
        height: 44, // 🔧 统一：与现有过滤器相同高度
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          // 🔧 统一：使用与现有过滤器相同的背景色
          color: isSelected 
            ? ThemeColors.primary.withValues(alpha: 0.2)
            : ThemeColors.surfaceContainerHighest.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
              ? ThemeColors.primary.withValues(alpha: 0.5)
              : ThemeColors.outline.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon, 
              color: isSelected ? ThemeColors.primary : Colors.white70,
              size: 16,
            ),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: isSelected ? Colors.white : Colors.white70,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 🎯 团队工作量对比卡片
  Widget _buildTeamComparisonCard() {
    if (_teamComparisonData.isEmpty) {
      return PremiumGlassCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(Icons.group_work, color: Colors.orange, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    '团队工作量对比',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Text(
                '暂无历史数据',
                style: TextStyle(color: Colors.white70),
              ),
            ],
          ),
        ),
      );
    }

    final workerStats = _teamComparisonData['workerStats'] as Map<String, dynamic>? ?? {};

    return PremiumGlassCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.group_work, color: ThemeColors.secondary, size: 20), // 🔧 使用主题颜色
                const SizedBox(width: 8),
                Text(
                  '团队工作量对比',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: ThemeColors.secondary.withOpacity(0.2), // 🔧 使用主题颜色
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '${workerStats.length} 人',
                    style: TextStyle(
                      fontSize: 12,
                      color: ThemeColors.secondary, // 🔧 使用主题颜色
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            // 工人对比列表
            Column(
              children: workerStats.entries.take(5).map((entry) {
                final stats = entry.value as Map<String, dynamic>;
                final totalTonnage = (stats['totalTonnage'] as double? ?? 0.0);
                final totalTasks = (stats['totalTasks'] as int? ?? 0);
                final avgTonnage = (stats['averageTonnagePerTask'] as double? ?? 0.0);

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: ThemeColors.surfaceContainerHighest.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: ThemeColors.secondary.withOpacity(0.2), // 🔧 使用主题颜色
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 16,
                        backgroundColor: ThemeColors.secondary.withOpacity(0.2), // 🔧 使用主题颜色
                        child: Text(
                          (stats['workerName'] as String? ?? '未知').substring(0, 1),
                          style: TextStyle(
                            color: ThemeColors.secondary, // 🔧 使用主题颜色
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              stats['workerName'] as String? ?? '未知',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              '${stats['role'] ?? '未知'} | ${stats['warehouse'] ?? '未知'}',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '${totalTonnage.toStringAsFixed(1)}吨',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            '$totalTasks任务',
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(width: 12),
                      GestureDetector(
                        onTap: () => _showPersonalTrendDialog(entry.key, stats),
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: ThemeColors.secondary.withOpacity(0.2), // 🔧 使用主题颜色
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Icon(
                            Icons.trending_up,
                            color: ThemeColors.secondary, // 🔧 使用主题颜色
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 🎯 个人趋势网格
  Widget _buildPersonalTrendsGrid() {
    if (_personalWorkloadTrends.isEmpty) {
      return PremiumGlassCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(Icons.trending_up, color: Colors.green, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    '个人工作量趋势',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Text(
                '暂无趋势数据',
                style: TextStyle(color: Colors.white70),
              ),
            ],
          ),
        ),
      );
    }

    return PremiumGlassCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: ThemeColors.accent, size: 20), // 🔧 使用主题颜色
                const SizedBox(width: 8),
                Text(
                  '个人工作量趋势',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Text(
                  _getPeriodDisplayName(_selectedPeriod),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            // 小型趋势图网格
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.2,
              ),
              itemCount: _personalWorkloadTrends.length.clamp(0, 4), // 最多显示4个
              itemBuilder: (context, index) {
                final entry = _personalWorkloadTrends.entries.elementAt(index);
                final workerId = entry.key;
                final trends = entry.value;
                final workerData = _filteredData[workerId];

                if (workerData == null) return Container();

                return _buildMiniTrendCard(workerId, workerData, trends);
              },
            ),
            if (_personalWorkloadTrends.length > 4) ...[
              const SizedBox(height: 16),
              Center(
                child: TextButton(
                  onPressed: _showAllPersonalTrends,
                  child: Text(
                    '查看全部 ${_personalWorkloadTrends.length} 人趋势',
                    style: TextStyle(
                      color: ThemeColors.accent, // 🔧 使用主题颜色
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 🎯 获取周期显示名称
  String _getPeriodDisplayName(String period) {
    switch (period) {
      case 'daily':
        return '每日趋势';
      case 'weekly':
        return '每周趋势';
      case 'monthly':
        return '每月趋势';
      default:
        return '趋势分析';
    }
  }

  /// 🎯 小型趋势卡片
  Widget _buildMiniTrendCard(String workerId, Map<String, dynamic> workerData, Map<String, double> trends) {
    return GestureDetector(
      onTap: () => _showPersonalTrendDialog(workerId, workerData),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: ThemeColors.surfaceContainerHighest.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: ThemeColors.accent.withOpacity(0.2), // 🔧 使用主题颜色
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 12,
                  backgroundColor: ThemeColors.accent.withOpacity(0.2), // 🔧 使用主题颜色
                  child: Text(
                    (workerData['name'] as String? ?? '未知').substring(0, 1),
                    style: TextStyle(
                      color: ThemeColors.accent, // 🔧 使用主题颜色
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    workerData['name'] as String? ?? '未知',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Expanded(
              child: trends.isNotEmpty 
                ? WorkloadLineChart(
                    data: trends.entries.map((e) => {'label': e.key, 'value': e.value}).toList(),
                    height: double.infinity,
                  )
                : Center(
                    child: Text(
                      '暂无数据',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.white54,
                      ),
                    ),
                  ),
            ),
            const SizedBox(height: 4),
            Text(
              '${workerData['tonnage'] ?? 0}吨 · ${workerData['tasks'] ?? 0}任务',
              style: TextStyle(
                fontSize: 10,
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 🎯 显示个人趋势详情对话框
  void _showPersonalTrendDialog(String workerId, Map<String, dynamic> workerData) {
    final trends = _personalWorkloadTrends[workerId] ?? {};

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(16),
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          decoration: BoxDecoration(
            gradient: ThemeColors.primaryGradient,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 头部
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: Colors.green.withOpacity(0.2),
                      child: Text(
                        (workerData['name'] as String? ?? '未知').substring(0, 1),
                        style: TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            workerData['name'] as String? ?? '未知',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            '${workerData['role'] ?? '未知'} | ${workerData['warehouse'] ?? '未知'}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              // 内容
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // 统计概要
                      _buildPersonalSummaryCards(workerData),
                      const SizedBox(height: 20),
                      // 趋势图表
                      if (trends.isNotEmpty) ...[
                        Text(
                          '工作量趋势 (${_getPeriodDisplayName(_selectedPeriod)})',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Container(
                          height: 250,
                          child: WorkloadLineChart(
                            data: trends.entries.map((e) => {'label': e.key, 'value': e.value}).toList(),
                            height: 250,
                          ),
                        ),
                      ] else ...[
                        Container(
                          height: 100,
                          child: Center(
                            child: Text(
                              '暂无${_getPeriodDisplayName(_selectedPeriod)}数据',
                              style: TextStyle(color: Colors.white70),
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🎯 个人统计概要卡片
  Widget _buildPersonalSummaryCards(Map<String, dynamic> workerData) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            '总任务数',
            '${workerData['tasks'] ?? 0}',
            Icons.assignment,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildSummaryCard(
            '总吨数',
            '${workerData['tonnage'] ?? 0}',
            Icons.scale,
            Colors.green,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildSummaryCard(
            '完成率',
            '${((workerData['completionRate'] as double? ?? 0.0) * 100).toStringAsFixed(0)}%',
            Icons.check_circle,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  /// 🎯 统计概要卡片
  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 🎯 显示所有个人趋势
  void _showAllPersonalTrends() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.all(16),
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.9,
          ),
          decoration: BoxDecoration(
            gradient: ThemeColors.primaryGradient,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              // 头部
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.trending_up, color: Colors.green, size: 24),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        '全部个人工作量趋势',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              // 内容
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      childAspectRatio: 1.2,
                    ),
                    itemCount: _personalWorkloadTrends.length,
                    itemBuilder: (context, index) {
                      final entry = _personalWorkloadTrends.entries.elementAt(index);
                      final workerId = entry.key;
                      final trends = entry.value;
                      final workerData = _filteredData[workerId];

                      if (workerData == null) return Container();

                      return _buildMiniTrendCard(workerId, workerData, trends);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🎯 新增：专业分析切换按钮
  Widget _buildAdvancedAnalysisToggle() {
    return PremiumGlassCard(
      child: InkWell(
        onTap: () {
          setState(() {
            _showAdvancedAnalysis = !_showAdvancedAnalysis;
          });
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.analytics,
                color: Colors.blue,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '专业数据分析',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '识别效率、货物流转、质量控制、成本分析等深度洞察',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                _showAdvancedAnalysis ? Icons.expand_less : Icons.expand_more,
                color: Colors.white70,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🎯 新增：专业分析区域
  Widget _buildAdvancedAnalysisSection() {
    return Column(
      children: [
        // 识别效率分析
        _buildAnalysisCard(
          '识别效率分析',
          Icons.speed,
          Colors.blue,
          _buildRecognitionEfficiencyContent(),
        ),
        const SizedBox(height: 12),
        // 货物流转分析
        _buildAnalysisCard(
          '货物流转分析',
          Icons.local_shipping,
          Colors.green,
          _buildCargoFlowContent(),
        ),
        const SizedBox(height: 12),
        // 质量控制分析
        _buildAnalysisCard(
          '质量控制分析',
          Icons.verified,
          Colors.orange,
          _buildQualityControlContent(),
        ),
        const SizedBox(height: 12),
        // 实时作业监控
        _buildAnalysisCard(
          '实时作业监控',
          Icons.monitor,
          Colors.purple,
          _buildRealTimeOperationContent(),
        ),
        const SizedBox(height: 12),
        // 成本效益分析
        _buildAnalysisCard(
          '成本效益分析',
          Icons.attach_money,
          Colors.teal,
          _buildCostBenefitContent(),
        ),
      ],
    );
  }

  /// 🎯 通用分析卡片组件
  Widget _buildAnalysisCard(
      String title, IconData icon, Color color, Widget content) {
    final isExpanded = _expandedSections[title] ?? false;

    return PremiumGlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () {
              setState(() {
                _expandedSections[title] = !isExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, color: color, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: Colors.white70,
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded) ...[
            const Divider(color: Colors.white12, height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: content,
            ),
          ],
        ],
      ),
    );
  }

  /// 🎯 识别效率分析内容 - 优化布局
  Widget _buildRecognitionEfficiencyContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 核心指标行 - 使用LayoutBuilder获取实际容器宽度，实现2行2列布局
        LayoutBuilder(
          builder: (context, constraints) {
            final cardWidth = (constraints.maxWidth - 12) / 2; // 减去中间间距
            return Column(
              children: [
                // 第一行：识别成功率 + 识别速度
                Row(
                  children: [
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '识别成功率',
                        '${((_recognitionAnalysis['recognitionSuccessRate'] ?? 0.0) * 100).toStringAsFixed(1)}%',
                        Icons.check_circle,
                        Colors.green,
                        showProgress: true,
                        progressValue:
                            (_recognitionAnalysis['recognitionSuccessRate'] ??
                                0.0),
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '识别速度',
                        '${(_recognitionAnalysis['recognitionSpeed'] ?? 0.0).toStringAsFixed(1)}张/分',
                        Icons.speed,
                        Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // 第二行：平均识别时间 + 识别准确率
                Row(
                  children: [
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '平均识别时间',
                        '${(_recognitionAnalysis['averageRecognitionTime'] ?? 0.0).toStringAsFixed(0)}ms',
                        Icons.timer,
                        Colors.orange,
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '识别准确率',
                        '${((_recognitionAnalysis['recognitionAccuracy'] ?? 0.95) * 100).toStringAsFixed(1)}%',
                        Icons.verified,
                        Colors.purple,
                        showProgress: true,
                        progressValue:
                            (_recognitionAnalysis['recognitionAccuracy'] ??
                                0.95),
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
        const SizedBox(height: 20),
        // 错误类型分布图表 - 增大显示区域
        if (_recognitionAnalysis['errorTypeDistribution'] != null) ...[
          _buildSectionTitle('错误类型分布'),
          const SizedBox(height: 12),
          SizedBox(
            height: 280, // 增大图表高度
            child: WorkloadPieChart(
              data: Map<String, double>.from(
                (_recognitionAnalysis['errorTypeDistribution']
                        as Map<String, int>)
                    .map((key, value) => MapEntry(key, value.toDouble())),
              ),
              size: 260, // 增大图表尺寸
            ),
          ),
          const SizedBox(height: 20),
        ],
      ],
    );
  }

  /// 🎯 货物流转分析内容
  Widget _buildCargoFlowContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 货物流转指标
        LayoutBuilder(
          builder: (context, constraints) {
            final cardWidth = (constraints.maxWidth - 12) / 2;
            return Column(
              children: [
                Row(
                  children: [
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '进库数量',
                        '${(_cargoFlowAnalysis['inboundCount'] ?? 0)} 批',
                        Icons.input,
                        Colors.blue,
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '出库数量',
                        '${(_cargoFlowAnalysis['outboundCount'] ?? 0)} 批',
                        Icons.output,
                        Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '库存周转率',
                        '${(_cargoFlowAnalysis['turnoverRate'] ?? 0.0).toStringAsFixed(1)}',
                        Icons.refresh,
                        Colors.orange,
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '平均停留时间',
                        '${(_cargoFlowAnalysis['averageStayTime'] ?? 0.0).toStringAsFixed(1)} 天',
                        Icons.schedule,
                        Colors.purple,
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
        const SizedBox(height: 20),
        // 货物流转趋势图
        _buildSectionTitle('货物流转趋势'),
        const SizedBox(height: 12),
        SizedBox(
          height: 280, // 增大图表高度
          child: _buildCargoFlowTrendChart(),
        ),
      ],
    );
  }

  /// 🎯 质量控制分析内容
  Widget _buildQualityControlContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 质量控制指标
        LayoutBuilder(
          builder: (context, constraints) {
            final cardWidth = (constraints.maxWidth - 12) / 2;
            return Column(
              children: [
                Row(
                  children: [
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '合格率',
                        '${((_qualityControlAnalysis['qualityRate'] ?? 0.0) * 100).toStringAsFixed(1)}%',
                        Icons.verified,
                        Colors.green,
                        showProgress: true,
                        progressValue:
                            (_qualityControlAnalysis['qualityRate'] ?? 0.0),
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '检验批次',
                        '${(_qualityControlAnalysis['inspectionBatches'] ?? 0)} 批',
                        Icons.batch_prediction,
                        Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '不合格数',
                        '${(_qualityControlAnalysis['defectCount'] ?? 0)} 批',
                        Icons.error,
                        Colors.red,
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '返工率',
                        '${((_qualityControlAnalysis['reworkRate'] ?? 0.0) * 100).toStringAsFixed(1)}%',
                        Icons.refresh,
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
        const SizedBox(height: 20),
        // 质量分布图表
        _buildSectionTitle('质量分布'),
        const SizedBox(height: 12),
        SizedBox(
          height: 280, // 增大图表高度
          child: _buildQualityDistributionChart(),
        ),
      ],
    );
  }

  /// 🎯 实时作业监控内容
  Widget _buildRealTimeOperationContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 实时作业指标
        LayoutBuilder(
          builder: (context, constraints) {
            final cardWidth = (constraints.maxWidth - 12) / 2;
            return Column(
              children: [
                Row(
                  children: [
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '进行中',
                        '${(_realTimeOperationData['ongoingTasks'] ?? 0)} 个',
                        Icons.play_circle,
                        Colors.blue,
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '已完成',
                        '${(_realTimeOperationData['completedTasks'] ?? 0)} 个',
                        Icons.check_circle,
                        Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '未开始',
                        '${(_realTimeOperationData['pendingTasks'] ?? 0)} 个',
                        Icons.pause_circle,
                        Colors.orange,
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '异常',
                        '${(_realTimeOperationData['errorTasks'] ?? 0)} 个',
                        Icons.error,
                        Colors.red,
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
        const SizedBox(height: 20),
        // 作业状态分布
        _buildSectionTitle('作业状态分布'),
        const SizedBox(height: 12),
        SizedBox(
          height: 280, // 增大图表高度
          child: _buildOperationStatusChart(),
        ),
      ],
    );
  }

  /// 🎯 成本效益分析内容
  Widget _buildCostBenefitContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 成本效益指标
        LayoutBuilder(
          builder: (context, constraints) {
            final cardWidth = (constraints.maxWidth - 12) / 2;
            return Column(
              children: [
                Row(
                  children: [
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '人工成本',
                        '¥${(_costBenefitAnalysis['laborCost'] ?? 0.0).toStringAsFixed(0)}',
                        Icons.person,
                        Colors.blue,
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '设备成本',
                        '¥${(_costBenefitAnalysis['equipmentCost'] ?? 0.0).toStringAsFixed(0)}',
                        Icons.precision_manufacturing,
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '总收益',
                        '¥${(_costBenefitAnalysis['totalRevenue'] ?? 0.0).toStringAsFixed(0)}',
                        Icons.trending_up,
                        Colors.green,
                      ),
                    ),
                    const SizedBox(width: 12),
                    SizedBox(
                      width: cardWidth,
                      child: _buildMetricCard(
                        '投资回报率',
                        '${((_costBenefitAnalysis['roi'] ?? 0.0) * 100).toStringAsFixed(1)}%',
                        Icons.analytics,
                        Colors.purple,
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
        const SizedBox(height: 20),
        // 成本分析图表
        _buildSectionTitle('成本分析'),
        const SizedBox(height: 12),
        SizedBox(
          height: 280, // 增大图表高度
          child: _buildCostAnalysisChart(),
        ),
      ],
    );
  }

  /// 新增：货物流转趋势图表
  Widget _buildCargoFlowTrendChart() {
    // 模拟趋势数据
    final trendData = {
      '周一': 45.0,
      '周二': 52.0,
      '周三': 48.0,
      '周四': 61.0,
      '周五': 55.0,
      '周六': 38.0,
      '周日': 42.0,
    };

    return WorkloadBarChart(
      data: trendData,
      height: 280,
      title: '每日货物流转量',
      showLabels: true,
    );
  }

  /// 新增：质量分布图表
  Widget _buildQualityDistributionChart() {
    final qualityData = {
      '优秀': 65.0,
      '良好': 25.0,
      '合格': 8.0,
      '不合格': 2.0,
    };

    return WorkloadPieChart(
      data: qualityData,
      size: 260,
    );
  }

  /// 新增：作业状态图表
  Widget _buildOperationStatusChart() {
    final statusData = {
      '进行中': 45.0,
      '已完成': 120.0,
      '未开始': 25.0,
      '异常': 3.0,
    };

    return WorkloadPieChart(
      data: statusData,
      size: 260,
    );
  }

  /// 新增：成本分析图表
  Widget _buildCostAnalysisChart() {
    final costData = {
      '人工成本': 45000.0,
      '设备成本': 28000.0,
      '运营成本': 15000.0,
      '其他成本': 8000.0,
    };

    return WorkloadPieChart(
      data: costData,
      size: 260,
    );
  }

  /// 🎯 通用指标卡片
  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    bool showProgress = false,
    double progressValue = 0.0,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.white70,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          if (showProgress) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: progressValue,
              backgroundColor: Colors.white.withOpacity(0.1),
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 3,
            ),
          ],
        ],
      ),
    );
  }

  /// 🎯 章节标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    );
  }

  /// 🎯 识别效率分析区域
  Widget _buildRecognitionEfficiencySection() {
    return PremiumGlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.speed, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              Text(
                '识别效率分析',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // 识别成功率仪表盘
              Expanded(
                child: RecognitionEfficiencyGauge(
                  value:
                      (_recognitionAnalysis['recognitionSuccessRate'] ?? 0.0) *
                          100,
                  label: '识别成功率',
                  color: Colors.green,
                ),
              ),
              // 识别速度仪表盘
              Expanded(
                child: RecognitionEfficiencyGauge(
                  value: (_recognitionAnalysis['recognitionSpeed'] ?? 0.0)
                      .clamp(0.0, 100.0),
                  label: '识别速度',
                  color: Colors.blue,
                ),
              ),
              // 平均识别时间
              Expanded(
                child: Column(
                  children: [
                    Text(
                      '${(_recognitionAnalysis['averageRecognitionTime'] ?? 0.0).toStringAsFixed(0)}ms',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '平均识别时间',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // 错误类型分布
          if (_recognitionAnalysis['errorTypeDistribution'] != null) ...[
            Text(
              '错误类型分布',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            WorkloadPieChart(
              data: Map<String, double>.from(
                (_recognitionAnalysis['errorTypeDistribution']
                        as Map<String, int>)
                    .map((key, value) => MapEntry(key, value.toDouble())),
              ),
              size: 120,
            ),
          ],
          const SizedBox(height: 16),
          // 识别时间热力图
          if (_recognitionAnalysis['hourlyRecognitionTimes'] != null) ...[
            Text(
              '识别时间热力图',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            WorkloadHeatmap(
              data: Map<String, List<double>>.from(
                (_recognitionAnalysis['hourlyRecognitionTimes']
                    as Map<String, List<double>>),
              ),
              title: '各时段识别时间分布（毫秒）',
            ),
          ],
        ],
      ),
    );
  }

  /// 🎯 货物流转分析区域
  Widget _buildCargoFlowSection() {
    return PremiumGlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.local_shipping, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Text(
                '货物流转分析',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // 产品流转分布
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '产品流转分布',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    WorkloadPieChart(
                      data: Map<String, double>.from(
                        (_cargoFlowAnalysis['productCodeFlow']
                                    as Map<String, int>? ??
                                {})
                            .map((key, value) =>
                                MapEntry(key, value.toDouble())),
                      ),
                      size: 120,
                    ),
                  ],
                ),
              ),
              // 仓库流转分布
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '仓库流转分布',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    WorkloadBarChart(
                      data: Map<String, double>.from(
                        (_cargoFlowAnalysis['warehouseFlow']
                                    as Map<String, int>? ??
                                {})
                            .map((key, value) =>
                                MapEntry(key, value.toDouble())),
                      ),
                      height: 120,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // 货物状态分布
          if (_cargoFlowAnalysis['cargoStatusDistribution'] != null) ...[
            Text(
              '货物状态分布',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            WorkloadBarChart(
              data: Map<String, double>.from(
                (_cargoFlowAnalysis['cargoStatusDistribution']
                        as Map<String, int>)
                    .map((key, value) => MapEntry(key, value.toDouble())),
              ),
              height: 120,
            ),
          ],
        ],
      ),
    );
  }

  /// 🎯 质量控制分析区域
  Widget _buildQualityControlSection() {
    return PremiumGlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.verified, color: Colors.orange, size: 20),
              const SizedBox(width: 8),
              Text(
                '质量控制分析',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // 质量指标概览
          Row(
            children: [
              Expanded(
                  child: _buildQualityMetricCard(
                '总照片数',
                '${_qualityControlAnalysis['qualityMetrics']?['totalPhotos'] ?? 0}',
                Icons.photo_camera,
                Colors.blue,
              )),
              Expanded(
                  child: _buildQualityMetricCard(
                '验证通过',
                '${_qualityControlAnalysis['qualityMetrics']?['verifiedPhotos'] ?? 0}',
                Icons.check_circle,
                Colors.green,
              )),
              Expanded(
                  child: _buildQualityMetricCard(
                '识别失败',
                '${_qualityControlAnalysis['qualityMetrics']?['failedPhotos'] ?? 0}',
                Icons.error,
                Colors.red,
              )),
              Expanded(
                  child: _buildQualityMetricCard(
                '人工确认',
                '${_qualityControlAnalysis['qualityMetrics']?['manualVerifiedPhotos'] ?? 0}',
                Icons.person,
                Colors.purple,
              )),
            ],
          ),
          const SizedBox(height: 16),
          // 缺陷类型分布
          if (_qualityControlAnalysis['defectTypeDistribution'] != null) ...[
            Text(
              '缺陷类型分布',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 280,
              child: WorkloadPieChart(
                data: Map<String, double>.from(
                  (_qualityControlAnalysis['defectTypeDistribution']
                          as Map<String, int>)
                      .map((key, value) => MapEntry(key, value.toDouble())),
                ),
                size: 260,
              ),
            ),
          ],
          const SizedBox(height: 16),
          // 检验通过率
          if (_qualityControlAnalysis['inspectionPassRates'] != null) ...[
            Text(
              '各模板检验通过率',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            WorkloadBarChart(
              data: Map<String, double>.from(
                (_qualityControlAnalysis['inspectionPassRates']
                        as Map<String, double>)
                    .map((key, value) => MapEntry(key, value * 100)), // 转换为百分比
              ),
              height: 120,
            ),
          ],
        ],
      ),
    );
  }

  /// 🎯 实时作业监控区域
  Widget _buildRealTimeOperationSection() {
    return PremiumGlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.monitor, color: Colors.purple, size: 20),
              const SizedBox(width: 8),
              Text(
                '实时作业监控',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // 任务状态看板
          Row(
            children: [
              Expanded(
                  child: _buildStatusCard(
                '进行中',
                '${_realTimeOperationData['taskStatusBoard']?['inProgress'] ?? 0}',
                Icons.play_circle,
                Colors.blue,
              )),
              Expanded(
                  child: _buildStatusCard(
                '已完成',
                '${_realTimeOperationData['taskStatusBoard']?['completed'] ?? 0}',
                Icons.check_circle,
                Colors.green,
              )),
              Expanded(
                  child: _buildStatusCard(
                '失败',
                '${_realTimeOperationData['taskStatusBoard']?['failed'] ?? 0}',
                Icons.error_outline,
                Colors.red,
              )),
            ],
          ),
          const SizedBox(height: 16),
          // 设备状态
          if (_realTimeOperationData['deviceStatusCount'] != null) ...[
            Text(
              '设备状态',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            WorkloadPieChart(
              data: Map<String, double>.from(
                (_realTimeOperationData['deviceStatusCount']
                        as Map<String, int>)
                    .map((key, value) => MapEntry(key, value.toDouble())),
              ),
              size: 120,
            ),
          ],
          const SizedBox(height: 16),
          // 异常报警
          if (_realTimeOperationData['alarmList'] != null) ...[
            Text(
              '异常报警',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            ...((_realTimeOperationData['alarmList']
                    as List<Map<String, dynamic>>)
                .take(3)
                .map((alarm) {
              return Container(
                margin: const EdgeInsets.only(bottom: 4),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning,
                      color: Colors.orange,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        alarm['message'] ?? '',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList()),
          ],
        ],
      ),
    );
  }

  /// 🎯 成本效益分析区域
  Widget _buildCostBenefitSection() {
    return PremiumGlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.attach_money, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Text(
                '成本效益分析',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // 成本概览
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildCostMetricCard(
                '总人工成本',
                '¥${(_costBenefitAnalysis['totalLaborCost'] ?? 0.0).toStringAsFixed(0)}',
                Icons.people,
                Colors.blue,
              ),
              _buildCostMetricCard(
                '每吨成本',
                '¥${(_costBenefitAnalysis['costPerTon'] ?? 0.0).toStringAsFixed(1)}',
                Icons.scale,
                Colors.orange,
              ),
              _buildCostMetricCard(
                'ROI提升',
                '${(_costBenefitAnalysis['efficiencyROI']?['roiPercentage'] ?? 0.0).toStringAsFixed(1)}%',
                Icons.trending_up,
                Colors.green,
              ),
            ],
          ),
          const SizedBox(height: 16),
          // 角色成本分布
          if (_costBenefitAnalysis['laborCostByRole'] != null) ...[
            Text(
              '角色成本分布',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            WorkloadBarChart(
              data: Map<String, double>.from(
                (_costBenefitAnalysis['laborCostByRole']
                    as Map<String, double>),
              ),
              height: 120,
            ),
          ],
        ],
      ),
    );
  }

  /// 质量指标卡片
  Widget _buildQualityMetricCard(
      String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          title,
          style: TextStyle(
            fontSize: 10,
            color: Colors.white70,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 状态卡片
  Widget _buildStatusCard(
      String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          title,
          style: TextStyle(
            fontSize: 10,
            color: Colors.white70,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 成本指标卡片
  Widget _buildCostMetricCard(
      String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          title,
          style: TextStyle(
            fontSize: 10,
            color: Colors.white70,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
