import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../services/strict_security_service.dart';
import '../services/app_security_service.dart';
import '../services/enterprise_license_service.dart';
import '../utils/theme_colors.dart';
import '../services/logging_service.dart';
import '../widgets/premium_glass_card.dart';
import '../widgets/themed_button.dart';
import '../utils/strict_route_guard.dart';
import '../core/lifecycle_mixin.dart';
import '../core/providers/app_providers.dart';
import '../services/hardware_fingerprint.dart';
import '../utils/gesture_navigation.dart';

/// 🔐 统一的激活页面 - 支持试用期激活和许可证激活
class StrictActivationPage extends ConsumerStatefulWidget {
  const StrictActivationPage({super.key});

  @override
  ConsumerState<StrictActivationPage> createState() => _StrictActivationPageState();
}

class _StrictActivationPageState extends ConsumerState<StrictActivationPage>
    with LifecycleMixin<StrictActivationPage> {
  final _activationCodeController = TextEditingController();

  bool _isLoading = false;
  String _statusMessage = '';
  Map<String, dynamic> _licenseStatus = {};
  bool _canActivateTrial = true;
  String _deviceId = '';

  @override
  void initState() {
    super.initState();
    LoggingService.info('🔐 激活页面加载', tag: 'StrictActivation');
    _loadLicenseStatus();
    _loadDeviceId();
  }

  @override
  void onLifecycleDispose() {
    _activationCodeController.dispose();
  }

  /// 加载设备ID
  Future<void> _loadDeviceId() async {
    try {
      final deviceId = await AppSecurityService.getDeviceId();
      setState(() {
        _deviceId = deviceId;
      });
      LoggingService.info('设备ID加载完成: $_deviceId', tag: 'StrictActivation');
    } catch (e) {
      LoggingService.error('加载设备ID失败', error: e, tag: 'StrictActivation');
      setState(() {
        _deviceId = '获取失败';
      });
    }
  }

  /// 加载许可证状态
  Future<void> _loadLicenseStatus() async {
    try {
      final status = await StrictSecurityService.getLicenseStatus();
      setState(() {
        _licenseStatus = status;
        _canActivateTrial = status['userRole'] == UserRole.trial;
      });
      LoggingService.info('许可证状态加载完成: $status', tag: 'StrictActivation');
    } catch (e) {
      LoggingService.error('加载许可证状态失败', error: e, tag: 'StrictActivation');
      _showMessage('加载状态失败: $e', false);
    }
  }

  /// 激活试用期
  Future<void> _activateTrial() async {
    if (!_canActivateTrial) {
      _showMessage('试用期只能激活一次', false);
      return;
    }

    setState(() {
      _isLoading = true;
      _statusMessage = '正在激活试用期...';
    });

    try {
      final result = await StrictSecurityService.activateTrial();
      
      if (result['success'] as bool) {
        _showMessage(result['message'] as String, true);
        
        // 🔧 修复：激活成功后立即刷新安全状态并跳转到主页
        if (result['shouldNavigateHome'] as bool) {
          // 立即刷新路由守卫的安全状态缓存
          await StrictRouteGuard.refreshSecurityStatus();
          
          // 无延迟直接跳转
          if (mounted) {
            context.go('/home');
          }
        }
      } else {
        _showMessage(result['message'] as String, false);
      }
      
      await _loadLicenseStatus();
    } catch (e) {
      LoggingService.error('激活试用期失败', error: e, tag: 'StrictActivation');
      _showMessage('激活失败: $e', false);
    } finally {
      setState(() {
        _isLoading = false;
        _statusMessage = '';
      });
    }
  }

  /// 使用激活码激活
  Future<void> _activateWithCode() async {
    final code = _activationCodeController.text.trim();

    if (code.isEmpty) {
      _showMessage('请输入激活码', false);
      return;
    }
    
    setState(() {
      _isLoading = true;
      _statusMessage = '正在验证激活码...';
    });

    try {
      final result = await StrictSecurityService.validateActivationCode(code);
      
      if (result['success'] as bool) {
        _showMessage(result['message'] as String, true);
        _activationCodeController.clear();
        
        // 🔧 修复：激活成功后立即刷新安全状态并跳转到主页
        if (result['shouldNavigateHome'] as bool) {
          // 立即刷新路由守卫的安全状态缓存
          await StrictRouteGuard.refreshSecurityStatus();
          
          // 无延迟直接跳转
          if (mounted) {
            context.go('/home');
          }
        }
      } else {
        _showMessage(result['message'] as String, false);
      }
      
      await _loadLicenseStatus();
    } catch (e) {
      LoggingService.error('激活码验证失败', error: e, tag: 'StrictActivation');
      _showMessage('验证失败: $e', false);
    } finally {
      setState(() {
        _isLoading = false;
        _statusMessage = '';
      });
    }
  }

  /// 显示消息
  void _showMessage(String message, bool isSuccess) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isSuccess ? Icons.check_circle : Icons.error,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: isSuccess ? Colors.green : Colors.red,
        duration: Duration(seconds: isSuccess ? 2 : 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 构建状态卡片 - 现代化主题风格
  Widget _buildStatusCard() {
    final userRole = _licenseStatus['userRole'] as UserRole?;
    final isValid = _licenseStatus['isValid'] as bool? ?? false;
    final remainingDays = _licenseStatus['remainingDays'] as int? ?? 0;

    String statusText = '未激活';
    Color statusColor = ThemeColors.warning;
    IconData statusIcon = Icons.info;

    if (userRole == UserRole.superAdmin) {
      statusText = '超级管理员';
      statusColor = ThemeColors.secondary;
      statusIcon = Icons.admin_panel_settings;
    } else if (userRole == UserRole.activated && isValid) {
      statusText = '已激活，剩余$remainingDays天';
      statusColor = ThemeColors.success;
      statusIcon = Icons.verified;
    } else if (userRole == UserRole.trial) {
      if (isValid) {
        statusText = '试用期已激活，剩余$remainingDays天';
        statusColor = ThemeColors.info;
        statusIcon = Icons.schedule;
      } else {
        statusText = '试用期已过期';
        statusColor = ThemeColors.error;
        statusIcon = Icons.schedule_outlined;
      }
    }

    return Container(
      padding: ThemeColors.cardPaddingLarge,
      decoration: ModernUIHelper.modernCardDecoration(
        isGlass: true,
      ),
      child: Row(
        children: [
          Icon(statusIcon, color: statusColor, size: 24),
          const SizedBox(width: 12),
          Text(
            '激活状态',
            style: TextStyles.modernLabel.copyWith(
              color: Colors.white70,
            ),
          ),
          const Spacer(),
          Text(
            statusText,
            style: TextStyles.body.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建设备ID显示区域 - 现代化主题风格
  Widget _buildDeviceIdSection() {
    return Container(
      padding: ThemeColors.cardPaddingLarge,
      decoration: ModernUIHelper.modernCardDecoration(
        isGlass: true,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.fingerprint,
                color: Colors.white70,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '设备ID',
                style: TextStyles.modernLabel,
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(ThemeColors.modernRadiusSmall),
                    border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
                  ),
                  child: Text(
                    _deviceId.isEmpty ? '正在获取...' : _deviceId,
                    style: TextStyles.modernDescription.copyWith(
                      fontFamily: 'monospace',
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: _deviceId.isNotEmpty ? () {
                  Clipboard.setData(ClipboardData(text: _deviceId));
                  _showMessage('设备ID已复制到剪贴板', true);
                } : null,
                icon: const Icon(
                  Icons.copy,
                  size: 18,
                  color: Colors.white70,
                ),
                tooltip: '复制设备ID',
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '请将此设备ID提供给管理员生成激活码',
            style: TextStyles.caption.copyWith(
              color: Colors.white60,
            ),
          ),
        ],
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    // 严格禁用手势导航
    return GestureNavigation.disableGestures(
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: Container(
          decoration: const BoxDecoration(
            gradient: ThemeColors.primaryGradient, // 使用系统主题渐变
          ),
          child: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
              child: Column(
                children: [
                  // 应用图标和标题
                  const SizedBox(height: 40),
                  const Icon(
                    Icons.shield,
                    size: 80,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    '装运卫士+',
                    style: TextStyles.modernTitle, // 使用现代化标题样式
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'ML Kit V2专业版',
                    style: TextStyles.modernDescription, // 使用现代化描述样式
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 40),

                  // 设备ID显示
                  _buildDeviceIdSection(),
                  const SizedBox(height: 24),

                  // 激活状态卡片
                  _buildStatusCard(),
                  const SizedBox(height: 24),

                  // 激活码输入区域
                  Text(
                    '激活码',
                    style: TextStyles.modernLabel, // 使用现代化标签样式
                  ),
                  const SizedBox(height: 12),
                  Container(
                    decoration: ModernUIHelper.modernCardDecoration(
                      isGlass: true,
                    ),
                    child: TextField(
                      controller: _activationCodeController,
                      style: TextStyles.body, // 使用标准正文样式
                      decoration: ModernUIHelper.modernInputDecoration(
                        hintText: '请输入许可证码 (PROF-XXXX-...)',
                        prefixIcon: const Icon(Icons.vpn_key, color: Colors.white70),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // 激活按钮
                  SizedBox(
                    width: double.infinity,
                    height: ThemeColors.buttonHeightLarge, // 使用标准按钮高度
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _activateWithCode,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ThemeColors.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusSmall),
                        ),
                        elevation: 2,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.check_circle, size: 18),
                                const SizedBox(width: 8),
                                Text(
                                  '激活许可证',
                                  style: TextStyles.modernButton,
                                ),
                              ],
                            ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 分隔线
                  Row(
                    children: [
                      Expanded(child: Container(height: 1, color: Colors.white30)),
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          '或',
                          style: TextStyle(color: Colors.white60, fontSize: 12),
                        ),
                      ),
                      Expanded(child: Container(height: 1, color: Colors.white30)),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // 试用按钮
                  SizedBox(
                    width: double.infinity,
                    height: ThemeColors.buttonHeightLarge, // 使用标准按钮高度
                    child: ElevatedButton(
                      onPressed: _canActivateTrial && !_isLoading ? _activateTrial : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white.withValues(alpha: 0.1),
                        foregroundColor: Colors.white,
                        side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusSmall),
                        ),
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12), // 增加内边距
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min, // 防止内容被拉伸
                        children: [
                          const Icon(Icons.schedule, size: 20),
                          const SizedBox(width: 12),
                          Flexible( // 使用Flexible包裹文本，防止溢出
                            child: Text(
                              '免费试用7天',
                              style: TextStyles.modernButton.copyWith(
                                fontSize: 15, // 稍微减小字体以确保显示完整
                              ),
                              overflow: TextOverflow.ellipsis, // 防止文本溢出
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '试用期间只能激活一次，激活后立即生效',
                    style: TextStyles.modernDescription.copyWith(
                      fontSize: 13, // 调整说明文字大小
                      color: Colors.white60,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2, // 允许换行
                    overflow: TextOverflow.visible, // 允许文本自动换行
                  ),
                  const SizedBox(height: 40), // 底部间距，防止被安全区域遮挡
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
