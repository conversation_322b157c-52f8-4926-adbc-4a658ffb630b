import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../widgets/industrial_logo.dart';
import '../utils/theme_colors.dart';
import '../widgets/themed_card.dart';
import '../utils/simple_navigation_helper.dart';
import '../core/lifecycle_mixin.dart';
import '../core/providers/app_providers.dart';

/// 🏢 关于我们页面
/// 展示装运卫士的品牌定位、技术优势和企业级特性
class AboutPage extends ConsumerStatefulWidget {
  const AboutPage({Key? key}) : super(key: key);

  @override
  ConsumerState<AboutPage> createState() => _AboutPageState();
}

class _AboutPageState extends ConsumerState<AboutPage>
    with LifecycleMixin<AboutPage> {
  PackageInfo _packageInfo = PackageInfo(
    appName: '未知',
    packageName: '未知',
    version: '加载中...',
    buildNumber: '加载中...',
  );

  @override
  void initState() {
    super.initState();
    _initPackageInfo();
  }

  Future<void> _initPackageInfo() async {
    final info = await PackageInfo.fromPlatform();
    setState(() {
      _packageInfo = info;
    });
  }

  void _handleBackNavigation() {
    SimpleNavigationHelper.goBack(context);
  }

  @override
  Widget build(BuildContext context) {
    return SimpleNavigationHelper.buildStandardPage(
      onBackPressed: _handleBackNavigation,
      enableSwipeBack: true,
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: ThemeColors.primaryGradient,
          ),
          child: CustomScrollView(
            slivers: [
              _buildAppBar(context),
              SliverPadding(
                padding: const EdgeInsets.all(20),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    _buildLogoSection(),
                    const SizedBox(height: 30),
                    _buildMissionSection(),
                    const SizedBox(height: 30),
                    _buildTechAdvantagesSection(),
                    const SizedBox(height: 30),
                    _buildEnterpriseSection(),
                    const SizedBox(height: 30),
                    _buildVersionInfoSection(),
                    const SizedBox(height: 30),
                    _buildContactSection(),
                    const SizedBox(height: 50),
                  ]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🎯 自定义应用栏
  Widget _buildAppBar(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 80,
      floating: true,
      pinned: true,
      backgroundColor: ThemeColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      automaticallyImplyLeading: false,
      centerTitle: true,
      title: const Text(
        '关于装运卫士',
        style: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  /// 🏷️ Logo展示区域
  Widget _buildLogoSection() {
    return ThemedCard(
      type: CardType.glass,
      padding: const EdgeInsets.all(30),
      margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
      child: const Column(
        children: [
          IndustrialLogo(
            size: 120,
            primaryColor: Color(0xFF1976D2),
            accentColor: Color(0xFF1565C0),
            showText: true,
            showTagline: true,
          ),
          const SizedBox(height: 20),
          Text(
            '化工物流专业版',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w700,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '工业级物流标签智能识别系统',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  /// 🎯 使命愿景
  Widget _buildMissionSection() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: ThemeColors.primary.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.flag,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              const Text(
                '品牌使命',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w800,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          const Text(
            '专注工业物流场景，打造极致识别体验',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.white,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: ThemeColors.glassBackground,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withOpacity(0.12)),
            ),
            child: const Text(
              '装运卫士深耕工业物流领域，专门针对极端环境下的标签识别场景进行算法优化。我们的目标是在套膜、弱光、反光等挑战性环境中，为企业提供超越通用OCR软件的专业级识别解决方案。',
              style: TextStyle(
                fontSize: 15,
                color: ThemeColors.textOnGradient,
                height: 1.6,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 🚀 技术优势
  Widget _buildTechAdvantagesSection() {
    return ThemedCard(
      type: CardType.glass,
      child: Padding(
        padding: const EdgeInsets.all(0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: ThemeColors.accent.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.precision_manufacturing,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                const Text(
                  'Google ML Kit核心技术',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.w800,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // 智能识别技术架构
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: ThemeColors.primaryGradient,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: ThemeColors.primary.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '🏆 Google ML Kit专业识别架构',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: Color(0xFF1976D2),
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildTechLayer('第1层', '专业图像预处理引擎',
                      '对比度增强 • 图像锐化 • 智能降噪 • 尺寸优化\n工业环境专门适配 • 150ms高速处理'),
                  _buildTechLayer('第2层', 'Google ML Kit深度集成',
                      'ML Kit V2文本识别引擎 • Android原生调用\n化工标签专业优化 • 智能降级机制'),
                  _buildTechLayer('第3层', '智能缓存系统',
                      'LRU缓存策略 • 请求去重 • 异步处理\n60%+缓存命中率 • 瞬间响应重复请求'),
                  _buildTechLayer(
                      '第4层', '智能降级策略', 'ML Kit主引擎 + 图像增强备用处理\n自动质量检测 • 无缝错误恢复'),
                  _buildTechLayer('第5层', '性能监控优化', '图像特征分析 • 环境自适应调整 • 历史数据学习'),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // 技术指标网格 - 响应式布局
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                          child: _buildTechMetric(
                              '🎯', '高准确率', 'ML Kit V2', '实际测试数据')),
                      const SizedBox(width: 12),
                      Expanded(
                          child: _buildTechMetric(
                              '⚡', '150ms', '预处理速度', '20倍性能提升')),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                          child: _buildTechMetric(
                              '🔧', 'V2引擎', 'ML Kit最新', '化工专业优化')),
                      const SizedBox(width: 12),
                      Expanded(
                          child:
                              _buildTechMetric('🚀', '本地处理', '完全离线', '数据安全')),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // 核心算法技术细节
            ThemedCard(
              type: CardType.glass,
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildAlgorithmDetail('🤖 ML Kit V2引擎',
                      'Google ML Kit文本识别 V2 • 中英文字符识别 • Android原生调用 • 超时保护机制'),
                  const Divider(height: 24),
                  _buildAlgorithmDetail(
                      '💎 图像预处理增强', '12种专业算法：对比度增强、锐化处理、智能降噪、尺寸优化、格式自适应编码等'),
                  const Divider(height: 24),
                  _buildAlgorithmDetail(
                      '⚡ 高性能缓存', 'LRU缓存策略 • 请求去重合并 • 异步处理队列 • 性能统计监控'),
                  const Divider(height: 24),
                  _buildAlgorithmDetail(
                      '🧠 智能降级系统', '质量自动检测 • ML Kit主引擎切换 • 错误恢复机制 • 无缝用户体验'),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // 性能对比
            ThemedCard(
              type: CardType.glass,
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Text(
                    '🏆 专业版 vs 基础版性能对比',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                          child: _buildPerformanceItem('基础版本', '85%', false)),
                      Expanded(
                          child: _buildPerformanceItem('增强版本', '90%', false)),
                      Expanded(
                          child: _buildPerformanceItem('专业版本', '高准确率', true)),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    '极端环境适应：套膜环境 • 弱光环境 • 反光环境',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white70,
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 🏗️ 构建技术层次
  Widget _buildTechLayer(String layer, String title, String content) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ThemeColors.glassBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white.withOpacity(0.12)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: ThemeColors.primary,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  layer,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: ThemeColors.textOnGradient,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: TextStyle(
              fontSize: 14,
              color: ThemeColors.textOnGradient.withOpacity(0.9),
              height: 1.5,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  /// 🔧 构建算法细节
  Widget _buildAlgorithmDetail(String title, String content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w700,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          flex: 3,
          child: Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white70,
              height: 1.5,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }

  /// 📊 构建性能对比项
  Widget _buildPerformanceItem(
      String version, String accuracy, bool isHighlight) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isHighlight
            ? ThemeColors.primary.withOpacity(0.1)
            : ThemeColors.glassBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isHighlight
              ? ThemeColors.primary
              : Colors.white.withOpacity(0.12),
          width: isHighlight ? 2 : 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            version,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: isHighlight
                  ? ThemeColors.primary
                  : ThemeColors.textOnGradient,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            accuracy,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w800,
              color: isHighlight
                  ? ThemeColors.primary
                  : ThemeColors.textOnGradient,
            ),
          ),
          if (isHighlight)
            const Text(
              '🏆',
              style: TextStyle(fontSize: 16),
            ),
        ],
      ),
    );
  }

  /// 🏢 企业级特性
  Widget _buildEnterpriseSection() {
    return ThemedCard(
      type: CardType.glass,
      padding: const EdgeInsets.all(0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: ThemeColors.primary.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.business,
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              const Text(
                '企业级特性',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w800,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ..._buildEnterpriseFeatures(),
        ],
      ),
    );
  }

  /// 🏗️ 构建企业级特性列表
  List<Widget> _buildEnterpriseFeatures() {
    final features = [
      {'icon': '🔐', 'title': '企业级安全', 'desc': '设备绑定认证 • 离线激活机制 • 防篡改保护'},
      {'icon': '⚡', 'title': '性能优化', 'desc': '内存智能管理 • 异步批量处理 • 连接池优化'},
      {'icon': '📊', 'title': '数据管理', 'desc': 'PDF报告生成 • 批量数据同步 • 历史记录管理'},
      {'icon': '🎛️', 'title': '灵活配置', 'desc': '模板自定义 • 参数调优 • 多场景适配'},
    ];

    return features.map((feature) {
      return ThemedCard(
        type: CardType.glass,
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Color(0xFF1976D2).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                feature['icon']!,
                style: const TextStyle(fontSize: 20),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    feature['title']!,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    feature['desc']!,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white70,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }).toList();
  }

  /// 🏗️ 版本信息
  Widget _buildVersionInfoSection() {
    return ThemedCard(
      type: CardType.glass,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildInfoRow('应用版本', '装运卫士1.0'),
          const Divider(color: Colors.white24),
          _buildInfoRow('算法版本', 'Google ML Kit V2 文本识别引擎'),
          const Divider(color: Colors.white24),
          _buildInfoRow('发布日期', '2025年 Q3'),
          const Divider(color: Colors.white24),
          _buildInfoRow('许可证类型', '企业版授权'),
        ],
      ),
    );
  }

  /// 📞 联系我们
  Widget _buildContactSection() {
    return ThemedCard(
      type: CardType.glass,
      child: Padding(
        padding: const EdgeInsets.all(0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: ThemeColors.primary.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.contact_support,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                const Text(
                  '技术支持',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.w800,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ThemedCard(
              type: CardType.glass,
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(child: _buildSupportItem('24小时技术热线')),
                      const SizedBox(width: 12),
                      Expanded(child: _buildSupportItem('现场部署指导')),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(child: _buildSupportItem('定制化算法优化')),
                      const SizedBox(width: 12),
                      Expanded(child: _buildSupportItem('企业级SLA保证')),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 🏗️ 构建技术指标卡片
  Widget _buildTechMetric(
      String icon, String value, String label, String desc) {
    return ThemedCard(
      type: CardType.glass,
      margin: const EdgeInsets.all(0),
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            icon,
            style: const TextStyle(fontSize: 24),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w800,
              color: ThemeColors.textOnGradient,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: ThemeColors.textOnGradient,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            desc,
            style: TextStyle(
              fontSize: 12,
              color: ThemeColors.textOnGradient.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 🏢 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: ThemeColors.textOnGradient.withOpacity(0.8),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              textAlign: TextAlign.end,
              style: const TextStyle(
                fontSize: 15,
                color: ThemeColors.textOnGradient,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 🏢 技术支持项目
  Widget _buildSupportItem(String text) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ThemeColors.glassBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.support_agent, color: Colors.white, size: 24),
          const SizedBox(height: 8),
          Text(
            text,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white,
              fontWeight: FontWeight.w600,
              height: 1.3,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
