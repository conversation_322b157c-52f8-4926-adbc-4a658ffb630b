import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/workload_models.dart';
import '../../repositories/config_repository.dart';
import '../../services/config_migration_service.dart';
import '../../services/hive_storage_service.dart';
import '../../services/shared_preferences_data_source.dart';
import '../../utils/theme_colors.dart';
import '../../utils/app_logger.dart';

/// 配置管理页面
/// 提供人员、仓库、小组、模板的管理功能
class ConfigManagementPage extends ConsumerStatefulWidget {
  const ConfigManagementPage({super.key});

  @override
  ConsumerState<ConfigManagementPage> createState() => _ConfigManagementPageState();
}

class _ConfigManagementPageState extends ConsumerState<ConfigManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late ConfigRepository _configRepository;
  late ConfigMigrationService _migrationService;
  
  bool _isLoading = false;
  String _statusMessage = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeServices();
  }

  void _initializeServices() {
    final hiveStorage = HiveStorageService();
    final backupStorage = SharedPreferencesDataSource();
    
    _configRepository = ConfigRepositoryImpl(
      hiveStorage: hiveStorage,
      backupStorage: backupStorage,
    );
    
    _migrationService = ConfigMigrationService(_configRepository);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('配置管理'),
        backgroundColor: ThemeColors.primary,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.people), text: '人员管理'),
            Tab(icon: Icon(Icons.warehouse), text: '仓库管理'),
            Tab(icon: Icon(Icons.groups), text: '小组管理'),
            Tab(icon: Icon(Icons.description), text: '模板管理'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: _performDataMigration,
            tooltip: '数据迁移',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettings,
            tooltip: '设置',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: ThemeColors.primaryGradient,
        ),
        child: Column(
          children: [
            if (_isLoading || _statusMessage.isNotEmpty) _buildStatusBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _WorkerManagementTab(configRepository: _configRepository),
                  _WarehouseManagementTab(configRepository: _configRepository),
                  _GroupManagementTab(configRepository: _configRepository),
                  _TemplateManagementTab(configRepository: _configRepository),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBar() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      color: _isLoading ? Colors.blue.withOpacity(0.1) : Colors.green.withOpacity(0.1),
      child: Row(
        children: [
          if (_isLoading) ...[
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              _statusMessage.isEmpty ? '正在处理...' : _statusMessage,
              style: const TextStyle(color: Colors.white),
            ),
          ),
          if (!_isLoading && _statusMessage.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.close, color: Colors.white),
              onPressed: () => setState(() => _statusMessage = ''),
            ),
        ],
      ),
    );
  }

  Future<void> _performDataMigration() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '开始数据迁移...';
    });

    try {
      final result = await _migrationService.migrateAllData();
      
      setState(() {
        _isLoading = false;
        _statusMessage = '数据迁移完成: 成功${result.migrated}项, 失败${result.failed}项';
      });

      if (result.hasErrors) {
        _showMigrationErrors(result);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('数据迁移成功完成！迁移了${result.migrated}项配置'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = '数据迁移失败: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('数据迁移失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showMigrationErrors(MigrationResult result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('迁移结果'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('成功: ${result.migrated}项'),
              Text('失败: ${result.failed}项'),
              Text('跳过: ${result.skipped}项'),
              if (result.errors.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text('错误详情:', style: TextStyle(fontWeight: FontWeight.bold)),
                ...result.errors.map((error) => Text('• $error')),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('配置设置'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.import_export),
              title: Text('导入/导出配置'),
              subtitle: Text('备份和恢复配置数据'),
            ),
            ListTile(
              leading: Icon(Icons.sync),
              title: Text('同步设置'),
              subtitle: Text('配置数据同步选项'),
            ),
            ListTile(
              leading: Icon(Icons.security),
              title: Text('安全设置'),
              subtitle: Text('数据加密和权限设置'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}

/// 工人管理标签页
class _WorkerManagementTab extends StatefulWidget {
  final ConfigRepository configRepository;

  const _WorkerManagementTab({required this.configRepository});

  @override
  State<_WorkerManagementTab> createState() => _WorkerManagementTabState();
}

class _WorkerManagementTabState extends State<_WorkerManagementTab> {
  List<WorkerConfig> _workers = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadWorkers();
  }

  Future<void> _loadWorkers() async {
    setState(() => _isLoading = true);
    try {
      final workers = await widget.configRepository.getWorkers();
      setState(() {
        _workers = workers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      AppLogger.error('加载工人配置失败: $e', tag: 'WorkerManagementTab');
    }
  }

  List<WorkerConfig> get _filteredWorkers {
    if (_searchQuery.isEmpty) return _workers;
    return _workers.where((worker) =>
        worker.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        worker.role.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        worker.warehouse.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        worker.group.toLowerCase().contains(_searchQuery.toLowerCase())
    ).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: '搜索工人...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: (value) => setState(() => _searchQuery = value),
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton.icon(
                onPressed: _addWorker,
                icon: const Icon(Icons.add),
                label: const Text('添加'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredWorkers.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.people_outline, size: 64, color: Colors.white54),
                          const SizedBox(height: 16),
                          Text(
                            _searchQuery.isEmpty ? '暂无工人配置' : '未找到匹配的工人',
                            style: const TextStyle(color: Colors.white70, fontSize: 16),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _loadWorkers,
                            child: const Text('刷新'),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: _filteredWorkers.length,
                      itemBuilder: (context, index) {
                        final worker = _filteredWorkers[index];
                        return Card(
                          color: Colors.white.withOpacity(0.1),
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: worker.isActive ? Colors.green : Colors.grey,
                              child: Text(
                                worker.name.isNotEmpty ? worker.name[0] : '?',
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                            title: Text(
                              worker.name,
                              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                            ),
                            subtitle: Text(
                              '${worker.role} • ${worker.warehouse} • ${worker.group}',
                              style: const TextStyle(color: Colors.white70),
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.edit, color: Colors.blue),
                                  onPressed: () => _editWorker(worker),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete, color: Colors.red),
                                  onPressed: () => _deleteWorker(worker),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
        ),
      ],
    );
  }

  void _addWorker() {
    // TODO: 实现添加工人对话框
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('添加工人功能开发中...')),
    );
  }

  void _editWorker(WorkerConfig worker) {
    // TODO: 实现编辑工人对话框
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('编辑工人: ${worker.name}')),
    );
  }

  void _deleteWorker(WorkerConfig worker) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除工人 "${worker.name}" 吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await widget.configRepository.deleteWorker(worker.id);
                await _loadWorkers();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('已删除工人: ${worker.name}')),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('删除失败: $e')),
                );
              }
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

/// 仓库管理标签页
class _WarehouseManagementTab extends StatelessWidget {
  final ConfigRepository configRepository;

  const _WarehouseManagementTab({required this.configRepository});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        '仓库管理功能开发中...',
        style: TextStyle(color: Colors.white70, fontSize: 16),
      ),
    );
  }
}

/// 小组管理标签页
class _GroupManagementTab extends StatelessWidget {
  final ConfigRepository configRepository;

  const _GroupManagementTab({required this.configRepository});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        '小组管理功能开发中...',
        style: TextStyle(color: Colors.white70, fontSize: 16),
      ),
    );
  }
}

/// 模板管理标签页
class _TemplateManagementTab extends StatelessWidget {
  final ConfigRepository configRepository;

  const _TemplateManagementTab({required this.configRepository});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        '模板管理功能开发中...',
        style: TextStyle(color: Colors.white70, fontSize: 16),
      ),
    );
  }
}
