import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/template_config.dart';
import '../utils/theme_colors.dart';
import '../utils/responsive_helper.dart';
import '../utils/simple_navigation_helper.dart';
import '../widgets/themed_card.dart';
import '../widgets/themed_button.dart';
import '../widgets/responsive_scaffold.dart';
import 'package:flutter/services.dart';
import '../core/lifecycle_mixin.dart';
import '../core/providers/app_providers.dart';

class TemplateSelectionPage extends ConsumerStatefulWidget {
  const TemplateSelectionPage({super.key});

  @override
  ConsumerState<TemplateSelectionPage> createState() => _TemplateSelectionPageState();
}

class _TemplateSelectionPageState extends ConsumerState<TemplateSelectionPage>
    with TickerProviderStateMixin, LifecycleMixin<TemplateSelectionPage> {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // 可用模板列表
  final List<String> _availableTemplates = ['平板车', '集装箱'];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  @override
  void onLifecycleDispose() {
    _animationController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SimpleNavigationHelper.buildStandardPage(
      onBackPressed: () {
        // 添加触觉反馈
        HapticFeedback.lightImpact();
        context.go('/home');
      },
      enableSwipeBack: true,
      child: ResponsiveScaffold(
        title: '选择模板',
        showBackButton: true,
        automaticallyImplyLeading: true,
        onBackPressed: () {
          HapticFeedback.lightImpact();
          context.go('/home');
        },
        body: Column(
          children: [
            Expanded(
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: _buildTemplateList(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateList() {
    return ResponsiveContainer(
      maxWidth: ResponsiveHelper.getResponsiveMaxWidth(
        context,
        mobileMaxWidth: double.infinity,
        tabletMaxWidth: 600,
        desktopMaxWidth: 800,
      ),
      child: ListView(
        padding: ResponsiveHelper.getResponsivePadding(
            context, const EdgeInsets.symmetric(horizontal: 20, vertical: 8)),
        children: [
          _buildProfessionalBanner(),
          SizedBox(
              height:
                  ResponsiveHelper.getResponsiveSpacing(context, 8)), // 响应式间距
          _buildTemplateCard('平板车'),
          SizedBox(
              height:
                  ResponsiveHelper.getResponsiveSpacing(context, 8)), // 响应式间距
          _buildTemplateCard('集装箱'),
          SizedBox(
              height:
                  ResponsiveHelper.getResponsiveSpacing(context, 8)), // 响应式间距
          _buildTipCard(),
          SizedBox(
              height: ResponsiveHelper.getResponsiveSpacing(
                  context, 20)), // 响应式底部留白
        ],
      ),
    );
  }

  Widget _buildProfessionalBanner() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: ModernUIHelper.modernCardDecoration(
        isGlass: true,
        isHovered: false,
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(18),
                  decoration: ModernUIHelper.modernButtonDecoration(
                    type: 'primary',
                    isNeumorphic: false,
                  ),
                  child: const Icon(Icons.workspace_premium,
                      color: Colors.white, size: 40),
                ),
                const SizedBox(width: 24),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'ML Kit V2专业版',
                        style: TextStyles.modernTitle.copyWith(fontSize: 24),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      const SizedBox(height: 6),
                      Text(
                        'Google AI引擎 • 12种专业算法',
                        style: TextStyles.modernSubtitle.copyWith(fontSize: 14),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Flexible(child: _buildModernStatItem('高精度识别', 'AI智能引擎')),
                Flexible(child: _buildModernStatItem('本地识别', '完全离线处理')),
                Flexible(child: _buildModernStatItem('12种专业算法', '图像智能增强')),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String value, String label) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(value, style: TextStyles.subtitle),
        const SizedBox(height: 2),
        Text(label, style: TextStyles.caption),
      ],
    );
  }

  /// 🎨 现代化统计项 - 使用新拟态效果
  Widget _buildModernStatItem(String value, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: ModernUIHelper.modernCardDecoration(
        isNeumorphic: true,
        backgroundColor: ThemeColors.neumorphicBase,
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyles.modernButton.copyWith(
              color: ThemeColors.textOnDark,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyles.modernLabel.copyWith(
              color: ThemeColors.textOnDarkSecondary,
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateCard(String template) {
    final List<Map<String, dynamic>> advantages = template == '平板车'
        ? [
            {'icon': Icons.auto_awesome, 'text': 'ML Kit V2引擎'},
            {'icon': Icons.image_search, 'text': '12种专业算法'},
            {'icon': Icons.wb_sunny, 'text': '强光反光抑制'},
            {'icon': Icons.speed, 'text': '毫秒级响应'},
          ]
        : [
            {'icon': Icons.auto_awesome, 'text': 'ML Kit V2引擎'},
            {'icon': Icons.auto_fix_high, 'text': '12种专业算法'},
            {'icon': Icons.nightlight_round, 'text': 'AI弱光增强'},
            {'icon': Icons.security, 'text': '本地离线识别'},
          ];
    final String groupTitle =
        template == '平板车' ? '户外环境优化 | 强光适配' : '弱光环境增强 | 边缘优化';
    final Color groupColor =
        template == '平板车' ? const Color(0xFF1976D2) : const Color(0xFF388E3C);
    // 分两列，每列2个特性
    final leftColumn = advantages.sublist(0, 2);
    final rightColumn = advantages.sublist(2, 4);
    return GestureDetector(
      onTap: () => _showTaskTypeSheet(template),
      child: ThemedCard(
        type: CardType.glass,
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: _getTemplateGradient(template),
                    borderRadius:
                        BorderRadius.circular(ThemeColors.radiusMedium),
                  ),
                  child: Icon(
                    _getTemplateIcon(template),
                    size: 36,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        template,
                        style: TextStyles.cardTitle,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getTemplateDescription(template),
                        style: TextStyles.cardSubtitle,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ],
                  ),
                ),
                Icon(Icons.chevron_right,
                    color: ThemeColors.textOnDarkSecondary),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: groupColor,
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: Colors.white.withOpacity(0.3), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: groupColor.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                groupTitle,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: Colors.black26,
                      offset: const Offset(0.5, 0.5),
                      blurRadius: 1,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 12),
            // 优势描述区域，两列分布，字体白色，图标多样
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: leftColumn
                        .map((adv) => Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Row(
                                children: [
                                  Icon(adv['icon'] as IconData,
                                      color: Colors.white, size: 16),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      adv['text'] as String,
                                      style: TextStyles.description.copyWith(
                                        color: ThemeColors.textOnDark,
                                        fontSize: 13,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                    ),
                                  ),
                                ],
                              ),
                            ))
                        .toList(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: rightColumn
                        .map((adv) => Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Row(
                                children: [
                                  Icon(adv['icon'] as IconData,
                                      color: Colors.white, size: 16),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      adv['text'] as String,
                                      style: TextStyles.description.copyWith(
                                        color: ThemeColors.textOnDark,
                                        fontSize: 13,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                    ),
                                  ),
                                ],
                              ),
                            ))
                        .toList(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  LinearGradient _getTemplateGradient(String template) {
    switch (template) {
      case '平板车':
        return ThemeColors.blueGradient;
      case '集装箱':
        return ThemeColors.greenGradient;
      case '散装车':
        return ThemeColors.yellowGradient;
      case '吨袋':
        return ThemeColors.orangeGradient;
      default:
        return ThemeColors.primaryButtonGradient;
    }
  }

  IconData _getTemplateIcon(String template) {
    switch (template) {
      case '平板车':
        return Icons.local_shipping;
      case '集装箱':
        return Icons.inventory_2;
      case '散装车':
        return Icons.fire_truck;
      case '吨袋':
        return Icons.work_outline;
      default:
        return Icons.photo_camera;
    }
  }

  String _getTemplateDescription(String template) {
    switch (template) {
      case '平板车':
        return '户外强光环境 • 反光抑制优化';
      case '集装箱':
        return '弱光环境增强 • 边缘智能识别';
      case '散装车':
        return '适用于散装车装载识别';
      case '吨袋':
        return '适用于吨袋装载识别';
      default:
        return '通用拍照模板';
    }
  }

  void _showTemplateDetail(String template) {
    SimpleNavigationHelper.showManagedBottomSheet(
      context: context,
      isDismissible: true,
      builder: (context) => _buildTemplateDetailSheet(template),
    );
  }

  Widget _buildTemplateDetailSheet(String template) {
    final photos = TemplateConfig.getPhotoConfigs(template);

    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        gradient: ThemeColors.primaryGradient,
        borderRadius: const BorderRadius.vertical(
            top: Radius.circular(ThemeColors.radiusXLarge)),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // 顶部把手
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: ThemeColors.textOnGradient.withOpacity(0.5),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 内容区域
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题区域
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: _getTemplateGradient(template),
                            borderRadius:
                                BorderRadius.circular(ThemeColors.radiusMedium),
                          ),
                          child: Icon(
                            _getTemplateIcon(template),
                            color: ThemeColors.textOnGradient,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                template,
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.w800,
                                  color: ThemeColors.textOnGradient,
                                ),
                              ),
                              Text(
                                '${photos.length}张照片需求',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: ThemeColors.textOnGradient
                                      .withOpacity(0.8),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // 照片配置列表
                    Text(
                      '拍照要求',
                      style: TextStyles.subtitle,
                    ),

                    const SizedBox(height: 16),

                    Expanded(
                      child: ListView.builder(
                        itemCount: photos.length,
                        itemBuilder: (context, index) {
                          final photo = photos[index];
                          return _buildPhotoRequirementCard(photo, index);
                        },
                      ),
                    ),

                    const SizedBox(height: 16),

                    // 操作按钮
                    Row(
                      children: [
                        Expanded(
                          child: ThemedButton(
                            text: '选择其他模板',
                            onPressed: () => Navigator.of(context).pop(),
                            type: ButtonType.secondary,
                            isOutlined: true,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ThemedButton(
                            text: '开始拍照',
                            onPressed: () {
                              Navigator.pop(context);
                              _onTemplateSelected(template);
                            },
                            type: ButtonType.success,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoRequirementCard(PhotoConfig photo, int index) {
    return ThemedCard(
      type: CardType.glass,
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              gradient: photo.isRequired
                  ? ThemeColors.orangeGradient
                  : ThemeColors.blueGradient,
              borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
            ),
            child: Center(
              child: Text(
                '${index + 1}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w700,
                  color: ThemeColors.textOnGradient,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      photo.label,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: ThemeColors.textOnGradient,
                      ),
                    ),
                    if (photo.isRequired) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: ThemeColors.warning,
                          borderRadius:
                              BorderRadius.circular(ThemeColors.radiusSmall),
                        ),
                        child: Text(
                          '必拍',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: ThemeColors.textOnGradient,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                if (photo.description.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    photo.description,
                    style: TextStyle(
                      fontSize: 12,
                      color: ThemeColors.textOnGradient.withOpacity(0.8),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 创建任务并导航
  void _onTemplateSelected(String template) {
    // 传递选中的模板到增强任务页面，默认使用单批次类型
    context.go('/enhanced-task/new?type=single&template=$template');
  }

  Widget _buildTipCard() {
    return ThemedCard(
      type: CardType.glass,
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部标题行 - 保持原有设计
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: ThemeColors.blueGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: const Icon(
                  Icons.info_outline,
                  size: 36,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '温馨提示',
                      style: TextStyles.cardTitle,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '技术支持与服务保障',
                      style: TextStyles.cardSubtitle,
                    ),
                  ],
                ),
              ),
              Icon(Icons.lightbulb_outline,
                  color: ThemeColors.textOnDarkSecondary),
            ],
          ),
          const SizedBox(height: 16),

          // 技术标签
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            decoration: BoxDecoration(
              color: const Color(0xFFE8F5E9),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              '化工专业应用 | AI识别技术',
              style: TextStyles.caption.copyWith(color: Colors.black87),
            ),
          ),
          const SizedBox(height: 12),

          // 技术特性列表 - 简化版
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTechFeature(Icons.auto_awesome, 'ML Kit V2引擎'),
                    const SizedBox(height: 8),
                    _buildTechFeature(Icons.image_search, '12种专业算法'),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTechFeature(Icons.security, '本地离线识别'),
                    const SizedBox(height: 8),
                    _buildTechFeature(Icons.speed, '毫秒级响应'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 🔧 技术特性项 - 模仿模板卡片的优势项样式
  Widget _buildTechFeature(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          color: ThemeColors.textOnDark,
          size: 18,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyles.description,
          ),
        ),
      ],
    );
  }

  void _showTaskTypeSheet(String template) {
    SimpleNavigationHelper.showManagedBottomSheet(
      context: context,
      isDismissible: true,
      builder: (context) => _buildTaskTypeSheet(template),
    );
  }

  Widget _buildTaskTypeSheet(String template) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      padding: const EdgeInsets.fromLTRB(24, 16, 24, 32),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          Center(
            child: Text(
              '选择任务类型',
              style: TextStyles.dialogTitle,
            ),
          ),
          const SizedBox(height: 8),
          Center(
            child: Text(
              '选择适合您需求的任务类型',
              style: TextStyles.dialogContent,
            ),
          ),
          const SizedBox(height: 24),
          _buildTaskTypeOption(
            icon: Icons.inventory_2,
            title: '单批次任务',
            desc: '单一牌号、批号的标准识别任务',
            color: const Color(0xFFE8F5E9),
            onTap: () {
              Navigator.pop(context);
              _onTaskTypeSelected(template, 'single');
            },
          ),
          const SizedBox(height: 16),
          _buildTaskTypeOption(
            icon: Icons.local_shipping,
            title: '混装任务',
            desc: '多牌号、多批号的复合识别任务',
            color: const Color(0xFFE3F2FD),
            onTap: () {
              Navigator.pop(context);
              _onTaskTypeSelected(template, 'mixed');
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTaskTypeOption(
      {required IconData icon,
      required String title,
      required String desc,
      required Color color,
      required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(18),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            Icon(icon, size: 32, color: ThemeColors.textMedium),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title,
                      style: TextStyles.dialogTitle.copyWith(fontSize: 16)),
                  const SizedBox(height: 4),
                  Text(desc,
                      style: TextStyles.dialogContent.copyWith(fontSize: 13)),
                ],
              ),
            ),
            Icon(Icons.chevron_right, color: ThemeColors.textLight),
          ],
        ),
      ),
    );
  }

  void _onTaskTypeSelected(String template, String type) {
    // 跳转到增强任务页面，带上任务类型和模板参数
    context.go('/enhanced-task/new?type=$type&template=$template');
  }
}
