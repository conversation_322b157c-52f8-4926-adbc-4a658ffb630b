import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:loadguard/models/task_model.dart';
import '../../../utils/theme_colors.dart';

/// 📊 完成对话框组件
class CompletionDialog extends StatefulWidget {
  final TaskModel task;
  final Function(String) onComplete;
  final VoidCallback onShare;

  const CompletionDialog({
    Key? key,
    required this.task,
    required this.onComplete,
    required this.onShare,
  }) : super(key: key);

  @override
  State<CompletionDialog> createState() => _CompletionDialogState();
}

class _CompletionDialogState extends State<CompletionDialog> {
  late final TextEditingController _fileNameController;

  @override
  void initState() {
    super.initState();
    _fileNameController = TextEditingController(
      text:
          '${widget.task.template}-${DateFormat('yyyy-MM-dd-HH:mm:ss').format(DateTime.now())}',
    );
  }

  @override
  void dispose() {
    _fileNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final completedPhotos =
        widget.task.photos.where((p) => p.imagePath != null).length;
    // 🔧 修复：统计逻辑 - 按批次识别数量统计而非照片验证状态
    final totalRecognizedInBatches = widget.task.batches
        .fold(0, (sum, batch) => sum + batch.recognizedQuantity);
    final verifiedPhotos = totalRecognizedInBatches; // 使用批次识别总数
    final recognizedProducts = <String>{};

    // 统计识别到的产品
    for (final photo in widget.task.photos) {
      if (photo.recognitionResult?.extractedProductCode != null) {
        recognizedProducts.add(photo.recognitionResult!.extractedProductCode!);
      }
    }

    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      contentPadding: const EdgeInsets.all(24),
      backgroundColor: Theme.of(context).colorScheme.surface,
      elevation: 6,
      insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: ThemeColors.success.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.check_circle,
                    color: ThemeColors.success,
                    size: 32,
                  ),
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Text(
                    '识别完成',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                      color: ThemeColors.textDark,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 识别结果统计
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: ThemeColors.background,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: ThemeColors.border),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '识别结果统计：',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: ThemeColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildStatRow('已拍照片', '$completedPhotos 张'),
                  _buildStatRow('已识别', '$verifiedPhotos 张'),
                  _buildStatRow('识别产品', '${recognizedProducts.length} 种'),
                  if (recognizedProducts.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    for (final product in recognizedProducts)
                      Padding(
                        padding: const EdgeInsets.only(left: 16, bottom: 4),
                        child: Text(
                          '• $product',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                  ],
                ],
              ),
            ),
            const SizedBox(height: 20),

            // 文件名输入
            TextField(
              controller: _fileNameController,
              decoration: InputDecoration(
                labelText: '报告文件名',
                hintText: '请输入文件名',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.grey, width: 1.5),
                ),
                prefixIcon: const Icon(Icons.description),
                floatingLabelBehavior: FloatingLabelBehavior.auto,
                filled: true,
                fillColor: Theme.of(context).colorScheme.surfaceVariant,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              ),
            ),
            const SizedBox(height: 24),

            // 按钮组
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      side: BorderSide(color: Colors.grey),
                      foregroundColor: Colors.grey,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('取消'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: FilledButton(
                    onPressed: () =>
                        widget.onComplete(_fileNameController.text),
                    style: FilledButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      backgroundColor: ThemeColors.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('完成'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$label:',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: ThemeColors.accent,
            ),
          ),
        ],
      ),
    );
  }
}
