import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/core/providers/app_providers.dart';
import 'package:loadguard/utils/theme_colors.dart';
import 'package:loadguard/utils/safe_file_operations.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/widgets/manual_confirmation_dialog.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';

/// 📸 照片网格组件
class TaskPhotoGrid extends ConsumerStatefulWidget {
  final TaskModel task;
  final Function(String) onTakePicture;
  final bool showStatusBadge;
  final void Function(PhotoItem)? onShowRecognitionDetail;
  final VoidCallback? onShowAllResults;
  final void Function(PhotoItem)? onRetryRecognition;
  final void Function(String)? onDeleteCustomPhoto;

  const TaskPhotoGrid({
    Key? key,
    required this.task,
    required this.onTakePicture,
    this.showStatusBadge = false,
    this.onShowRecognitionDetail,
    this.onShowAllResults,
    this.onRetryRecognition,
    this.onDeleteCustomPhoto,
  }) : super(key: key);

  @override
  ConsumerState<TaskPhotoGrid> createState() => _TaskPhotoGridState();
}

class _TaskPhotoGridState extends ConsumerState<TaskPhotoGrid> {
  final Map<String, bool> _errorShown = {};

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.0,
            ),
            itemCount: widget.task.photos.length,
            itemBuilder: (context, index) {
              final photo = widget.task.photos[index];
              return _buildPhotoCard(context, photo);
            },
          ),
        ),
        // 移除重复的浮动识别结果按钮 - 与顶部功能重复
      ],
    );
  }

  Widget _buildPhotoCard(BuildContext context, PhotoItem photo) {
    final hasImage = photo.imagePath != null;
    final isVerified = photo.isVerified;
    final needRecognition = photo.needRecognition;
    final recognitionFailed = photo.recognitionFailed;
    final isRecognitionCompleted = photo.isRecognitionCompleted;
    final recognitionStatus = photo.recognitionStatus;

    // 🔧 修复：使用新的状态判断逻辑
    final isRecognizing = hasImage &&
        needRecognition &&
        recognitionStatus == RecognitionStatus.processing;
    final isPending = hasImage &&
        needRecognition &&
        recognitionStatus == RecognitionStatus.pending;
    final isCompleted = hasImage &&
        needRecognition &&
        recognitionStatus == RecognitionStatus.completed;
    final isFailed = hasImage &&
        needRecognition &&
        recognitionStatus == RecognitionStatus.failed;
    final isCancelled = hasImage &&
        needRecognition &&
        recognitionStatus == RecognitionStatus.cancelled;

    return GestureDetector(
      onTap: () {
        if (hasImage) {
          // 如果已有照片，点击查看照片
          _showPhotoPreview(context, photo);
        } else {
          // 如果没有照片，点击拍照
          widget.onTakePicture(photo.id);
        }
      },
      onLongPress: () {
        if (hasImage) {
          // 长按显示操作菜单
          _showPhotoActionMenu(context, photo);
        }
      },
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(ThemeColors.cardRadius),
              boxShadow: ThemeColors.cardShadow,
              border: Border.all(
                color: _getBorderColor(
                    photo, hasImage, isVerified, recognitionFailed),
                width: 2,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  flex: 5,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius:
                          const BorderRadius.vertical(top: Radius.circular(10)),
                    ),
                    child: hasImage
                        ? ClipRRect(
                            borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(10)),
                            child: Stack(
                              children: [
                                // 图片
                                kIsWeb
                                    ? Image.network(
                                        photo.imagePath!,
                                        fit: BoxFit.cover,
                                        width: double.infinity,
                                        height: double.infinity,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          if (!_errorShown
                                              .containsKey(photo.label)) {
                                            _errorShown[photo.label] = true;
                                            Future.microtask(() {
                                              if (mounted) {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(
                                                  SnackBar(
                                                    content: Text(
                                                        '无法加载图片: ${photo.label}'),
                                                    duration: const Duration(
                                                        seconds: 2),
                                                  ),
                                                );
                                              }
                                            });
                                          }
                                          return Center(
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                const Icon(
                                                    Icons.image_not_supported,
                                                    size: 32,
                                                    color: Colors.red),
                                                const SizedBox(height: 4),
                                                Text(
                                                  '图片已保存',
                                                  style: TextStyle(
                                                      fontSize: 12,
                                                      color: Colors.grey[700]),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      )
                                    : _buildNativeImage(photo.imagePath!),

                                // 🔧 新增：识别状态覆盖层
                                if (needRecognition && hasImage)
                                  Positioned.fill(
                                    child: _buildRecognitionOverlay(photo),
                                  ),

                                // 查看图标提示
                                Positioned(
                                  bottom: 4,
                                  right: 4,
                                  child: Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.6),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: const Icon(
                                      Icons.zoom_in,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : const Icon(
                            Icons.camera_alt,
                            size: 40,
                            color: Colors.grey,
                          ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Wrap(
                        spacing: 4.0,
                        runSpacing: 4.0,
                        children: [
                          if (photo.isRequired)
                            _buildTag(
                                '必拍',
                                hasImage
                                    ? const Color(0xFF4CAF50)
                                    : const Color(0xFFFB8C00)),
                          if (needRecognition)
                            _buildTag('需识别', const Color(0xFF2196F3)),
                          if (photo.isMixedLoadMatch)
                            _buildTag('混装匹配', const Color(0xFF9C27B0)),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        photo.label,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      const SizedBox(height: 2),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              hasImage ? '点击查看' : '点击拍照',
                              style: TextStyle(
                                fontSize: 11,
                                color: hasImage ? Colors.blue : Colors.grey,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // 状态图标 - 已移除，使用识别状态覆盖层代替
          // 在照片卡片右上角或下方，若photo.manualVerified为true，显示绿色"人工确认"标签
          if (photo.manualVerified)
            Positioned(
              right: 4,
              top: 4,
              child: GestureDetector(
                onLongPress: () => _showManualVerifiedTooltip(context),
                child: Tooltip(
                  message: '本照片由人工确认通过',
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      '人工确认',
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTag(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 10,
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getBorderColor(
      PhotoItem photo, bool hasImage, bool isVerified, bool recognitionFailed) {
    if (recognitionFailed) return const Color(0xFFE53E3E);
    if (isVerified) return const Color(0xFF4CAF50);
    if (photo.isRequired) return hasImage ? Colors.green : Colors.orange;
    return Colors.grey.withOpacity(0.3);
  }

  /// 🔧 新增：构建识别状态覆盖层
  Widget _buildRecognitionOverlay(PhotoItem photo) {
    switch (photo.recognitionStatus) {
      case RecognitionStatus.processing:
        return Container(
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.7),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                strokeWidth: 3,
              ),
              const SizedBox(height: 8),
              const Text(
                '🤖 AI识别中...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );

      case RecognitionStatus.completed:
        if (photo.isVerified) {
          return Container(
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.3),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Center(
              child: Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 32,
              ),
            ),
          );
        } else {
          return Container(
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.3),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Center(
              child: Icon(
                Icons.warning,
                color: Colors.orange,
                size: 32,
              ),
            ),
          );
        }

      case RecognitionStatus.failed:
        return Container(
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.3),
            borderRadius: BorderRadius.circular(10),
          ),
          child: const Center(
            child: Icon(
              Icons.error,
              color: Colors.red,
              size: 32,
            ),
          ),
        );

      case RecognitionStatus.cancelled:
        return Container(
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.3),
            borderRadius: BorderRadius.circular(10),
          ),
          child: const Center(
            child: Icon(
              Icons.cancel,
              color: Colors.grey,
              size: 32,
            ),
          ),
        );

      case RecognitionStatus.pending:
      default:
        return const SizedBox.shrink();
    }
  }

  /// 🔧 新增：获取识别标签文本
  String _getRecognitionTagText(PhotoItem photo) {
    switch (photo.recognitionStatus) {
      case RecognitionStatus.pending:
        return '需识别';
      case RecognitionStatus.processing:
        return '识别中';
      case RecognitionStatus.completed:
        return photo.isVerified ? '识别通过' : '识别完成';
      case RecognitionStatus.failed:
        return '识别失败';
      case RecognitionStatus.cancelled:
        return '识别取消';
    }
  }

  /// 🔧 新增：获取识别标签颜色
  Color _getRecognitionTagColor(PhotoItem photo) {
    switch (photo.recognitionStatus) {
      case RecognitionStatus.pending:
        return const Color(0xFF1976D2);
      case RecognitionStatus.processing:
        return Colors.blue;
      case RecognitionStatus.completed:
        return photo.isVerified
            ? const Color(0xFF4CAF50)
            : const Color(0xFFFF9800);
      case RecognitionStatus.failed:
        return const Color(0xFFE53E3E);
      case RecognitionStatus.cancelled:
        return Colors.grey;
    }
  }

  /// 🔧 新增：获取状态文本
  String _getStatusText(PhotoItem photo) {
    final hasImage = photo.imagePath != null;
    final needRecognition = photo.needRecognition;

    if (!hasImage) return '点击拍照';
    if (!needRecognition) return '已拍照';

    return photo.recognitionStatusText;
  }

  /// 🔧 新增：获取状态颜色
  Color _getStatusColor(PhotoItem photo) {
    return photo.recognitionStatusColor;
  }

  bool get _hasRecognitionResult =>
      widget.task.photos.any((p) => p.isVerified || p.recognitionFailed);

  // _buildStatusBadge 方法已移除，使用识别状态覆盖层代替

  void _showPhotoPreview(BuildContext context, PhotoItem photo) {
    final photos =
        widget.task.photos.where((p) => p.imagePath != null).toList();
    final initialIndex = photos.indexOf(photo);
    showDialog(
      context: context,
      builder: (ctx) {
        int currentIndex = initialIndex;
        return StatefulBuilder(
          builder: (context, setState) {
            final currentPhoto = photos[currentIndex];
            return Dialog(
              backgroundColor: Colors.black,
              insetPadding: const EdgeInsets.all(0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: GestureDetector(
                      onHorizontalDragEnd: (details) {
                        if (details.primaryVelocity != null) {
                          if (details.primaryVelocity! < 0 &&
                              currentIndex < photos.length - 1) {
                            setState(() => currentIndex++);
                          } else if (details.primaryVelocity! > 0 &&
                              currentIndex > 0) {
                            setState(() => currentIndex--);
                          }
                        }
                      },
                      child: Center(
                        child: currentPhoto.imagePath != null
                            ? (kIsWeb
                                ? Image.network(currentPhoto.imagePath!,
                                    fit: BoxFit.contain)
                                : Image.file(File(currentPhoto.imagePath!),
                                    fit: BoxFit.contain))
                            : const Icon(Icons.broken_image,
                                color: Colors.white, size: 80),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          onPressed: () {
                            Navigator.pop(context);
                            if (currentPhoto.isCustom &&
                                currentPhoto.label != '添加更多') {
                              // 调用外部删除方法
                              if (widget.onDeleteCustomPhoto != null) {
                                widget.onDeleteCustomPhoto!(currentPhoto.id);
                              }
                            } else {
                              widget.onTakePicture(
                                  currentPhoto.id); // 触发删除逻辑由外部处理
                            }
                          },
                          tooltip: '删除照片',
                        ),
                        IconButton(
                          icon: const Icon(Icons.camera_alt,
                              color: Colors.orange),
                          onPressed: () {
                            Navigator.pop(context);
                            widget.onTakePicture(currentPhoto.id); // 触发重拍
                          },
                          tooltip: '重拍',
                        ),
                        IconButton(
                          icon: const Icon(Icons.photo_library,
                              color: Colors.blue),
                          onPressed: () {
                            Navigator.pop(context);
                            widget.onTakePicture(currentPhoto.id);
                          },
                          tooltip: '本地上传',
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Text(
                      currentPhoto.label,
                      style: const TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _showPhotoActionMenu(BuildContext context, PhotoItem photo) {
    final actions = <Widget>[];
    if (photo.recognitionFailed || !photo.isVerified) {
      actions.add(ListTile(
        leading: const Icon(Icons.edit_note, color: Colors.green),
        title: const Text('人工确认'),
        subtitle: const Text('手动输入正确的识别结果'),
        onTap: () {
          Navigator.pop(context);
          _showManualConfirmation(photo);
        },
      ));
    }
    actions.add(ListTile(
      leading: const Icon(Icons.camera_alt, color: Colors.green),
      title: const Text('重新拍照'),
      subtitle: const Text('拍摄新照片替换当前照片'),
      onTap: () {
        Navigator.pop(context);
        widget.onTakePicture(photo.id);
      },
    ));
    actions.add(ListTile(
      leading: const Icon(Icons.photo_library, color: Colors.blue),
      title: const Text('本地上传'),
      subtitle: const Text('从相册选择照片替换'),
      onTap: () {
        Navigator.pop(context);
        widget.onTakePicture(photo.id);
      },
    ));
    if (photo.isCustom && photo.label != '添加更多') {
      actions.add(ListTile(
        leading: const Icon(Icons.delete, color: Colors.red),
        title: const Text('删除照片'),
        subtitle: const Text('移除当前自定义照片'),
        onTap: () {
          Navigator.pop(context);
          if (widget.onDeleteCustomPhoto != null) {
            widget.onDeleteCustomPhoto!(photo.id);
          }
        },
      ));
    }
    if (photo.manualVerified) {
      actions.add(ListTile(
        leading: const Icon(Icons.undo, color: Colors.orange),
        title: const Text('撤销人工确认'),
        onTap: () {
          setState(() {
            photo.manualVerified = false;
          });
          Navigator.of(context).pop();
        },
      ));
    }
    showModalBottomSheet(
      context: context,
      builder: (ctx) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '照片操作：${photo.label}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...actions,
            const SizedBox(height: 16),
            TextButton(
              onPressed: () => Navigator.pop(ctx),
              child: const Text('取消'),
            ),
          ],
        ),
      ),
    );
  }

  void _retryRecognition(PhotoItem photo) {
    // 重置识别状态
    photo.recognitionFailed = false;
    photo.isVerified = false;
    photo.ocrText = null;

    // 触发重新识别
    if (widget.onRetryRecognition != null) {
      widget.onRetryRecognition!(photo);
    }
  }

  /// 构建本地图片显示
  Widget _buildNativeImage(String imagePath) {
    try {
      final file = File(imagePath);

      // 检查文件是否存在
      if (!file.existsSync()) {
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.image_not_supported, size: 32, color: Colors.orange),
              const SizedBox(height: 4),
              Text('文件不存在',
                  style: TextStyle(fontSize: 12, color: Colors.orange)),
            ],
          ),
        );
      }

      return Image.file(
        file,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        errorBuilder: (context, error, stackTrace) {
          if (!_errorShown.containsKey(imagePath)) {
            _errorShown[imagePath] = true;
            Future.microtask(() {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('图片加载失败: ${error.toString()}'),
                    duration: const Duration(seconds: 3),
                  ),
                );
              }
            });
          }
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.broken_image, size: 32, color: Colors.red),
                const SizedBox(height: 4),
                Text('加载失败', style: TextStyle(fontSize: 12, color: Colors.red)),
              ],
            ),
          );
        },
      );
    } catch (e) {
      return const Center(
        child: Icon(Icons.error, size: 40, color: Colors.red),
      );
    }
  }

  void _showManualVerifiedTooltip(BuildContext context) {
    // Implementation of _showManualVerifiedTooltip method
  }

  /// 🔧 新增：显示人工确认对话框
  void _showManualConfirmation(PhotoItem photo) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ManualConfirmationDialog(
        photo: photo,
        presetProductCode: widget.task.batches.isNotEmpty
            ? widget.task.batches.first.productCode
            : null,
        presetBatchNumber: widget.task.batches.isNotEmpty
            ? widget.task.batches.first.batchNumber
            : null,
        onConfirm: (productCode, batchNumber, notes) {
          _handleManualConfirmation(photo, productCode, batchNumber, notes);
        },
        onCancel: () {
          // 取消操作，不做任何处理
        },
      ),
    );
  }

  /// 🔧 新增：处理人工确认结果
  void _handleManualConfirmation(
      PhotoItem photo, String productCode, String batchNumber, String? notes) {
    try {
      // 更新照片状态
      setState(() {
        photo.manualVerified = true;
        photo.isVerified = true;
        photo.recognitionStatus = RecognitionStatus.completed;
        photo.recognitionFailed = false;
        photo.matchedProductCode = productCode;
        photo.matchedBatchNumber = batchNumber;
        photo.recognitionEndTime = DateTime.now();

        // 创建人工确认的识别结果
        photo.recognitionResult = RecognitionResult(
          qrCode: null,
          ocrText: photo.recognitionResult?.ocrText ?? '人工确认',
          extractedProductCode: productCode,
          extractedBatchNumber: batchNumber,
          isQrOcrConsistent: false,
          matchesPreset: true, // 人工确认默认为匹配
          confidence: 100.0, // 人工确认置信度为100%
          recognitionTime: DateTime.now(),
          status: RecognitionStatus.completed,
          errorMessage: null,
          metadata: {
            'manualConfirmed': true,
            'confirmationTime': DateTime.now().toIso8601String(),
            'confirmationNotes': notes,
            'originalOcrText': photo.recognitionResult?.ocrText,
          },
        );

        // 更新识别元数据
        photo.recognitionMetadata = {
          ...?photo.recognitionMetadata,
          'manualConfirmation': {
            'confirmed': true,
            'productCode': productCode,
            'batchNumber': batchNumber,
            'notes': notes,
            'confirmationTime': DateTime.now().toIso8601String(),
            'operator': 'manual', // 可以后续扩展为具体操作员
          },
        };
      });

      // 更新任务的批次识别结果
      widget.task.updateBatchRecognition(
          productCode, batchNumber, '${photo.label}_manual_confirmed');

      // 保存任务状态
      final taskService = ref.read(taskServiceProvider);
      taskService.updateTask(widget.task);

      // 显示成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Text('人工确认成功：$productCode - $batchNumber'),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );

      AppLogger.info('人工确认完成: ${photo.label} -> $productCode - $batchNumber');
    } catch (e) {
      AppLogger.error('人工确认失败: $e');

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text('人工确认失败: $e')),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // 在识别详情弹窗、低置信度弹窗等所有相关位置，补充人工确认按钮
  Future<void> _showRecognitionResultDialog(PhotoItem photo) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('识别详情 - ${photo.label}'),
        content: Text(photo.recognitionResult?.ocrText ?? '无识别结果'),
        actions: [
          if (photo.recognitionFailed || !photo.isVerified)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _showManualConfirmation(photo);
              },
              child: const Text('人工确认'),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
