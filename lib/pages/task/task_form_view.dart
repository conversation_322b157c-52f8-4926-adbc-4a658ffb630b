import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/core/providers/app_providers.dart';
import 'package:go_router/go_router.dart';
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/services/workload_assignment_service.dart';
import 'package:loadguard/models/product_database.dart';
import 'package:loadguard/models/worker_info_data.dart';
import '../../utils/theme_colors.dart';
import '../../utils/simple_navigation_helper.dart';
import '../../utils/app_logger.dart';
import '../../widgets/product_selector_modal.dart';
import 'worker_selection_page.dart';

/// 📝 **简化版任务表单** - 恢复基本预设功能
class TaskFormView extends ConsumerStatefulWidget {
  final String? template;

  const TaskFormView({
    Key? key,
    this.template,
  }) : super(key: key);

  @override
  ConsumerState<TaskFormView> createState() => _TaskFormViewState();
}

class _TaskFormViewState extends ConsumerState<TaskFormView> {
  final _formKey = GlobalKey<FormState>();
  final _productCodeController = TextEditingController();
  final _batchNumberController = TextEditingController();
  final _quantityController = TextEditingController();
  final FocusNode _batchFocusNode = FocusNode();
  final FocusNode _quantityFocusNode = FocusNode();

  bool _isLoading = false;
  String? _selectedProductCode;
  String? _selectedCategory;

  // 人员选择相关
  final Set<String> _selectedWorkerIds = {};
  bool _showWorkerSelection = false;

  // 新的仓库选择相关状态
  final Set<String> _selectedWarehouses = {'1号库'}; // 默认选择1号库
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  // 🎯 **使用ProductDatabase获取产品列表**
  List<ProductInfo> get _popularProducts =>
      ProductDatabase.getPopularProducts(limit: 12);

  // 2. 类别映射
  final Map<String, List<String>> _categoryMap = {
    'LLDPE': ['LLDPE'],
    'HDPE': ['HDPE'],
    'mPE': ['mPE'],
    'PP': ['PP'],
    'SAN': ['SAN'],
    'PS': ['PS'],
  };

  @override
  void initState() {
    super.initState();
    _loadPresetData();
    _batchFocusNode.addListener(() {
      // 🔧 修复：移除clearComposing()调用，避免打断输入法连接
    });
    _quantityFocusNode.addListener(() {
      // 🔧 修复：移除clearComposing()调用，避免打断输入法连接
    });
    _batchNumberController.addListener(() {
    });
    _quantityController.addListener(() {
    });
  }

  @override
  void dispose() {
    _batchFocusNode.dispose();
    _quantityFocusNode.dispose();
    _searchController.dispose();
    _productCodeController.dispose();
    _batchNumberController.dispose();
    _quantityController.dispose();
    super.dispose();
  }

  /// 📋 **恢复预设数据加载**
  void _loadPresetData() {
    switch (widget.template) {
      case '平板车':
        _selectedProductCode = 'LLD-7042';
        _productCodeController.text = 'LLD-7042';
        _batchNumberController.text = '250615F10422';
        _quantityController.text = '20';
        break;
      case '集装箱':
        _selectedProductCode = 'HD-5000S';
        _productCodeController.text = 'HD-5000S';
        _batchNumberController.text = '250615F20251';
        _quantityController.text = '15';
        break;
      default:
        _selectedProductCode = 'LLD-7042';
        _productCodeController.text = 'LLD-7042';
        _batchNumberController.text = '250615F10422';
        _quantityController.text = '20';
    }
  }

  /// 🚀 **创建任务并开始拍照**
  void _createSingleTaskAndStartPhotos() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      setState(() => _isLoading = true);

      if (!mounted) return;

      final taskService = ref.read(taskServiceProvider);
      final quantity = int.parse(_quantityController.text.trim());

      // 🔧 修复：先保存工作量分配，再创建任务（确保任务创建时能正确加载工作量数据）
      if (_selectedWorkerIds.isNotEmpty) {
        try {
          final selectedWorkers = allWorkers
              .where((worker) => _selectedWorkerIds.contains(worker.id))
              .toList();

          final forklifts =
              selectedWorkers.where((w) => w.role == '叉车').toList();
          final warehouses =
              selectedWorkers.where((w) => w.role == '仓管').toList();
          final others = selectedWorkers
              .where((w) => w.role != '叉车' && w.role != '仓管')
              .toList();

          await WorkloadAssignmentService.saveAssignment(
            palletCount: quantity,
            selectedForklifts: [...forklifts, ...others], // 将其他角色也加入叉车组
            selectedWarehouses: warehouses,
          );

        } catch (e) {
          // 不阻止任务创建流程，只记录错误
        }
      }

      // 创建任务（此时工作量分配已保存，任务创建时可以正确加载）
      final newTask = await taskService.createTask(
        template: widget.template ?? '平板车',
        productCode: _selectedProductCode ?? '',
        batchNumber: _batchNumberController.text.trim(),
        quantity: quantity,
        participants: _selectedWorkerIds.toList(),
      );

      // 🔧 修复：确保任务完全保存后再跳转
      await taskService.saveTask(newTask);

      // 🔧 修复：等待一小段时间确保数据同步
      await Future.delayed(const Duration(milliseconds: 100));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('任务创建成功，已分配给${_selectedWorkerIds.length}名工作人员'),
            backgroundColor: Colors.green,
          ),
        );
      }

      if (mounted) {
        // 🔧 修复：使用正确的路由跳转到任务详情页面
        // 使用go替换push，避免返回到表单页
        context.go('/task-detail/${newTask.id}?type=photos');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('创建任务失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    return SimpleNavigationHelper.buildStandardPage(
      onBackPressed: () {
        HapticFeedback.lightImpact();
        context.go('/template-selection');
      },
      enableSwipeBack: true,
      child: Stack(
        children: [
          Scaffold(
            appBar: AppBar(
              title: Text('${widget.template ?? '平板车'}任务'),
              backgroundColor: ThemeColors.primary,
              foregroundColor: Colors.white,
              elevation: 0,
              automaticallyImplyLeading: true,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  HapticFeedback.lightImpact();
                  context.go('/template-selection');
                },
              ),
            ),
            body: Container(
              height: screenHeight, // 全屏高度
              decoration: const BoxDecoration(
                gradient: ThemeColors.primaryGradient,
              ),
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Form(
                    key: _formKey,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // ML Kit V2专业版标识
                          _buildProfessionalHeader(),
                          const SizedBox(height: 32),
                          _buildProductCodeSelector(),
                          const SizedBox(height: 20),
                          _buildBatchNumberInput(),
                          const SizedBox(height: 20),
                          _buildQuantityInput(),
                          const SizedBox(height: 20),
                          _buildWorkerSelection(),
                          const SizedBox(height: 24),
                          _buildQuickSelectGrid(),
                          const SizedBox(height: 32),
                          _buildStartPhotoButton(),
                          const SizedBox(height: 16),
                          _buildMixedTaskButton(),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          if (_isLoading)
            const ModalBarrier(dismissible: false, color: Colors.black26),
          if (_isLoading) const Center(child: CircularProgressIndicator()),
        ],
      ),
    );
  }

  Future<bool> _onWillPop() async {
    // 🆕 自动保存表单草稿，无弹窗直接返回
    final hasChanges = _productCodeController.text.isNotEmpty ||
        _batchNumberController.text.isNotEmpty ||
        _quantityController.text.isNotEmpty;

    if (hasChanges && !_isLoading) {
      // 自动保存表单数据作为草稿
      await _autoSaveFormDraft();
    }

    // 直接返回，无弹窗确认
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    } else {
      context.go('/template-selection');
    }
    return true;
  }

  /// 🔄 自动保存表单草稿
  Future<void> _autoSaveFormDraft() async {
    try {
      // 这里可以实现将表单数据保存到本地存储
      // 例如使用SharedPreferences保存草稿
      Log.i('📝 表单草稿已自动保存', tag: 'FormDraft');
    } catch (e) {
      Log.e('❌ 表单草稿保存失败', error: e, tag: 'FormDraft');
    }
  }

  /// 🏆 **ML Kit V2专业版标识**
  Widget _buildProfessionalHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.verified, color: ThemeColors.primaryHover, size: 28),
              const SizedBox(width: 12),
              Text(
                'ML Kit V2专业版',
                style: TextStyles.cardTitle,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Google AI引擎 | 化工专业识别 | 智能批号验证',
            style: TextStyles.cardSubtitle,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 🎯 **恢复：预设牌号选择器**
  Widget _buildProductCodeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '选择预设牌号',
          style: TextStyles.subtitle.copyWith(fontSize: 16),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withOpacity(0.3)),
          ),
          child: InkWell(
            onTap: () {
              _showProductSelector(context);
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  Icon(Icons.category, color: Colors.white.withOpacity(0.8)),
                  const SizedBox(width: 16),
                  if (_selectedProductCode != null) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        color: _getProductCategory(_selectedProductCode!),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        _getProductCategoryName(_selectedProductCode!),
                        style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        _selectedProductCode!,
                        style: const TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                            fontWeight: FontWeight.w600),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    Icon(Icons.arrow_drop_down,
                        color: Colors.white
                            .withOpacity(0.8)), // 🔧 修复：使用兼容的opacity
                  ],
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 获取产品分类对应的颜色
  Color _getProductCategory(String productCode) {
    final product = ProductDatabase.getAllProducts().firstWhere(
      (p) => p.code == productCode,
      orElse: () => ProductInfo(
        code: productCode,
        name: '',
        category: 'LLDPE',
        commonBatchPrefixes: [],
        priority: 0,
      ),
    );
    return ProductDatabase.getCategoryColor(product.category);
  }

  // 获取产品分类名称
  String _getProductCategoryName(String productCode) {
    final product = ProductDatabase.getAllProducts().firstWhere(
      (p) => p.code == productCode,
      orElse: () => ProductInfo(
        code: productCode,
        name: '',
        category: 'LLDPE',
        commonBatchPrefixes: [],
        priority: 0,
      ),
    );
    return product.category;
  }

  // 显示产品选择器弹窗 - 优化版本：分组展示，更好的视觉层次
  void _showProductSelector(BuildContext context) {
    SimpleNavigationHelper.showManagedBottomSheet(
      context: context,
      isDismissible: true,
      builder: (context) {
        return ProductSelectorModal(
          selectedProductCode: _selectedProductCode,
          onProductSelected: (productCode) {
            setState(() {
              _selectedProductCode = productCode;
              _productCodeController.text = productCode;
            });
            _updateBatchSuggestion(productCode);
            // 添加选择反馈
            HapticFeedback.selectionClick();
          },
        );
      },
    );
  }

  /// 生成最近8天的批号前缀（yyMMdd）
  List<String> _generateRecentBatchPrefixes({int days = 8}) {
    final now = DateTime.now();
    return List.generate(days, (i) {
      final date = now.subtract(Duration(days: i));
      final y = date.year % 100;
      final m = date.month.toString().padLeft(2, '0');
      final d = date.day.toString().padLeft(2, '0');
      return '$y$m$d';
    });
  }

  /// 🧠 智能批号输入 - 使用动态日期前缀
  Widget _buildBatchNumberInput() {
    final prefixes = _generateRecentBatchPrefixes();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '批次号',
          style: TextStyles.subtitle.copyWith(fontSize: 16),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _batchNumberController,
          focusNode: _batchFocusNode,
          style: const TextStyle(color: Colors.white, fontSize: 16),
          decoration: InputDecoration(
            hintText: '输入批号 (如：250615F10422)',
            hintStyle: TextStyle(
                color: Colors.white.withOpacity(0.6)), // 🔧 修复：使用兼容的opacity
            filled: true,
            fillColor: Colors.white.withOpacity(0.15), // 🔧 修复：使用兼容的opacity
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: Colors.white.withOpacity(0.3)), // 🔧 修复：使用兼容的opacity
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: Colors.white.withOpacity(0.3)), // 🔧 修复：使用兼容的opacity
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.white, width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
          validator: (value) => value?.isEmpty ?? true ? '请输入批次号' : null,
          onChanged: (val) {
          },
        ),
        if (prefixes.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            '常用批号前缀：',
            style: TextStyle(
              color: Colors.white.withOpacity(0.8), // 🔧 修复：使用兼容的opacity
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          // 改为两行显示，不使用水平滚动
          Wrap(
            spacing: 8, // 水平间距
            runSpacing: 4, // 垂直间距
            children: prefixes.map((prefix) {
              return GestureDetector(
                onTap: () {
                  _batchNumberController.text = '${prefix}F10422';
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2), // 🔧 修复：使用兼容的opacity
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    prefix,
                    style: TextStyle(
                      color:
                          Colors.white.withOpacity(0.9), // 🔧 修复：使用兼容的opacity
                      fontSize: 12,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  /// 📦 **数量输入**
  Widget _buildQuantityInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '数量（托）',
          style: TextStyles.subtitle.copyWith(fontSize: 16),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _quantityController,
          focusNode: _quantityFocusNode,
          keyboardType: TextInputType.number,
          // 🔧 测试：暂时移除 inputFormatters，看是否解决问题
          // inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          style: const TextStyle(color: Colors.white, fontSize: 16),
          decoration: InputDecoration(
            hintText: '输入数量 (如：20)',
            hintStyle: TextStyle(
                color: Colors.white.withOpacity(0.6)), // 🔧 修复：使用兼容的opacity
            filled: true,
            fillColor: Colors.white.withOpacity(0.15), // 🔧 修复：使用兼容的opacity
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: Colors.white.withOpacity(0.3)), // 🔧 修复：使用兼容的opacity
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: Colors.white.withOpacity(0.3)), // 🔧 修复：使用兼容的opacity
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                  color: Colors.white.withOpacity(0.3)), // 🔧 修复：使用兼容的opacity
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
          onChanged: (value) {
            // 🔧 测试：检查是否有异步操作清空了输入
            // Future.delayed(Duration(milliseconds: 100), () {
            //   if (_quantityController.text != value) {
            //     print(
            //         '[LOG] 警告：输入被异步修改了！原值: $value, 当前值: ${_quantityController.text}');
            //   }
            // });
            // 🔧 测试：移除setState调用，避免重建干扰输入
            // setState(() {});
          },
          // 🔧 测试：暂时移除validator，看是否干扰输入
          // validator: (value) {
          //   if (value?.isEmpty ?? true) return '请输入数量';
          //   if (int.tryParse(value!) == null) return '请输入有效数字';
          //   return null;
          // },
        ),
        // 总吨数显示 - 紧跟在数量输入框下方，样式保持一致
        const SizedBox(height: 12),
        _buildTotalTonnageDisplay(),
      ],
    );
  }

  /// 📊 **总吨数显示** - 独立组件，样式与数量输入框保持一致
  Widget _buildTotalTonnageDisplay() {
    final quantity = int.tryParse(_quantityController.text) ?? 0;
    final totalTonnage = quantity * 1.5;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '总吨数',
          style: TextStyles.subtitle.copyWith(fontSize: 16),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15), // 🔧 修复：使用兼容的opacity
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
                color: Colors.white.withOpacity(0.3)), // 🔧 修复：使用兼容的opacity
          ),
          child: Text(
            quantity > 0 ? '${totalTonnage.toStringAsFixed(1)} 吨' : '0.0 吨',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  /// 📊 **恢复：常用牌号快速选择**
  Widget _buildQuickSelectGrid() {
    // 获取所有热门产品
    final topProducts = _getTopProducts();
    final hotCodes = _getHotCodes();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.local_fire_department,
                color: Colors.white, size: 18),
            const SizedBox(width: 6),
            Text('常用牌号快速选择', style: TextStyles.subtitle.copyWith(fontSize: 16)),
          ],
        ),
        const SizedBox(height: 12),
        // 将类别过滤器移到单独一行
        _buildCategoryFilter(),
        const SizedBox(height: 16),
        LayoutBuilder(
          builder: (context, constraints) {
            final double itemWidth =
                (constraints.maxWidth - (3 * 8)) / 4; // 4 columns with spacing
            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4, // 4列
                childAspectRatio:
                    math.max(2.0, itemWidth / 40), // 调整高宽比让文字有更多空间
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: 8, // 始终渲染8个格子
              itemBuilder: (context, index) {
                if (index < topProducts.length) {
                  final product = topProducts[index];
                  final isSelected = _selectedProductCode == product.code;
                  final isHot = hotCodes.contains(product.code);
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedProductCode = product.code;
                        _productCodeController.text = product.code;
                      });
                      _updateBatchSuggestion(product.code);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 4, vertical: 4),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Colors.white.withOpacity(0.3)
                            : Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: isSelected
                              ? Colors.white
                              : Colors.white.withOpacity(0.3),
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (isHot)
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text('🔥',
                                    style: TextStyle(fontSize: 12)),
                                const SizedBox(width: 4),
                                Flexible(
                                  child: Text(
                                    product.code,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: isSelected
                                          ? FontWeight.w700
                                          : FontWeight.w600,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            )
                          else
                            Flexible(
                              child: Text(
                                product.code,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: isSelected
                                      ? FontWeight.w700
                                      : FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                } else {
                  // 空白占位，保持网格一致性
                  return Container(
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.white.withOpacity(0.1)),
                    ),
                  );
                }
              },
            );
          },
        ),
      ],
    );
  }

  /// 🚀 **开始拍照按钮**
  Widget _buildStartPhotoButton() {
    return ElevatedButton(
      onPressed: _isLoading ? null : _createSingleTaskAndStartPhotos,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        foregroundColor: ThemeColors.primary,
        padding: const EdgeInsets.symmetric(vertical: 18),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 8,
      ),
      child: _isLoading
          ? const SizedBox(
              height: 24,
              width: 24,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.camera_alt, size: 24),
                const SizedBox(width: 12),
                const Text(
                  '开始拍照 专业版识别',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
    );
  }

  /// 🔄 **混装任务按钮**
  Widget _buildMixedTaskButton() {
    return OutlinedButton.icon(
      icon: const Icon(Icons.swap_horiz, size: 20),
      label: const Text('切换到混装任务'),
      style: OutlinedButton.styleFrom(
        foregroundColor: Colors.white,
        side: BorderSide(color: Colors.white.withOpacity(0.5)),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        padding: const EdgeInsets.symmetric(vertical: 14),
      ),
      onPressed: () {
        // 修复：切换到新的混装任务表单页面
        // 获取当前模板，如果为空则使用集装箱作为默认（常用模板）
        final currentTemplate = widget.template ?? '集装箱';
        context.go('/enhanced-task/new?type=mixed&template=$currentTemplate');
      },
    );
  }

  /// 🔄 **更新批号建议** - 使用ProductDatabase数据
  void _updateBatchSuggestion(String? productCode) {
    if (productCode != null) {
      final product = ProductDatabase.presetProducts[productCode];
      if (product != null && product.commonBatchPrefixes.isNotEmpty) {
        final prefix = product.commonBatchPrefixes.first;

        // 🚀 特殊处理：HDPE开工料使用H格式批号
        if (productCode.contains('开工料') || productCode.contains('HDPE')) {
          // 根据图片格式生成：250612H10079-4B
          _batchNumberController.text = '${prefix}H10079-4B';
        } else {
          // 其他产品使用标准F格式
          _batchNumberController.text = '${prefix}F10422';
        }
      }
    }
  }

  /// 🎨 **获取分类颜色** - 与ProductInfo.categoryColor保持一致
  Color _getCategoryColor(String category) {
    return ProductDatabase.getCategoryColor(category);
  }

  // 3. 类别按钮Widget
  Widget _buildCategoryFilter() {
    final categories = ['LLDPE', 'HDPE', 'mPE', 'PP', 'SAN', 'PS'];
    return SizedBox(
      height: 36,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final cat = categories[index];
          final selected = _selectedCategory == cat;
          final color = ProductDatabase.getCategoryColor(cat);
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: OutlinedButton(
              onPressed: () {
                setState(() {
                  _selectedCategory = selected ? null : cat;
                });
              },
              style: OutlinedButton.styleFrom(
                backgroundColor: selected
                    ? color.withOpacity(0.3)
                    : Colors.white.withOpacity(0.08),
                foregroundColor: Colors.white,
                side: BorderSide(
                    color: selected ? color : Colors.white.withOpacity(0.3)),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20)),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
              ),
              child: Text(cat,
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13)),
            ),
          );
        },
      ),
    );
  }

  // 4. 获取当前类别下的高频牌号
  List<ProductInfo> _getFilteredProducts() {
    final all = _popularProducts;
    if (_selectedCategory == null) return all;
    final allowedCats = _categoryMap[_selectedCategory!] ?? [];
    return all
        .where((p) => allowedCats.any((c) => p.category.contains(c)))
        .toList();
  }

  // 5. 高频排序并取前8
  List<ProductInfo> _getTopProducts() {
    final filtered = _getFilteredProducts();
    filtered.sort((a, b) => (b.priority).compareTo(a.priority));
    return filtered.take(8).toList();
  }

  // 6. 获取当前组火热前三名code
  Set<String> _getHotCodes() {
    final top = _getFilteredProducts();
    top.sort((a, b) => (b.priority).compareTo(a.priority));
    return top.take(3).map((e) => e.code).toSet();
  }

  /// 👥 **人员选择组件**
  Widget _buildWorkerSelection() {
    final quantity = int.tryParse(_quantityController.text) ?? 0;
    final totalTonnage = quantity * 1.5;
    final perPersonTonnage = _selectedWorkerIds.isNotEmpty
        ? totalTonnage / _selectedWorkerIds.length
        : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '参与人员',
          style: TextStyles.subtitle.copyWith(fontSize: 16),
        ),
        const SizedBox(height: 8),

        // 人员选择输入框 - 新设计
        _buildWorkerSelectionField(),
        const SizedBox(height: 12),

        // 人均分配信息 - 只在有选中人员且有数量时显示
        if (quantity > 0 && _selectedWorkerIds.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '人均分配',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                      Text(
                        '${perPersonTonnage.toStringAsFixed(1)} 吨',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: ThemeColors.primaryHover,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_selectedWorkerIds.length}人参与',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// 构建人员列表
  Widget _buildWorkerList() {
    final groupedWorkers = <String, Map<String, List<WorkerInfo>>>{};

    // 按库区和小组分组，排除无角色人员
    for (final worker in allWorkers) {
      if (worker.role == '无') continue;

      groupedWorkers.putIfAbsent(worker.warehouse, () => {});
      groupedWorkers[worker.warehouse]!.putIfAbsent(worker.group, () => []);
      groupedWorkers[worker.warehouse]![worker.group]!.add(worker);
    }

    return ListView(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      children: groupedWorkers.entries.map((warehouseEntry) {
        return ExpansionTile(
          title: Text(
            warehouseEntry.key,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          subtitle: Text(
            '${_getWarehouseWorkerCount(warehouseEntry.value)}人',
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 12,
            ),
          ),
          iconColor: Colors.white,
          collapsedIconColor: Colors.white,
          children: warehouseEntry.value.entries.map((groupEntry) {
            if (groupEntry.value.isEmpty) return const SizedBox();

            return ExpansionTile(
              title: Text(
                groupEntry.key.isEmpty ? '未分组' : groupEntry.key,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 13,
                ),
              ),
              subtitle: Text(
                '${groupEntry.value.length}人',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.6),
                  fontSize: 11,
                ),
              ),
              iconColor: Colors.white,
              collapsedIconColor: Colors.white,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Wrap(
                    spacing: 4,
                    runSpacing: 4,
                    children: groupEntry.value.map((worker) {
                      final isSelected = _selectedWorkerIds.contains(worker.id);
                      return GestureDetector(
                        onTap: () => _toggleWorkerSelection(worker.id),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? ThemeColors.primaryHover
                                : Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isSelected
                                  ? Colors.white
                                  : Colors.white.withOpacity(0.3),
                            ),
                          ),
                          child: Text(
                            worker.name,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 11,
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
                const SizedBox(height: 8),
              ],
            );
          }).toList(),
        );
      }).toList(),
    );
  }

  int _getWarehouseWorkerCount(Map<String, List<WorkerInfo>> groups) {
    return groups.values.fold(0, (sum, workers) => sum + workers.length);
  }

  void _toggleWorkerSelection(String workerId) {
    setState(() {
      if (_selectedWorkerIds.contains(workerId)) {
        _selectedWorkerIds.remove(workerId);
      } else {
        _selectedWorkerIds.add(workerId);
      }
    });
  }

  void _clearWorkerSelection() {
    setState(() {
      _selectedWorkerIds.clear();
    });
  }

  Future<void> _showTeamSelectionDialog() async {
    // 获取所有小组
    final teams = <String, List<WorkerInfo>>{};
    for (final worker in allWorkers) {
      if (worker.role == '无') continue;
      final teamKey = '${worker.warehouse} ${worker.group}';
      teams.putIfAbsent(teamKey, () => []).add(worker);
    }

    final selectedTeam = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: ThemeColors.primary,
        title: const Text(
          '选择小组',
          style: TextStyle(color: Colors.white),
        ),
        content: Container(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: teams.entries.map((entry) {
              return ListTile(
                title: Text(
                  entry.key,
                  style: const TextStyle(color: Colors.white),
                ),
                subtitle: Text(
                  '${entry.value.length}人',
                  style: TextStyle(color: Colors.white.withOpacity(0.7)),
                ),
                onTap: () => Navigator.of(context).pop(entry.key),
              );
            }).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              '取消',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );

    if (selectedTeam != null && teams.containsKey(selectedTeam)) {
      setState(() {
        // 添加该小组的所有人员
        for (final worker in teams[selectedTeam]!) {
          _selectedWorkerIds.add(worker.id);
        }
      });
    }
  }

  /// 👥 **人员选择输入框** - 新设计，类似您的建议
  Widget _buildWorkerSelectionField() {
    final selectedWorkers = _selectedWorkerIds.map((id) {
      return allWorkers.firstWhere(
        (w) => w.id == id,
        orElse: () => const WorkerInfo(
            id: '', name: 'Unknown', role: '', warehouse: '', group: ''),
      );
    }).toList();

    String displayText;
    if (_selectedWarehouses.isEmpty) {
      displayText = '请先选择库区';
    } else if (selectedWorkers.isEmpty) {
      displayText = '${_selectedWarehouses.join("、")} - 点击选择人员';
    } else {
      final warehouseText = _selectedWarehouses.join("、");
      final workerText = selectedWorkers.map((w) => w.name).join("、");
      displayText =
          '$warehouseText - 已选${selectedWorkers.length}人: $workerText';
    }

    return GestureDetector(
      onTap: _openWorkerSelectionPage,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            const Icon(Icons.people, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                displayText,
                style: TextStyle(
                  color:
                      selectedWorkers.isEmpty ? Colors.white70 : Colors.white,
                  fontSize: 16,
                  fontWeight: selectedWorkers.isEmpty
                      ? FontWeight.normal
                      : FontWeight.w500,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (selectedWorkers.isNotEmpty) ...[
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: ThemeColors.primaryHover,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${selectedWorkers.length}人',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                const Icon(Icons.arrow_forward_ios,
                    color: Colors.white70, size: 16),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 🚀 **打开全屏人员选择页面**
  void _openWorkerSelectionPage() async {
    final result = await Navigator.of(context).push<Map<String, dynamic>>(
      MaterialPageRoute(
        builder: (context) => WorkerSelectionPage(
          selectedWarehouses: _selectedWarehouses,
          selectedWorkerIds: _selectedWorkerIds,
          quantity: int.tryParse(_quantityController.text) ?? 0,
        ),
        fullscreenDialog: true,
      ),
    );

    if (result != null && mounted) {
      setState(() {
        _selectedWarehouses.clear();
        _selectedWarehouses.addAll(result['warehouses'] ?? <String>{});

        _selectedWorkerIds.clear();
        _selectedWorkerIds.addAll(result['workerIds'] ?? <String>{});
      });
    }
  }

  /// 🏭 **仓库选择器** - 默认显示1号库，支持下拉多选
  Widget _buildWarehouseSelector() {
    final allWarehouses = allWorkers.map((w) => w.warehouse).toSet().toList()
      ..sort();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () => _showWarehouseSelectionDialog(allWarehouses),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.15),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.warehouse, color: Colors.white, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _selectedWarehouses.isEmpty
                        ? const Text('请选择仓库',
                            style:
                                TextStyle(color: Colors.white70, fontSize: 16))
                        : Wrap(
                            spacing: 8,
                            runSpacing: 4,
                            children: _selectedWarehouses.map((warehouse) {
                              return Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 4),
                                decoration: BoxDecoration(
                                  color: ThemeColors.primaryHover,
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(warehouse,
                                        style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold)),
                                    const SizedBox(width: 4),
                                    GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          _selectedWarehouses.remove(warehouse);
                                        });
                                      },
                                      child: const Icon(Icons.close,
                                          color: Colors.white, size: 16),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                          ),
                  ),
                  const Icon(Icons.arrow_drop_down, color: Colors.white),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 🚀 **智能快捷选择区**
  Widget _buildQuickSelectionArea() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '⚡ 快速选择',
            style: TextStyle(
                color: Colors.white, fontSize: 14, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child:
                    _buildQuickButton('最近合作', Icons.history, _selectRecentTeam),
              ),
              const SizedBox(width: 8),
              Expanded(
                child:
                    _buildQuickButton('标准配置', Icons.stars, _selectStandardTeam),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickButton(
                    '清空选择', Icons.clear_all, _clearWorkerSelection),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 🔍 **搜索区域**
  Widget _buildSearchArea() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(12),
        ),
        child: TextField(
          controller: _searchController,
          style: const TextStyle(color: Colors.white, fontSize: 16),
          decoration: const InputDecoration(
            hintText: '搜索库区/小组/姓名...',
            hintStyle: TextStyle(color: Colors.white54),
            border: InputBorder.none,
            prefixIcon: Icon(Icons.search, color: Colors.white54),
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value.trim();
            });
          },
        ),
      ),
    );
  }

  /// 📋 **展开式人员列表** - 类似截图的样式，默认全展开
  Widget _buildExpandedWorkerList() {
    final filteredWorkers = _getFilteredWorkers();
    final groupedWorkers = _groupWorkersByTeam(filteredWorkers);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        children: groupedWorkers.entries.map((entry) {
          final group = entry.key;
          final workers = entry.value;
          final groupColor = _getGroupColor(group);

          return Column(
            children: [
              // 小组标题
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: groupColor,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        group.isEmpty ? '未分组' : group,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${workers.length}人',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              // 人员列表 - 全展开
              ...workers.map((worker) => _buildWorkerItem(worker, groupColor)),
              const SizedBox(height: 12),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// 👤 **人员项目** - 类似截图中的样式
  Widget _buildWorkerItem(WorkerInfo worker, Color groupColor) {
    final isSelected = _selectedWorkerIds.contains(worker.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _toggleWorkerSelection(worker.id),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected
                  ? groupColor.withOpacity(0.3)
                  : Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? groupColor : Colors.white.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: groupColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    worker.group.isEmpty ? '未分组' : worker.group,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    worker.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                // 选择状态圆圈
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected ? groupColor : Colors.transparent,
                    border: Border.all(
                      color: isSelected
                          ? groupColor
                          : Colors.white.withOpacity(0.5),
                      width: 2,
                    ),
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white, size: 14)
                      : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 辅助方法
  Widget _buildQuickButton(String text, IconData icon, VoidCallback onPressed) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16, color: Colors.white),
      label:
          Text(text, style: const TextStyle(color: Colors.white, fontSize: 12)),
      style: OutlinedButton.styleFrom(
        foregroundColor: Colors.white,
        side: BorderSide(color: Colors.white.withOpacity(0.3)),
        backgroundColor: Colors.white.withOpacity(0.1),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      ),
    );
  }

  List<WorkerInfo> _getFilteredWorkers() {
    var workers = allWorkers.where((w) => w.role != '无').toList();

    // 按选中仓库过滤
    if (_selectedWarehouses.isNotEmpty) {
      workers = workers
          .where((w) => _selectedWarehouses.contains(w.warehouse))
          .toList();
    }

    // 搜索过滤
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      workers = workers
          .where((w) =>
              w.name.toLowerCase().contains(query) ||
              w.warehouse.toLowerCase().contains(query) ||
              w.group.toLowerCase().contains(query) ||
              w.role.toLowerCase().contains(query))
          .toList();
    }

    return workers;
  }

  Map<String, List<WorkerInfo>> _groupWorkersByTeam(List<WorkerInfo> workers) {
    final groups = <String, List<WorkerInfo>>{};
    for (final worker in workers) {
      final groupKey = '${worker.group.isEmpty ? "未分组" : worker.group}';
      groups.putIfAbsent(groupKey, () => []);
      groups[groupKey]!.add(worker);
    }
    return groups;
  }

  Color _getGroupColor(String group) {
    final colors = [
      Colors.green,
      Colors.blue,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.red,
    ];
    return colors[group.hashCode.abs() % colors.length];
  }

  void _showWarehouseSelectionDialog(List<String> warehouses) async {
    final tempSelected = Set<String>.from(_selectedWarehouses);

    final result = await showDialog<Set<String>>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          backgroundColor: const Color(0xFF2A2A3E),
          title: const Text('选择仓库', style: TextStyle(color: Colors.white)),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView(
              shrinkWrap: true,
              children: warehouses.map((warehouse) {
                final isSelected = tempSelected.contains(warehouse);
                return CheckboxListTile(
                  title: Text(warehouse,
                      style: const TextStyle(color: Colors.white)),
                  value: isSelected,
                  onChanged: (value) {
                    setState(() {
                      if (value == true) {
                        tempSelected.add(warehouse);
                      } else {
                        tempSelected.remove(warehouse);
                      }
                    });
                  },
                  activeColor: ThemeColors.primaryHover,
                  checkColor: Colors.white,
                );
              }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消', style: TextStyle(color: Colors.white70)),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(tempSelected),
              child: const Text('确定', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );

    if (result != null) {
      setState(() {
        _selectedWarehouses.clear();
        _selectedWarehouses.addAll(result);
      });
    }
  }

  void _selectRecentTeam() {
    // TODO: 实现最近合作团队选择逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('最近合作功能待实现')),
    );
  }

  void _selectStandardTeam() {
    // 根据当前数量推荐标准人数
    final quantity = int.tryParse(_quantityController.text) ?? 0;
    if (quantity > 0) {
      final recommendedCount =
          (quantity / 10).ceil().clamp(2, 6); // 每10托推荐1人，最少2人最多6人
      final availableWorkers = _getFilteredWorkers();

      setState(() {
        _selectedWorkerIds.clear();
        // 优先选择叉车角色
        final forklifts = availableWorkers
            .where((w) => w.role.contains('叉车'))
            .take(recommendedCount ~/ 2)
            .toList();
        final others = availableWorkers
            .where((w) => !w.role.contains('叉车'))
            .take(recommendedCount - forklifts.length)
            .toList();

        for (final worker in [...forklifts, ...others]) {
          _selectedWorkerIds.add(worker.id);
        }
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('已选择 ${_selectedWorkerIds.length} 人标准配置')),
      );
    }
  }
}
