import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/core/providers/app_providers.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/template_config.dart';
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';
import 'package:loadguard/services/workload_assignment_service.dart';
import 'package:loadguard/models/worker_info_data.dart';
import 'package:loadguard/services/async_upload_service.dart';
import 'package:loadguard/services/high_performance_image_processor.dart';
import 'package:loadguard/services/integrated_processing_service.dart';
import 'package:loadguard/services/pdf_service.dart';
import 'package:loadguard/utils/theme_colors.dart';
import 'package:loadguard/utils/simple_navigation_helper.dart';
import 'package:loadguard/utils/safe_file_operations.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/widgets/themed_button.dart';
import 'package:loadguard/widgets/upload_progress_widget.dart';
import 'package:loadguard/services/confidence_evaluation_service.dart';
import 'package:loadguard/widgets/confidence_detail_widget.dart';
import 'package:loadguard/widgets/confidence_badge_widget.dart';
import 'package:loadguard/widgets/enhanced_confidence_dialog.dart';
import 'package:loadguard/widgets/manual_confirmation_dialog.dart';

import 'widgets/task_photo_grid.dart';
import 'widgets/completion_dialog.dart';
import 'dart:io';
import 'dart:async';
import 'package:loadguard/services/adaptive_processing_strategy.dart';
import 'package:loadguard/services/smart_image_quality_assessor.dart';
import 'package:loadguard/widgets/themed_card.dart';
import 'package:loadguard/pages/task/widgets/floating_complete_button.dart';

/// 📸 任务拍照视图 - 优化版
class TaskPhotoView extends ConsumerStatefulWidget {
  final String? taskId;
  final TaskModel? currentTask;
  final String? template;

  const TaskPhotoView({
    Key? key,
    this.taskId,
    this.currentTask,
    this.template,
  }) : super(key: key);

  @override
  ConsumerState<TaskPhotoView> createState() => _TaskPhotoViewState();
}

class _TaskPhotoViewState extends ConsumerState<TaskPhotoView> {
  late final MLKitTextRecognitionService _recognitionService;
  late final AsyncUploadService _uploadService;
  late final HighPerformanceImageProcessor _imageProcessor;
  late final IntegratedProcessingService _integratedService;

  TaskModel? _currentTask;
  final ImagePicker _picker = ImagePicker();

  // 🚀 UI更新优化机制
  Timer? _uiUpdateTimer;
  bool _pendingUIUpdate = false;
  static const Duration _uiUpdateDelay = Duration(milliseconds: 50);

  // 识别状态跟踪
  final Map<String, bool> _photoRecognizing = {};
  final Map<String, RecognitionResult?> _recognitionResults = {};

  bool get _canCompleteTask {
    if (_currentTask == null) return false;
    // 根据用户要求，所有照片都必须拍摄后才能完成
    return _currentTask!.photos
        .where((p) => p.isRequired)
        .every((p) => p.imagePath != null);
  }

  /// 🚀 批量UI更新机制 - 防止频繁setState
  void _scheduleUIUpdate() {
    if (_pendingUIUpdate || !mounted) return;
    
    _pendingUIUpdate = true;
    _uiUpdateTimer?.cancel();
    _uiUpdateTimer = Timer(_uiUpdateDelay, () {
      if (mounted) {
        _pendingUIUpdate = false;
        // 🔧 修复：真正触发UI更新
        setState(() {
          // 强制UI重新构建
        });
      }
    });
  }

  /// 🚀 立即UI更新（仅在关键时刻使用）
  void _updateUIImmediately() {
    _uiUpdateTimer?.cancel();
    _pendingUIUpdate = false;
    if (mounted) {
      // 🔧 修复：立即触发UI更新
      setState(() {
        // 强制UI重新构建
      });
    }
  }

  @override
  void initState() {
    super.initState();
    print('🔍 [TaskPhotoView] initState开始');
    print('🔍 [TaskPhotoView] widget.taskId: ${widget.taskId}');
    print('🔍 [TaskPhotoView] widget.currentTask: ${widget.currentTask?.id ?? "null"}');

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 如有 context 相关初始化操作，放到这里
    });
    _recognitionService = MLKitTextRecognitionService();
    _uploadService = AsyncUploadService();
    _imageProcessor = HighPerformanceImageProcessor();
    _integratedService = IntegratedProcessingService();

    _currentTask = widget.currentTask;
    _loadTaskIfNeeded();
    _initializePhotoConfiguration();
    _initializeServices();
    _checkCameraPermission();
  }

  /// 🚀 初始化核心服务
  Future<void> _initializeServices() async {
    await _recognitionService.initialize();
    await _uploadService.initialize();
    await _imageProcessor.initialize();
    await _integratedService.initialize();
  }

  /// ---------------------------------------------------
  /// 🚀 全新V4版照片处理流水线 (2024-06-12)
  /// 使用集成处理服务，智能优化处理策略
  /// ---------------------------------------------------

  /// 🥇 识别成功回调
  void _onRecognitionSuccess(String photoId, RecognitionResult result) async {
    if (!mounted) return;
    final taskService = ref.read(taskServiceProvider);
    final photoItem =
        taskService.currentTask?.photos.firstWhere((p) => p.id == photoId);
    if (photoItem != null) {
      setState(() {
        photoItem.recognitionStatus =
            RecognitionStatus.completed; // 🔧 识别成功，状态为完成
        photoItem.isVerified = result.matchesPreset;
        photoItem.recognitionFailed = false; // 🔧 修复：识别成功就不是失败
        photoItem.recognitionEndTime = DateTime.now(); // 🔧 记录结束时间
        photoItem.ocrText = result.ocrText;
        photoItem.matchedProductCode = result.extractedProductCode;
        photoItem.matchedBatchNumber = result.extractedBatchNumber;
        photoItem.lastRecognitionTime = DateTime.now();
        photoItem.recognitionResult = result; // 🔧 保存识别结果以显示置信度
        photoItem.recognitionErrorMessage = null; // 🔧 清除错误信息
        photoItem.recognitionMetadata = result.metadata; // 🔧 保存识别元数据
        // 用id写回结果
        taskService?.updatePhotoResult(_currentTask!.id, photoItem.id, result);
      });

      // 🔧 修复：不匹配预设时显示警告，但不是失败弹窗
      if (!result.matchesPreset) {
        _showRecognitionMismatchDialog(photoItem, '识别结果不匹配预设信息');
      }

      // 🎯 新增：刷新工作量统计
      try {
        await taskService.refreshWorkloadStatistics();
        AppLogger.info('✅ 工作量统计已刷新');
      } catch (e) {
        AppLogger.error('❌ 刷新工作量统计失败: $e');
      }
    }
  }

  /// 🥈 识别失败回调
  void _onRecognitionFailure(String photoId, String error) async {
    if (!mounted) return;
    final taskService = ref.read(taskServiceProvider);
    final photoItem =
        taskService.currentTask?.photos.firstWhere((p) => p.id == photoId);
    if (photoItem != null) {
      setState(() {
        photoItem.recognitionStatus = RecognitionStatus.failed; // 🔧 统一使用枚举状态
        photoItem.isVerified = false;
        photoItem.recognitionFailed = true; // 🔧 明确标记识别失败
        photoItem.recognitionEndTime = DateTime.now(); // 🔧 记录结束时间
        photoItem.recognitionErrorMessage = error; // 🔧 保存错误信息
        photoItem.lastRecognitionTime = DateTime.now();
      });

      // 🔧 显示识别失败的弹窗提示
      _showRecognitionFailureDialog(photoItem, error);

      // 🎯 新增：刷新工作量统计（即使失败也要更新统计）
      try {
        await taskService.refreshWorkloadStatistics();
        AppLogger.info('✅ 工作量统计已刷新（识别失败）');
      } catch (e) {
        AppLogger.error('❌ 刷新工作量统计失败: $e');
      }
    }
  }

  /// 🔧 显示识别不匹配对话框
  void _showRecognitionMismatchDialog(PhotoItem photo, String message) {
    // 获取预设信息
    final presetProductCode = _currentTask?.batches.isNotEmpty == true
        ? _currentTask!.batches.first.productCode
        : '';
    final presetBatchNumber = _currentTask?.batches.isNotEmpty == true
        ? _currentTask!.batches.first.batchNumber
        : '';

    // 评估置信度
    if (photo.recognitionResult != null) {
      final confidenceScore = ConfidenceEvaluationService.evaluateConfidence(
        result: photo.recognitionResult!,
        presetProductCode: presetProductCode,
        presetBatchNumber: presetBatchNumber,
      );

      // 使用新的精美置信度弹窗
      showDialog(
        context: context,
        builder: (context) => EnhancedConfidenceDialog(
          confidenceScore: confidenceScore,
          recognitionResult: photo.recognitionResult!,
          photoLabel: photo.label,
          onRetryRecognition: () => _retryRecognition(photo),
          onManualConfirm: () async {
            // 🔧 修复：使用人工确认对话框
            Navigator.of(context).pop(); // 先关闭当前对话框
            _showManualConfirmationDialog(photo);
          },
          onRetakePhoto: () => _handleAddPhoto(photo.id),
        ),
      );
    }
  }

  /// 🔧 显示识别失败对话框
  void _showRecognitionFailureDialog(PhotoItem photo, String error) {
    // 获取预设信息
    final presetProductCode = _currentTask?.batches.isNotEmpty == true
        ? _currentTask!.batches.first.productCode
        : '';
    final presetBatchNumber = _currentTask?.batches.isNotEmpty == true
        ? _currentTask!.batches.first.batchNumber
        : '';

    // 评估置信度
    ConfidenceScore? confidenceScore;
    if (photo.recognitionResult != null) {
      confidenceScore = ConfidenceEvaluationService.evaluateConfidence(
        result: photo.recognitionResult!,
        presetProductCode: presetProductCode,
        presetBatchNumber: presetBatchNumber,
      );
    }

    // 如果没有识别结果，创建一个默认的失败结果
    if (photo.recognitionResult == null) {
      photo.recognitionResult = RecognitionResult(
        ocrText: '',
        matchesPreset: false,
        isQrOcrConsistent: false,
        status: RecognitionStatus.failed,
      );
    }

    showDialog(
      context: context,
      builder: (context) => EnhancedConfidenceDialog(
        confidenceScore: confidenceScore ??
            ConfidenceScore(
              finalScore: 0.0,
              rawMlkitScore: 0.0,
              textQualityScore: 0.2,
              matchScore: 0.0,
              consistencyScore: 0.0,
              level: ConfidenceLevel.veryLow,
              recommendation: ConfidenceRecommendation.retry,
              technicalDetails: {},
            ),
        recognitionResult: photo.recognitionResult!,
        photoLabel: photo.label,
        onRetryRecognition: () => _retryRecognition(photo),
        onManualConfirm: () async {
          // 🔧 修复：人工确认需要指定具体批次
          if (_currentTask != null && _currentTask!.batches.isNotEmpty) {
            // 优先使用第一个有剩余空间的批次
            BatchInfo? targetBatch;
            for (final batch in _currentTask!.batches) {
              if (batch.recognizedQuantity < batch.plannedQuantity) {
                targetBatch = batch;
                break;
              }
            }

            if (targetBatch != null) {
              // 更新批次统计
              if (_updateBatchRecognitionStats(targetBatch, photo.label)) {
                setState(() {
                  photo.manualVerified = true;
                  photo.isVerified = true;
                  photo.recognitionFailed = false;
                  photo.matchedBatchIds = [targetBatch!.id];
                  photo.matchedProductCode = targetBatch.productCode;
                  photo.matchedBatchNumber = targetBatch.batchNumber;
                });

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Row(
                      children: [
                        const Icon(Icons.check_circle, color: Colors.white),
                        const SizedBox(width: 8),
                        Text(
                            '已手动确认为 ${targetBatch.productCode} ${targetBatch.batchNumber}'),
                      ],
                    ),
                    backgroundColor: Colors.green,
                    duration: const Duration(seconds: 3),
                  ),
                );
              }
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('⚠️ 所有批次已满，无法进行人工确认'),
                  backgroundColor: Colors.orange,
                ),
              );
            }
          }
        },
        onRetakePhoto: () => _handleAddPhoto(photo.id),
      ),
    );
  }

  /// 🔧 新增：显示人工确认对话框选择批次
  void _showManualConfirmationDialog(PhotoItem photo) {
    if (_currentTask == null || _currentTask!.batches.isEmpty) return;
    // 检查所有批次是否已满
    BatchInfo? availableBatch;
    try {
      availableBatch = _currentTask!.batches.firstWhere(
        (batch) => batch.recognizedQuantity < batch.plannedQuantity,
      );
    } catch (e) {
      availableBatch = null;
    }
    if (availableBatch == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('所有批次已满，无法人工确认！'), backgroundColor: Colors.red),
      );
      return;
    }
    // 原有人工确认弹窗逻辑
    showDialog(
      context: context,
      builder: (context) => ManualConfirmationDialog(
        photo: photo,
        onConfirm: (productCode, batchNumber, notes) async {
          final targetBatch = _currentTask!.batches.firstWhere(
            (batch) =>
                batch.productCode == productCode &&
                batch.batchNumber == batchNumber,
            orElse: () => _currentTask!.batches.first,
          );
          if (_updateBatchRecognitionStats(targetBatch, photo.label)) {
            setState(() {
              photo.manualVerified = true;
              photo.isVerified = true;
              photo.recognitionFailed = false;
              photo.matchedBatchIds = [targetBatch.id];
              photo.matchedProductCode = productCode;
              photo.matchedBatchNumber = batchNumber;
              if (notes != null && notes.isNotEmpty) {
                photo.manualConfirmationNotes = notes;
              }
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.white),
                    const SizedBox(width: 8),
                    Text('已人工确认为 $productCode $batchNumber'),
                  ],
                ),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
              ),
            );
          }
          Navigator.of(context).pop();
        },
        onCancel: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  /// 🔄 重新识别照片
  void _retryRecognition(PhotoItem photo) async {
    AppLogger.info('🔄 _retryRecognition called: photoId=${photo.id}');
    if (photo.imagePath == null) return;
    try {
      // 重置状态
      setState(() {
        photo.recognitionStatus = RecognitionStatus.processing; // 🔧 统一使用枚举状态
        photo.isVerified = false;
        photo.recognitionStartTime = DateTime.now(); // 🔧 记录开始时间
        photo.recognitionErrorMessage = null; // 🔧 清除错误信息
        photo.ocrText = null;
      });

      // 使用集成处理服务重新处理
      // 🔧 修复：混合任务支持多批次匹配
      String? presetProductCode;
      String? presetBatchNumber;

      if (_currentTask != null && _currentTask!.batches.isNotEmpty) {
        if (_currentTask!.batches.length == 1) {
          // 单批次任务：使用唯一批次信息
          presetProductCode = _currentTask!.batches.first.productCode;
          presetBatchNumber = _currentTask!.batches.first.batchNumber;
        } else {
          // 混合任务：传递null，让识别服务自行匹配所有批次
          presetProductCode = null;
          presetBatchNumber = null;
        }
      }

      final result = await _integratedService.processPhotoIntelligently(
        photoId: photo.id,
        imagePath: photo.imagePath!,
        taskId: _currentTask?.id ?? 'unknown',
        needRecognition: true,
        presetProductCode: presetProductCode,
        presetBatchNumber: presetBatchNumber,
        allBatches: _currentTask?.batches, // 🔧 传递所有批次信息
        onRecognitionSuccess: _onRecognitionSuccess,
        onRecognitionFailure: _onRecognitionFailure,
      );

      AppLogger.info('重新识别结果: ${result.summary}');

      // 更新任务状态
      if (!mounted) return;
      try {
        final taskService = ref.read(taskServiceProvider);
        AppLogger.info('🔄 _retryRecognition: updateTask called');
        taskService.updateTask(taskService.currentTask!);
      } catch (e) {
        AppLogger.error('🔄 _retryRecognition: 无法获取TaskService: $e');
        return;
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('正在重新识别 ${photo.id}...')),
        );
      }
    } catch (e) {
      AppLogger.error('🔄 _retryRecognition: 重新识别失败: $e');
      if (mounted) {
        setState(() {
          photo.recognitionStatus = RecognitionStatus.failed; // 🔧 统一使用枚举状态
          photo.recognitionErrorMessage = e.toString(); // 🔧 保存错误信息
          photo.recognitionEndTime = DateTime.now(); // 🔧 记录结束时间
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('重新识别失败: $e')),
        );
      }
    }
  }

  /// 📂 加载任务（如果需要）
  void _loadTaskIfNeeded() async {
    print('🔍 [TaskPhotoView] _loadTaskIfNeeded开始');
    print('🔍 [TaskPhotoView] widget.taskId: ${widget.taskId}');
    print('🔍 [TaskPhotoView] _currentTask: ${_currentTask?.id ?? "null"}');

    if (widget.taskId != null && _currentTask == null) {
      if (!mounted) return;

      // 🔧 修复：使用watch而不是read来监听TaskService状态变化
      final taskService = ref.read(taskServiceProvider);
      print('🔍 [TaskPhotoView] TaskService中的任务数量: ${taskService.tasks.length}');

      _currentTask = taskService.getTaskById(widget.taskId!);
      print('🔍 [TaskPhotoView] getTaskById结果: ${_currentTask?.id ?? "null"}');

      if (_currentTask != null) {
        await taskService.setCurrentTask(_currentTask!);
        print('✅ [TaskPhotoView] 任务加载成功: ${_currentTask!.template}');
        if (mounted) {
          // 🚀 使用批量UI更新
        _scheduleUIUpdate();
        }
      } else {
        print('❌ [TaskPhotoView] 未找到任务: ${widget.taskId}');
        // 🔧 修复：如果找不到任务，尝试延迟重试
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted && _currentTask == null) {
            _loadTaskIfNeeded();
          }
        });
      }
    } else {
      print('🔍 [TaskPhotoView] 跳过任务加载 - taskId: ${widget.taskId}, _currentTask: ${_currentTask?.id ?? "null"}');
    }
  }

  /// 📸 初始化照片配置
  void _initializePhotoConfiguration() {
    if (_currentTask == null) return;

    // 如果照片列表为空，根据模板配置初始化
    if (_currentTask!.photos.isEmpty) {
      final photoConfigs =
          TemplateConfig.getPhotoConfigs(_currentTask!.template);
      for (final config in photoConfigs) {
        AppLogger.info('🔧 初始化照片配置: ${config.label}, isCustom: ${config.isCustom}');
        _currentTask!.photos.add(PhotoItem(
          id: config.id,
          label: config.label,
          isRequired: config.isRequired,
          needRecognition: config.needRecognition,
          isCustom: config.isCustom,
        ));
      }
      // 添加第一个"添加更多"按钮
      _addCustomPhotoPlaceholder();
      // 🚀 使用批量UI更新
      _scheduleUIUpdate();
    }
  }

  void _addCustomPhotoPlaceholder() {
    // 检查末尾是否已有"添加更多"按钮
    if (_currentTask!.photos.isEmpty || !_currentTask!.photos.last.isCustom) {
      _currentTask!.photos.add(PhotoItem(
        id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
        label: '添加更多',
        isRequired: false,
        needRecognition: false,
        isCustom: true,
      ));
    }
  }

  /// 📷 拍照或相册选择 - 恢复原有弹窗逻辑
  Future<void> _handleAddPhoto(String photoId) async {
            final photoItem = _currentTask!.photos.firstWhere((p) => p.id == photoId,
        orElse: () => PhotoItem(id: photoId, label: '', isRequired: false, needRecognition: false));

    // 如果是"添加更多"按钮
    if (photoItem != null && photoItem.isCustom && photoItem.label == '添加更多') {
      // 新增一个自定义照片项
      final newId =
          'custom_${DateTime.now().millisecondsSinceEpoch}_${_currentTask!.photos.length}';
      final newPhoto = PhotoItem(
        id: newId,
        label:
            '自定义照片${_currentTask!.photos.where((p) => p.isCustom && p.label != '添加更多').length + 1}',
        isRequired: false,
        needRecognition: false,
        isCustom: true,
      );
      setState(() {
        // 在"添加更多"按钮前插入新项
        _currentTask!.photos.insert(_currentTask!.photos.length - 1, newPhoto);
      });

      // 🔧 修复：创建新照片项后立即调用拍照功能
      // 等待setState完成后再调用拍照
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _handleAddPhoto(newId);
      });
      return;
    }

    // 🔧 简化：显示选择弹窗 - 只保留普通拍照和相册选择
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          gradient: ThemeColors.primaryGradient,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.95),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.camera_alt,
                      color: ThemeColors.primary,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '选择拍照方式',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: ThemeColors.primary,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // 普通拍照
              Container(
                margin: const EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.green,
                      size: 24,
                    ),
                  ),
                  title: const Text(
                    '拍照',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  subtitle: const Text(
                    '使用相机拍照',
                    style: TextStyle(color: Colors.grey),
                  ),
                  trailing: const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.green,
                    size: 16,
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _takePicture(photoId);
                  },
                ),
              ),

              // 从相册选择
              Container(
                margin: const EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.photo_library,
                      color: Colors.orange,
                      size: 24,
                    ),
                  ),
                  title: const Text(
                    '从相册选择',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                  subtitle: const Text(
                    '选择已有照片',
                    style: TextStyle(color: Colors.grey),
                  ),
                  trailing: const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.orange,
                    size: 16,
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _pickImageFromGallery(photoId);
                  },
                ),
              ),

              const SizedBox(height: 20),
              // 取消按钮
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: () => Navigator.pop(context),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    backgroundColor: Colors.grey.withOpacity(0.1),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    '取消',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🤖 智能拍照 - 高级模式
  Future<void> _takeSmartPicture(String photoId) async {
    try {
      // 🔐 检查相机权限
      final hasPermission = await _checkCameraPermission();
      if (!hasPermission) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('需要相机权限才能拍照'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // 获取照片配置，判断是否需要文本识别
      final photoConfig = _getPhotoConfigById(photoId);
      final isTextRecognitionMode = photoConfig?.needRecognition == true;

      // 直接使用普通拍照功能
      await _takePicture(photoId);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('拍照失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 📷 拍照 - 简化同步版本，立即反馈
  Future<void> _takePicture(String photoId) async {
    try {
      // 🔐 检查相机权限
      final hasPermission = await _checkCameraPermission();
      if (!hasPermission) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('需要相机权限才能拍照'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
        preferredCameraDevice: CameraDevice.rear,
      );

      if (image != null) {
        // 立即更新UI显示照片
        // AppLogger.info('📸 拍照保存路径: $photoId -> ${image.path}');
        final taskService = ref.read(taskServiceProvider);

        // 检查当前任务
        if (taskService.currentTask == null) {
          return;
        }

        // 查找照片项 - 修复：使用configId而不是photoId来查找
        final photoItem = taskService.currentTask!.photos.firstWhere(
          (p) => p.id == photoId,
          orElse: () => throw Exception('找不到照片项: $photoId'),
        );

        AppLogger.info('📸 开始保存照片: ${photoItem.id} -> ${image.path}');
        AppLogger.info('📸 照片标签: ${photoItem.label}');
        AppLogger.info('📸 照片isCustom: ${photoItem.isCustom}');
        AppLogger.info('📸 照片needRecognition: ${photoItem.needRecognition}');
        AppLogger.info('📸 照片configId: ${photoItem.configId}');

        // 🔧 特殊处理："添加更多"按钮
        AppLogger.info('📸 检查条件: isCustom=${photoItem.isCustom}, label="${photoItem.label}", configId="${photoItem.configId}"');
        if (photoItem.label == '添加更多' && photoItem.configId == 'additional_custom') {
          AppLogger.info('🔧 检测到"添加更多"按钮，开始创建新的自定义照片项');

          // 检查自定义照片数量限制
          final customPhotosCount = taskService.currentTask!.photos
              .where((p) => p.isCustom && p.label != '添加更多')
              .length;

          if (customPhotosCount >= 10) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('⚠️ 自定义照片已达到上限（10张），无法继续添加'),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 3),
              ),
            );
            return;
          }

          // 创建新的自定义照片项
          final newId = 'custom_${DateTime.now().millisecondsSinceEpoch}_${taskService.currentTask!.photos.length}';
          final newPhoto = PhotoItem(
            id: newId,
            label: '自定义照片${customPhotosCount + 1}',
            isRequired: false,
            needRecognition: false,
            isCustom: true,
          );

          setState(() {
            // 🔧 修复：移除当前的"添加更多"按钮，添加新照片项，然后重新添加"添加更多"按钮
            taskService.currentTask!.photos.removeWhere((p) => p.id == photoId); // 移除当前的"添加更多"按钮
            taskService.currentTask!.photos.add(newPhoto); // 添加新的自定义照片项

            // 重新添加一个新的"添加更多"按钮
            taskService.currentTask!.photos.add(PhotoItem(
              id: 'add_more_${DateTime.now().millisecondsSinceEpoch}',
              label: '添加更多',
              configId: 'additional_custom',
              isRequired: false,
              needRecognition: false,
              isCustom: true,
            ));
          });

          AppLogger.info('🔧 新建自定义照片项: ${newPhoto.label}, ID: ${newPhoto.id}');

          // 保存照片到新创建的项中
          await taskService.updatePhoto(newPhoto.id, image.path);
          _updateUIImmediately();

          AppLogger.info('🔧 照片已保存到新创建的自定义照片项');
          return;
        }
        await taskService.updatePhoto(photoItem.id, image.path);
        // 🔧 修复：立即更新UI显示照片
        _updateUIImmediately(); // 立即更新UI显示照片

        AppLogger.info('📸 拍照已保存，路径: ${photoItem.imagePath}');
        AppLogger.info('📸 当前任务照片数量: ${taskService.currentTask?.photos.length}');
        AppLogger.info('📸 有路径的照片: ${taskService.currentTask?.photos.where((p) => p.imagePath != null && p.imagePath!.isNotEmpty).length}');
        
        // 详细检查每张照片的状态
        for (int i = 0; i < taskService.currentTask!.photos.length; i++) {
          final photo = taskService.currentTask!.photos[i];
          AppLogger.info('📸 照片${i + 1}: ${photo.label} - 有路径: ${photo.imagePath != null && photo.imagePath!.isNotEmpty}');
        }

        // 🚀 启动统一的快速识别流程（与本地上传一致）
        final photoConfig = _getPhotoConfigById(photoId);
        final needRecognition = photoConfig?.needRecognition == true;

        if (needRecognition) {
          // 立即设置识别状态，然后启动快速识别
          setState(() {
            photoItem.recognitionStatus = RecognitionStatus.processing;
            photoItem.recognitionStartTime = DateTime.now();
          });

          // 异步启动识别，不阻塞UI
          Future.microtask(
              () => _startFastRecognitionProcessing(photoItem.id, image.path));
        } else {
          // 存证照片直接完成
          AppLogger.info('存证照片直接完成');
          setState(() {
            photoItem.recognitionStatus =
                RecognitionStatus.completed; // 🔧 统一使用枚举状态
            photoItem.recognitionEndTime = DateTime.now(); // 🔧 记录结束时间
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '拍照失败: $e',
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 🤖 执行AI识别 (保留原方法供兼容)
  Future<void> _performRecognition(String photoId, String imagePath) async {
    if (!mounted) return;
    final photoItem = _currentTask?.photos.firstWhere((p) => p.configId == photoId);
    if (photoItem != null &&
        photoItem.matchedBatchIds != null &&
        photoItem.matchedBatchIds!.isNotEmpty) {
      for (final batchId in photoItem.matchedBatchIds!) {
        BatchInfo? batch;
        try {
          batch = _currentTask!.batches.firstWhere((b) => b.id == batchId);
        } catch (e) {
          batch = null;
        }
        if (batch != null &&
            batch.recognizedQuantity >= batch.plannedQuantity) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    '批次${batch.productCode} ${batch.batchNumber}已达最大数量${batch.plannedQuantity}，不能再识别！'),
                backgroundColor: Colors.red),
          );
          return;
        }
      }
    }
    // 原有识别逻辑
    // ... existing code ...
  }

  /// 🚨 显示识别异常弹窗
  void _showRecognitionAlert(
      RecognitionResult result, String photoLabel, PhotoItem photoItem) {
    final double confidence = result.confidence ?? 0.0;
    final bool lowConfidence = confidence < 0.6;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Text('识别异常'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('照片：$photoLabel'),
            if (result.extractedProductCode != null)
              Text('产品牌号：${result.extractedProductCode}'),
            if (result.extractedBatchNumber != null)
              Text('产品批号：${result.extractedBatchNumber}'),
            if (lowConfidence)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  '⚠️ 识别置信度低（${(confidence * 100).toStringAsFixed(1)}%），建议重拍或人工确认',
                  style: const TextStyle(
                      color: Colors.orange, fontWeight: FontWeight.bold),
                ),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _handleAddPhoto(photoItem.id); // 重新拍照
            },
            child: const Text('重新拍照'),
          ),
          if (lowConfidence)
            TextButton(
              onPressed: () {
                setState(() {
                  photoItem.manualVerified = true;
                });
                Navigator.of(context).pop();
              },
              child: const Text('人工确认'),
            ),
        ],
      ),
    );
  }

  /// 🧠 智能文本匹配
  String? _findBestMatch(String source, String? target) {
    if (target == null || target.isEmpty) return null;
    final cleanSource =
        source.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '').toLowerCase();
    final cleanTarget =
        target.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '').toLowerCase();
    if (cleanSource.contains(cleanTarget)) {
      return target;
    }
    return null;
  }

  /// 📊 显示识别结果对话框
  Future<void> _showRecognitionResult(RecognitionResult result,
      {String? photoId}) async {
    if (!mounted) return;

    // 获取照片名称
    String photoName = '未知照片';
    if (photoId != null) {
      final photoItem = _currentTask?.photos.firstWhere(
            (p) => p.id == photoId,
            orElse: () =>
                PhotoItem(id: photoId, label: '未知照片', isRequired: false, needRecognition: false),
          ) ??
          PhotoItem(id: photoId, label: '未知照片', isRequired: false, needRecognition: false);
      photoName = photoItem.label;
    }

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  result.matchesPreset ? Icons.check_circle : Icons.warning,
                  color: result.matchesPreset ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                const Text('🤖 AI识别结果'),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              '照片：$photoName',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 准确率显示
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue[50]!, Colors.blue[100]!],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.precision_manufacturing,
                        color: Colors.blue[700]),
                    const SizedBox(width: 8),
                    const Text('MLKit引擎 • 高精度识别',
                        style: TextStyle(fontSize: 12, color: Colors.black87)),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // 只显示有效的识别结果
              if (result.extractedProductCode != null &&
                  result.extractedProductCode!.isNotEmpty) ...[
                const Text('🎯 提取信息:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                _buildExtractedInfo('🏷️ 产品代码', result.extractedProductCode!),
              ],

              if (result.extractedBatchNumber != null &&
                  result.extractedBatchNumber!.isNotEmpty) ...[
                if (result.extractedProductCode == null ||
                    result.extractedProductCode!.isEmpty)
                  const Text('🎯 提取信息:',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                if (result.extractedProductCode == null ||
                    result.extractedProductCode!.isEmpty)
                  const SizedBox(height: 8),
                _buildExtractedInfo('📦 批次号', result.extractedBatchNumber!),
              ],

              // 只显示有效的OCR文本
              if (result.ocrText != null &&
                  _isValidOcrText(result.ocrText!)) ...[
                const SizedBox(height: 16),
                const Text('📝 识别内容:',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _filterOcrText(result.ocrText!),
                    style: const TextStyle(color: Colors.black87),
                  ),
                ),
              ],

              // 如果没有任何有效识别结果，显示提示
              if ((result.extractedProductCode == null ||
                      result.extractedProductCode!.isEmpty) &&
                  (result.extractedBatchNumber == null ||
                      result.extractedBatchNumber!.isEmpty) &&
                  (result.ocrText == null ||
                      !_isValidOcrText(result.ocrText!))) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.orange, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '未识别到有效的产品信息，建议重新拍照或调整角度',
                          style: TextStyle(color: Colors.orange, fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // 匹配状态
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: result.matchesPreset
                      ? Colors.green[50]
                      : Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: result.matchesPreset ? Colors.green : Colors.orange,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      result.matchesPreset ? Icons.check : Icons.info,
                      color:
                          result.matchesPreset ? Colors.green : Colors.orange,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        result.matchesPreset
                            ? '✅ 识别结果与预设信息匹配'
                            : '⚠️ 识别结果与预设信息不完全匹配',
                        style: TextStyle(
                          color: result.matchesPreset
                              ? Colors.green[700]
                              : Colors.orange[700],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  Widget _buildExtractedInfo(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(label, style: const TextStyle(color: Colors.grey)),
          ),
          Expanded(
            child: Text(value,
                style: const TextStyle(fontWeight: FontWeight.w600)),
          ),
        ],
      ),
    );
  }

  /// 📋 获取照片配置
  PhotoConfig? _getPhotoConfigById(String photoId) {
    if (_currentTask == null) return null;
    final configs = TemplateConfig.getPhotoConfigs(_currentTask!.template);

    // 🔧 核心修复：通过PhotoItem的configId匹配模板配置
    final photoItem = _currentTask!.photos.firstWhere(
      (p) => p.id == photoId,
      orElse: () => throw Exception('未找到照片项: $photoId'),
    );

    if (photoItem.configId != null) {
      try {
        return configs.firstWhere((config) => config.id == photoItem.configId);
      } catch (e) {
        AppLogger.error(
            '⚠️ 未找到照片配置: ${photoItem.configId}, 可用配置: ${configs.map((c) => c.id).join(", ")}');
        return null;
      }
    }

    // 如果configId为空，尝试通过label匹配（兼容性）
    try {
      return configs.firstWhere((config) => config.label == photoId);
    } catch (e) {
      AppLogger.error(
          '⚠️ 未找到照片配置: $photoId, 可用配置: ${configs.map((c) => c.id).join(", ")}');
      return null;
    }
  }

  /// 🚀 后台专业识别算法 - 不阻塞UI
  Future<void> _performBackgroundLevel5Recognition(
      String photoId, String imagePath) async {
    final photoItem = _currentTask?.photos.firstWhere((p) => p.configId == photoId);
    if (_currentTask == null || photoItem == null) return;

    // 显示后台识别提示
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              const Text('🏆 Professional识别中... (后台进行，可继续拍照)'),
            ],
          ),
          backgroundColor: const Color(0xFF2196F3),
          duration: const Duration(seconds: 3),
        ),
      );
    }

    try {
      final startTime = DateTime.now();

      // 🔧 优化：支持混装任务的多批次匹配
      final List<RecognitionResult> ocrResults =
          await _recognitionService.processImage(imagePath);
      final fullOcrText = ocrResults.map((r) => r.ocrText).join(' \n ');

      // 🔧 多批次匹配逻辑
      bool matchesAnyBatch = false;
      String? matchedProductCode;
      String? matchedBatchNumber;
      String? matchedBatchId;
      List<String> matchedBatchIds = [];

      if (_currentTask!.batches.isNotEmpty) {
        // 遍历所有批次，任意一个匹配即成功
        for (final batch in _currentTask!.batches) {
          final productMatch = _findBestMatch(fullOcrText, batch.productCode);
          final batchMatch = _findBestMatch(fullOcrText, batch.batchNumber);

          if (productMatch != null && batchMatch != null) {
            // 🔧 修复：检查是否允许添加到此批次（防止超装）
            if (_updateBatchRecognitionStats(batch, photoItem.label)) {
              matchesAnyBatch = true;
              matchedProductCode = productMatch;
              matchedBatchNumber = batchMatch;
              matchedBatchId = batch.id;
              matchedBatchIds.add(batch.id);
              break; // 找到第一个匹配的批次就停止
            } else {
              // 超装了，继续检查下一个批次
              Log.w('批次 ${batch.productCode} ${batch.batchNumber} 超装，尝试下一个批次',
                  tag: 'Recognition');
              continue;
            }
          }
        }
      }

      final recognitionResult = RecognitionResult(
        ocrText: fullOcrText,
        matchesPreset: matchesAnyBatch,
        isQrOcrConsistent: true,
        extractedProductCode: matchedProductCode,
        extractedBatchNumber: matchedBatchNumber,
        status: matchesAnyBatch
            ? RecognitionStatus.completed
            : RecognitionStatus.failed,
        metadata: {
          'matchedBatchId': matchedBatchId,
          'matchedBatchIds': matchedBatchIds,
          'totalBatches': _currentTask!.batches.length,
          'isMixedLoad': _currentTask!.batches.length > 1,
        },
      );

      // 保存识别结果
      _recognitionResults[photoId] = recognitionResult;

      // 🔧 更新照片的匹配批次信息
      setState(() {
        photoItem.matchedBatchIds = matchedBatchIds;
        photoItem.recognitionStatus = recognitionResult.status;
        photoItem.isVerified = recognitionResult.matchesPreset;
        photoItem.recognitionEndTime = DateTime.now();
        photoItem.ocrText = recognitionResult.ocrText;
        photoItem.matchedProductCode = recognitionResult.extractedProductCode;
        photoItem.matchedBatchNumber = recognitionResult.extractedBatchNumber;
        photoItem.recognitionMetadata = recognitionResult.metadata;
      });

      // 显示识别结果
      await _showRecognitionResult(recognitionResult, photoId: photoId);

      final processingTime =
          DateTime.now().difference(startTime).inMilliseconds;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(matchesAnyBatch
                ? '🎉 识别成功！匹配批次: ${matchedBatchIds.length}个'
                : '❌ 识别失败，未匹配任何预设批次'),
            backgroundColor: matchesAnyBatch ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          photoItem.recognitionStatus = RecognitionStatus.failed;
          photoItem.recognitionErrorMessage = e.toString();
          photoItem.recognitionEndTime = DateTime.now();
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('识别失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 🔧 修复：更新批次识别统计，增加超装检查
  bool _updateBatchRecognitionStats(BatchInfo batch, String photoLabel) {
    // 检查是否会超装（识别数量超过计划数量）
    if (batch.recognizedQuantity >= batch.plannedQuantity) {
      // 已达到计划数量，提示超装警告
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              '⚠️ 警告：批次 ${batch.productCode} ${batch.batchNumber} 已识别数量已达到计划数量${batch.plannedQuantity}托，不能再添加更多项目！'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
      return false; // 返回false表示不允许添加
    }

    // 更新批次的识别数量
    if (!batch.recognizedItems.contains(photoLabel)) {
      batch.recognizedItems.add(photoLabel);
      batch.recognizedQuantity = batch.recognizedItems.length;

      // 如果刚好达到计划数量，显示完成提示
      if (batch.recognizedQuantity == batch.plannedQuantity) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '✅ 批次 ${batch.productCode} ${batch.batchNumber} 已完成识别！(${batch.recognizedQuantity}/${batch.plannedQuantity})'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
    return true; // 返回true表示成功添加
  }

  /// 🏆 专业识别算法（阻塞版本，保留兼容性）
  Future<void> _performLevel5Recognition(
      String photoId, String imagePath) async {
    final photoItem = _currentTask?.photos.firstWhere((p) => p.configId == photoId);
    if (_currentTask == null || photoItem == null) return;

    try {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                CircularProgressIndicator(strokeWidth: 2),
                const SizedBox(width: 16),
                Text('🏆 Professional企业级识别中...'),
              ],
            ),
            duration: Duration(seconds: 15),
          ),
        );
      }

      final startTime = DateTime.now();

      // 🔧 优化：支持混装任务的多批次匹配
      final List<RecognitionResult> ocrResults =
          await _recognitionService.processImage(imagePath);
      final fullOcrText = ocrResults.map((r) => r.ocrText).join(' \n ');

      // 🔧 多批次匹配逻辑
      bool matchesAnyBatch = false;
      String? matchedProductCode;
      String? matchedBatchNumber;
      String? matchedBatchId;
      List<String> matchedBatchIds = [];

      if (_currentTask!.batches.isNotEmpty) {
        // 遍历所有批次，任意一个匹配即成功
        for (final batch in _currentTask!.batches) {
          final productMatch = _findBestMatch(fullOcrText, batch.productCode);
          final batchMatch = _findBestMatch(fullOcrText, batch.batchNumber);

          if (productMatch != null && batchMatch != null) {
            // 🔧 修复：检查是否允许添加到此批次（防止超装）
            if (_updateBatchRecognitionStats(batch, photoItem.label)) {
              matchesAnyBatch = true;
              matchedProductCode = productMatch;
              matchedBatchNumber = batchMatch;
              matchedBatchId = batch.id;
              matchedBatchIds.add(batch.id);
              break; // 找到第一个匹配的批次就停止
            } else {
              // 超装了，继续检查下一个批次
              Log.w('批次 ${batch.productCode} ${batch.batchNumber} 超装，尝试下一个批次',
                  tag: 'Recognition');
              continue;
            }
          }
        }
      }

      final result = RecognitionResult(
        ocrText: fullOcrText,
        matchesPreset: matchesAnyBatch,
        isQrOcrConsistent: true,
        extractedProductCode: matchedProductCode,
        extractedBatchNumber: matchedBatchNumber,
        status: matchesAnyBatch
            ? RecognitionStatus.completed
            : RecognitionStatus.failed,
        metadata: {
          'matchedBatchId': matchedBatchId,
          'matchedBatchIds': matchedBatchIds,
          'totalBatches': _currentTask!.batches.length,
          'isMixedLoad': _currentTask!.batches.length > 1,
        },
      );

      final duration = DateTime.now().difference(startTime);

      // 🔧 更新照片的匹配批次信息
      setState(() {
        photoItem.matchedBatchIds = matchedBatchIds;
        photoItem.recognitionStatus = result.status;
        photoItem.isVerified = result.matchesPreset;
        photoItem.recognitionEndTime = DateTime.now();
        photoItem.ocrText = result.ocrText;
        photoItem.matchedProductCode = result.extractedProductCode;
        photoItem.matchedBatchNumber = result.extractedBatchNumber;
        photoItem.recognitionMetadata = result.metadata;
      });

      // 更新识别结果
      final taskService = ref.read(taskServiceProvider);
      await taskService.updatePhotoResult(
          _currentTask!.id, photoItem.id, result);

      // 显示识别结果
      if (mounted) {
        _showLevel5RecognitionResult(result, duration);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          photoItem.recognitionStatus = RecognitionStatus.failed;
          photoItem.recognitionErrorMessage = e.toString();
          photoItem.recognitionEndTime = DateTime.now();
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Professional识别失败: $e')),
        );
      }
    }
  }

  /// 🎯 显示专业识别结果（使用新的EnhancedConfidenceDialog）
  void _showLevel5RecognitionResult(
      RecognitionResult result, Duration duration) {
    // 获取预设信息
    final presetProductCode = _currentTask?.batches.isNotEmpty == true
        ? _currentTask!.batches.first.productCode
        : '';
    final presetBatchNumber = _currentTask?.batches.isNotEmpty == true
        ? _currentTask!.batches.first.batchNumber
        : '';

    // 评估置信度
    final confidenceScore = ConfidenceEvaluationService.evaluateConfidence(
      result: result,
      presetProductCode: presetProductCode,
      presetBatchNumber: presetBatchNumber,
    );

    // 使用新的精美置信度弹窗
    showDialog(
      context: context,
      builder: (context) => EnhancedConfidenceDialog(
        confidenceScore: confidenceScore,
        recognitionResult: result,
        photoLabel: '专业识别结果',
        onRetryRecognition: () {
          Navigator.pop(context);
          // 可以在这里添加重试逻辑
        },
        onManualConfirm: () async {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 8),
                  Text('已标记为人工确认通过'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        },
        onRetakePhoto: () {
          Navigator.pop(context);
          // 可以在这里添加重新拍照逻辑
        },
      ),
    );
  }

  /// 📊 构建性能指标
  Widget _buildPerformanceMetric(String icon, String value, String label) {
    return Column(
      children: [
        Text(
          icon,
          style: const TextStyle(fontSize: 16),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w700,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  /// 🔍 构建详细结果行
  Widget _buildDetailedResultRow(
      String label, String value, String preset, bool isMatch) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            SizedBox(
              width: 80,
              child: Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2C3E50),
                ),
              ),
            ),
            Expanded(
              child: Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontFamily: 'monospace',
                  color: Color(0xFF2C3E50),
                ),
              ),
            ),
            if (preset.isNotEmpty) ...[
              Icon(
                isMatch ? Icons.check : Icons.info,
                color: isMatch ? Colors.green : Colors.blue,
                size: 16,
              ),
            ] else ...[
              Icon(
                value != '无' && value != '未识别' ? Icons.check : Icons.info,
                color:
                    value != '无' && value != '未识别' ? Colors.green : Colors.grey,
                size: 16,
              ),
            ],
          ],
        ),
        if (preset.isNotEmpty && value != preset) ...[
          Padding(
            padding: const EdgeInsets.only(left: 80, top: 2),
            child: Text(
              '预设: $preset',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// ❌ **已废弃**: 由于MLKit不提供真实置信度，改为基于匹配状态判断
  String _getMatchStatus(RecognitionResult result) {
    if (result.matchesPreset) {
      return "匹配成功";
    } else if (result.extractedProductCode != null ||
        result.extractedBatchNumber != null) {
      return "部分匹配";
    } else {
      return "未匹配";
    }
  }

  Widget _buildResultRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '$label:',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  /// 📊 显示完成对话框
  void _showCompletionDialog() {
    if (_currentTask == null) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => CompletionDialog(
        task: _currentTask!,
        onComplete: _completeTask,
        onShare: _shareResults,
      ),
    );
  }

  /// ✅ 完成任务
  Future<void> _completeTask(String fileName) async {
    if (_currentTask == null) return;

    try {
      // 生成PDF报告
      final pdfBytes =
          await PdfService().generateTaskReport([_currentTask!], '任务报告');

      if (!mounted) return;

      final taskService = ref.read(taskServiceProvider);

      // 🔧 修复：标记任务完成状态
      await taskService.completeTask();

      // 分享PDF报告
      await Share.shareXFiles(
        [
          XFile.fromData(pdfBytes,
              mimeType: 'application/pdf', name: '$fileName.pdf')
        ],
        subject: 'ML Kit V2专业版识别报告 - ${_currentTask!.template}',
        text: '''
ML Kit V2专业版识别报告

车辆类型：${_currentTask!.template}
任务时间：${DateFormat('yyyy-MM-dd HH:mm').format(_currentTask!.createTime)}
识别准确率：高准确率

本报告由装运卫士ML Kit V2专业版生成
        ''',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('完成任务失败: $e')),
        );
      }
    }
  }

  /// 📤 分享结果
  Future<void> _shareResults() async {
    if (_currentTask == null) return;

    try {
      final pdfBytes =
          await PdfService().generateTaskReport([_currentTask!], '任务报告');

      // await Share.shareXFiles(
      //   [XFile.fromData(pdfBytes, mimeType: 'application/pdf', name: 'task_report.pdf')],
      //   subject: 'ML Kit V2专业版识别报告',
      //   text: '装运卫士ML Kit V2专业版识别报告',
      // );
      // 暂时注释掉分享功能，等待安装share_plus依赖
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('分享失败: $e')),
        );
      }
    }
  }

  /// 🆕 简化的手势导航返回逻辑 - 自动保存并返回
  void _handleBackNavigation() async {
    // 自动保存当前进度
    await _saveProgress();

    // 直接返回主页，无弹窗确认
    if (mounted) {
      context.go('/home');
    }
  }

  @override
  Widget build(BuildContext context) {
    return _buildMainContent(context);
  }

  Widget _buildMainContent(BuildContext context) {
    print('🔍 [TaskPhotoView] _buildMainContent - _currentTask: ${_currentTask?.id ?? "null"}');
    print('🔍 [TaskPhotoView] widget.taskId: ${widget.taskId}');
    print('🔍 [TaskPhotoView] widget.currentTask: ${widget.currentTask?.id ?? "null"}');

    if (_currentTask == null) {
      print('❌ [TaskPhotoView] _currentTask为null，显示未找到任务信息');
      return Scaffold(
        appBar: AppBar(title: const Text('拍照任务')),
        body: const Center(child: Text('未找到任务信息')),
      );
    }

    return SimpleNavigationHelper.buildStandardPage(
      onBackPressed: _handleBackNavigation, // 🆕 手势导航返回
      enableSwipeBack: true,
      child: Scaffold(
        backgroundColor: Colors.grey[100],
        appBar: _buildAppBar(),
        body: Column(
          children: [
            _buildPhotoStatsBar(_currentTask!),
            Expanded(child: _buildPhotoGrid()),
            if (_isAllPhotosCompleted()) _buildCompletionActions(),
          ],
        ),
      ),
    );
  }

  ///  新增：完成操作区域
  Widget _buildCompletionActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 完成状态提示
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.green),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '任务拍照完成',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      Text(
                        '已完成 ${_currentTask!.photos.where((p) => p.isRequired && p.imagePath != null).length} 张必拍照片',
                        style:
                            const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // 操作按钮
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _showRecognitionResultList,
                  icon: const Icon(Icons.fact_check),
                  label: const Text('查看结果'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.blue,
                    side: const BorderSide(color: Colors.blue),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 2,
                child: ElevatedButton.icon(
                  onPressed: _saveAndShare,
                  icon: const Icon(Icons.save_alt),
                  label: const Text('保存并分享'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 保存进度
  Future<void> _saveProgress() async {
    try {
      if (!mounted) return;

      final taskService = ref.read(taskServiceProvider);

      await taskService.saveTaskDirectly(_currentTask!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.save, color: Colors.white),
                const SizedBox(width: 8),
                Text('进度已保存，可稍后继续'),
              ],
            ),
            backgroundColor: ThemeColors.success,
          ),
        );
      }
    } catch (e) {
      AppLogger.error('保存进度失败: $e');
    }
  }

  /// 📊 显示内存状态
  void _showMemoryStats() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🚀 性能状态'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('📱 内存使用: 正常'),
            Text(
                '🖼️ 缓存图片: ${_currentTask?.photos.where((p) => p.imagePath != null).length ?? 0}'),
            Text('📊 内存占用: 良好'),
            const Divider(),
            Text('🌐 活跃连接: 正常'),
            Text('📤 智能识别: 已就绪'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('识别系统运行正常')),
              );
            },
            child: const Text('清理内存'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 📊 构建顶部统计栏
  Widget _buildPhotoStatsBar(TaskModel task) {
    // 统计逻辑
    final int totalPhotos = task.photos.length;
    final int takenPhotos = task.photos.where((p) => p.imagePath != null).length;
    final int requiredPhotos = task.photos.where((p) => p.isRequired).length;
    final int completedRequired = task.photos.where((p) => p.isRequired && p.imagePath != null).length;
    final int verifiedPhotos = task.photos.where((p) => p.isVerified).length;
    final int aiRecognized = task.photos.where((p) => p.recognitionResult != null && p.recognitionResult!.matchesPreset).length;
    final int localSaved = task.photos.where((p) => p.imagePath != null).length;
    final int manualConfirmed = task.photos.where((p) => p.manualVerified).length;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 第一行：已拍照片、验证通过、必拍完成
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSimpleStatItem('$takenPhotos', '已拍照片', Colors.blue),
              _buildSimpleStatItem('$verifiedPhotos', '验证通过', Colors.green),
              _buildSimpleStatItem('$completedRequired/$requiredPhotos', '必拍完成', Colors.orange),
            ],
          ),
          const SizedBox(height: 16),
          // 第二行：AI识别、本地存证、人工确认
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSimpleStatItem('$aiRecognized', 'AI识别', Colors.purple),
              _buildSimpleStatItem('$localSaved', '本地存证', Colors.grey),
              _buildSimpleStatItem('$manualConfirmed', '人工确认', Colors.green),
            ],
          ),
        ],
      ),
    );
  }

  /// 📊 构建简单统计项
  Widget _buildSimpleStatItem(String value, String label, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    // 🚀 清理UI更新Timer
    _uiUpdateTimer?.cancel();
    
    // ML Kit V2专业版资源清理
    _recognitionService.dispose();
    _uploadService.dispose();
    _imageProcessor.dispose();
    AppLogger.info('🧹 任务照片视图资源已清理');
    super.dispose();
  }

  // 识别结果列表弹窗 - 采用现代化卡片设计
  void _showRecognitionResultList() {
    if (_currentTask == null) return;
    final recognizedPhotos = _currentTask!.photos
        .where((p) => p.isVerified || p.recognitionFailed == true)
        .toList();

    if (recognizedPhotos.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('暂无识别结果'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 380,
            maxHeight: MediaQuery.of(context).size.height * 0.85,
          ),
          decoration: BoxDecoration(
            // 🔧 采用图三的现代化渐变背景
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF1E293B), // 深蓝灰
                Color(0xFF334155), // 中蓝灰
                Color(0xFF475569), // 浅蓝灰
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
              BoxShadow(
                color: const Color(0xFF3B82F6).withOpacity(0.2),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF0F172A), // 深色
                      Color(0xFF1E293B), // 中深色
                    ],
                  ),
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(20)),
                  border: Border(
                    bottom: BorderSide(
                      color: const Color(0xFF475569).withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFF3B82F6).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: const Color(0xFF3B82F6).withOpacity(0.4),
                          width: 1,
                        ),
                      ),
                      child: const Icon(
                        Icons.fact_check,
                        color: Color(0xFF3B82F6),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        '识别结果汇总',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 内容区域
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      // 🔧 统计信息卡片 - 采用图三风格
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Color(0xFF1E293B), // 深蓝灰
                              Color(0xFF334155), // 中蓝灰
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: const Color(0xFF475569).withOpacity(0.3),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildStatItem(
                              '${_currentTask!.batches.fold(0, (sum, batch) => sum + batch.recognizedQuantity)}',
                              '识别成功',
                              Colors.green,
                            ),
                            Container(
                              width: 1,
                              height: 40,
                              color: const Color(0xFF475569).withOpacity(0.5),
                            ),
                            _buildStatItem(
                              '${recognizedPhotos.where((p) => p.recognitionFailed).length}',
                              '识别失败',
                              Colors.red,
                            ),
                            Container(
                              width: 1,
                              height: 40,
                              color: const Color(0xFF475569).withOpacity(0.5),
                            ),
                            _buildStatItem(
                              '${recognizedPhotos.length}',
                              '总计',
                              const Color(0xFF3B82F6),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // 识别结果列表
                      ...recognizedPhotos
                          .map((photo) => _buildRecognitionResultCard(photo))
                          .toList(),
                    ],
                  ),
                ),
              ),

              // 底部按钮
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF0F172A).withOpacity(0.8),
                  borderRadius:
                      const BorderRadius.vertical(bottom: Radius.circular(20)),
                  border: Border(
                    top: BorderSide(
                      color: const Color(0xFF475569).withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.white,
                          side: const BorderSide(color: Color(0xFF475569)),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text('关闭'),
                      ),
                    ),
                    if (_isAllPhotosCompleted()) ...[
                      const SizedBox(width: 12),
                      Expanded(
                        flex: 2,
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.of(context).pop();
                            _exportToPDF();
                          },
                          icon: const Icon(Icons.picture_as_pdf,
                              color: Colors.white),
                          label: const Text('导出PDF'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF3B82F6),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🔧 新增：统计项组件
  Widget _buildStatItem(String value, String label, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.white70,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 🔧 新增：识别结果卡片 - 采用表格形式整齐排列
  Widget _buildRecognitionResultCard(PhotoItem photo) {
    final isSuccess = photo.isVerified;
    final isFailed = photo.recognitionFailed;
    final color = isSuccess
        ? Colors.green
        : isFailed
            ? Colors.red
            : Colors.orange;
    final result = photo.recognitionResult;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF1E293B),
            const Color(0xFF334155),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showRecognitionDetail(photo),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 第一行：标题+状态
              Row(
                children: [
                  Expanded(
                    child: Text(
                      photo.label,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: color.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      isSuccess
                          ? '识别成功'
                          : isFailed
                              ? '识别失败'
                              : '未识别',
                      style: TextStyle(
                        color: color,
                        fontSize: 11,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              // 表格信息区
              _buildInfoTableRow(
                  '产品码', result?.extractedProductCode ?? '未识别', isSuccess),
              const SizedBox(height: 6),
              _buildInfoTableRow(
                  '批号', result?.extractedBatchNumber ?? '未识别', isSuccess),
              const SizedBox(height: 6),
              _buildInfoTableRow(
                  '识别时间',
                  photo.lastRecognitionTime != null
                      ? _formatDateTime(photo.lastRecognitionTime!)
                      : '无',
                  isSuccess),
              // 置信度显示
              if (photo.recognitionResult != null) ...[
                const SizedBox(height: 10),
                Builder(
                  builder: (context) {
                    final presetProductCode =
                        _currentTask?.batches.isNotEmpty == true
                            ? _currentTask!.batches.first.productCode
                            : '';
                    final presetBatchNumber =
                        _currentTask?.batches.isNotEmpty == true
                            ? _currentTask!.batches.first.batchNumber
                            : '';
                    final confidenceScore =
                        ConfidenceEvaluationService.evaluateConfidence(
                      result: photo.recognitionResult!,
                      presetProductCode: presetProductCode,
                      presetBatchNumber: presetBatchNumber,
                    );
                    return Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 3),
                      decoration: BoxDecoration(
                        color: Color(confidenceScore.level.colorValue)
                            .withOpacity(0.15),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: Color(confidenceScore.level.colorValue)
                              .withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            confidenceScore.level == ConfidenceLevel.high
                                ? Icons.check_circle
                                : Icons.info_outline,
                            color: Color(confidenceScore.level.colorValue),
                            size: 12,
                          ),
                          const SizedBox(width: 3),
                          Text(
                            confidenceScore.percentageString,
                            style: TextStyle(
                              color: Color(confidenceScore.level.colorValue),
                              fontSize: 11,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 🔧 新增：表格行构建
  Widget _buildInfoTableRow(String label, String value, bool isSuccess) {
    return Row(
      children: [
        SizedBox(
          width: 60,
          child: Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 13,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (label != '识别时间')
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: Icon(
              isSuccess && value != '未识别'
                  ? Icons.check_circle
                  : Icons.info_outline,
              color: isSuccess && value != '未识别' ? Colors.green : Colors.grey,
              size: 18,
            ),
          ),
      ],
    );
  }

  /// 🔧 新增：获取照片文件名
  String _getPhotoFileName(String imagePath) {
    final fileName = imagePath.split('/').last;
    // 如果文件名太长，只显示前面部分
    if (fileName.length > 15) {
      return '${fileName.substring(0, 12)}...';
    }
    return fileName;
  }

  /// 🔧 新增：格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 🔧 新增：检查是否所有照片都已完成
  bool _isAllPhotosCompleted() {
    if (_currentTask == null) return false;
    final requiredPhotos =
        _currentTask!.photos.where((p) => p.isRequired).toList();
    return requiredPhotos.every(
        (p) => p.imagePath != null && (p.isVerified || p.recognitionFailed));
  }

  /// 🔧 新增：导出PDF功能
  Future<void> _exportToPDF() async {
    // 先让用户输入文件名
    final fileName = await _showFileNameDialog();
    if (fileName == null || fileName.isEmpty) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  backgroundColor: Colors.black12,
                  strokeCap: StrokeCap.round,
                ),
              ),
              SizedBox(width: 12),
              Text('正在生成PDF报告...'),
            ],
          ),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(12))),
          elevation: 6,
        ),
      );

      // 生成PDF并返回完整路径
      final pdfPath = await _generateTaskPDF(fileName);

      if (pdfPath != null) {
        _showPDFExportSuccess(pdfPath, fileName);
      } else {
        throw Exception('PDF生成失败');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('PDF导出失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 🔧 新增：文件名输入对话框
  Future<String?> _showFileNameDialog() async {
    final controller = TextEditingController();
    // 默认文件名
    final defaultName =
        '任务报告_${_currentTask?.template ?? "未知"}_${DateTime.now().millisecondsSinceEpoch ~/ 1000}';
    controller.text = defaultName;

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2D3748), // 深色背景
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Row(
          children: [
            Icon(Icons.edit, color: Colors.white),
            SizedBox(width: 8),
            Text('设置文件名', style: TextStyle(color: Colors.white)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('请输入PDF文件名:', style: TextStyle(color: Colors.white)),
            const SizedBox(height: 12),
            TextField(
              controller: controller,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.blue),
                ),
                hintText: '输入文件名（不含扩展名）',
                hintStyle: TextStyle(color: Colors.grey[400]),
                prefixIcon: const Icon(Icons.description, color: Colors.white),
                filled: true,
                fillColor: Colors.white.withOpacity(0.1),
              ),
              maxLength: 50,
            ),
            const SizedBox(height: 8),
            const Text(
              '文件将保存到: /Documents/装运卫士/',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消', style: TextStyle(color: Colors.white)),
          ),
          ElevatedButton(
            onPressed: () {
              final fileName = controller.text.trim();
              if (fileName.isNotEmpty) {
                Navigator.pop(context, fileName);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('确定', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// 🔧 新增：生成任务PDF
  Future<String?> _generateTaskPDF(String fileName) async {
    try {
      if (_currentTask == null) return null;

      // 🔧 修复：直接使用当前任务数据，确保PDF中的信息是实时的
      AppLogger.info('=== PDF生成调试 ===');
      AppLogger.info('当前任务ID: ${_currentTask!.id}');
      AppLogger.info('当前任务照片数: ${_currentTask!.photos.length}');

      // 详细检查当前任务的照片状态
      for (int i = 0; i < _currentTask!.photos.length; i++) {
        final photo = _currentTask!.photos[i];
        if (photo.imagePath != null && photo.imagePath!.isNotEmpty) {
          AppLogger.info('PDF生成 - 照片${i + 1}: ${photo.label} - 路径: ${photo.imagePath} - 状态: ${photo.recognitionStatus}');
        }
      }

      // 🔧 关键修复：直接使用当前页面的任务数据，避免数据同步问题
      final pdfBytes =
          await PdfService().generateTaskReport([_currentTask!], '任务报告');

      // 获取应用文档目录
      final directory = await getApplicationDocumentsDirectory();
      final reportsDir = Directory('${directory.path}/装运卫士');

      // 创建目录（如果不存在）
      if (!await reportsDir.exists()) {
        await reportsDir.create(recursive: true);
      }

      // 创建PDF文件
      final file = File('${reportsDir.path}/$fileName.pdf');
      await file.writeAsBytes(pdfBytes);

      return file.path;
    } catch (e) {
      throw Exception('PDF生成失败: $e');
    }
  }

  /// 🔧 新增：显示PDF导出成功对话框
  void _showPDFExportSuccess(String pdfPath, String fileName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2D3748), // 深色背景
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('PDF导出成功', style: TextStyle(color: Colors.white)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('文件名: $fileName.pdf', style: TextStyle(color: Colors.white)),
            const SizedBox(height: 8),
            const Text('保存位置: /Documents/装运卫士/',
                style: TextStyle(color: Colors.white)),
            const SizedBox(height: 8),
            Text(
              '完整路径: $pdfPath',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info, color: Colors.blue, size: 16),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '您可以在文件管理器中找到此文件',
                      style: TextStyle(fontSize: 12, color: Colors.blue),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定', style: TextStyle(color: Colors.white)),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _sharePDF(pdfPath);
            },
            icon: const Icon(Icons.share, color: Colors.white),
            label: const Text('立即分享', style: TextStyle(color: Colors.white)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 🔧 新增：分享PDF
  Future<void> _sharePDF(String pdfPath) async {
    try {
      // 检查文件是否存在
      final file = File(pdfPath);
      if (!await file.exists()) {
        throw Exception('PDF文件不存在');
      }

      // 检查文件大小
      final fileSize = await file.length();
      if (fileSize == 0) {
        throw Exception('PDF文件为空');
      }

      // 调用系统分享功能
      await Share.shareXFiles(
        [XFile(pdfPath)],
        subject: 'ML Kit V2专业版识别报告',
        text: '''装运卫士ML Kit V2专业版识别报告

📊 任务类型: ${_currentTask?.template ?? '未知'}
📅 生成时间: ${DateTime.now().toString()}
📱 Generated by LoadGuard ML Kit V2 Professional''',
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.share, color: Colors.white),
                  const SizedBox(width: 8),
                  Text('分享失败'),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                '错误详情: $e',
                style: TextStyle(fontSize: 12),
              ),
            ],
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 4),
          action: SnackBarAction(
            label: '重试',
            textColor: Colors.white,
            onPressed: () => _sharePDF(pdfPath),
          ),
        ),
      );
    }
  }

  /// 单张识别详情弹窗（使用新的EnhancedConfidenceDialog）
  void _showRecognitionDetail(PhotoItem photo) {
    // 评估置信度
    ConfidenceScore? confidenceScore;
    RecognitionResult? correctedResult;

    if (photo.recognitionResult != null) {
      final presetProductCode = _currentTask?.batches.isNotEmpty == true
          ? _currentTask!.batches.first.productCode
          : '';
      final presetBatchNumber = _currentTask?.batches.isNotEmpty == true
          ? _currentTask!.batches.first.batchNumber
          : '';

      // 🔧 修复：使用PhotoItem中的匹配结果创建完整的RecognitionResult
      correctedResult = RecognitionResult(
        ocrText: photo.recognitionResult!.ocrText,
        extractedProductCode: photo.matchedProductCode, // 使用PhotoItem中的匹配结果
        extractedBatchNumber: photo.matchedBatchNumber, // 使用PhotoItem中的匹配结果
        isQrOcrConsistent: photo.recognitionResult!.isQrOcrConsistent,
        matchesPreset: photo.isVerified, // 使用PhotoItem中的验证状态
        confidence: photo.recognitionResult!.confidence,
        recognitionTime: photo.recognitionResult!.recognitionTime,
        status: photo.recognitionStatus,
        errorMessage: photo.recognitionErrorMessage,
        metadata: photo.recognitionMetadata,
      );

      confidenceScore = ConfidenceEvaluationService.evaluateConfidence(
        result: correctedResult,
        presetProductCode: presetProductCode,
        presetBatchNumber: presetBatchNumber,
      );
    }

    // 使用新的EnhancedConfidenceDialog
    if (confidenceScore != null && correctedResult != null) {
      showDialog(
        context: context,
        builder: (context) => EnhancedConfidenceDialog(
          confidenceScore: confidenceScore!,
          recognitionResult: correctedResult!,
          photoLabel: photo.label,
          onRetryRecognition: () => _retryRecognition(photo),
          onManualConfirm: () async {
            // 🔧 修复：使用人工确认对话框
            Navigator.of(context).pop(); // 先关闭当前对话框
            _showManualConfirmationDialog(photo);
          },
          onRetakePhoto: () => _handleAddPhoto(photo.id),
        ),
      );
    } else {
      // 如果没有识别结果，显示简单的照片预览
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: const Color(0xFF2D3748),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              const Icon(Icons.photo, color: Colors.grey),
              const SizedBox(width: 8),
              Expanded(
                  child: Text(photo.label,
                      style: const TextStyle(color: Colors.white))),
            ],
          ),
          content: Container(
            width: 400,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 照片预览
                  if (photo.imagePath != null)
                    Container(
                      width: double.infinity,
                      height: 200,
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[600]!),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: kIsWeb
                            ? Image.network(
                                photo.imagePath!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: Colors.grey[600],
                                    child: const Icon(Icons.error,
                                        size: 50, color: Colors.white),
                                  );
                                },
                              )
                            : SafeFileOperations.safeImageFile(
                                photo.imagePath,
                                fit: BoxFit.cover,
                              ),
                      ),
                    ),

                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey.withOpacity(0.3)),
                    ),
                    child: const Text(
                      '暂无识别结果',
                      style: TextStyle(color: Colors.white),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      );
    }
  }

  /// 构建详情行
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建对比行
  Widget _buildComparisonRow(String label, String detected, String preset) {
    final isMatch = detected == preset && preset.isNotEmpty;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label:',
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.grey,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(
              isMatch ? Icons.check_circle : Icons.info,
              color: isMatch ? Colors.green : Colors.orange,
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '识别到: $detected',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (preset.isNotEmpty) ...[
                    const SizedBox(height: 2),
                    Text(
                      '预设值: $preset',
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// AppBar 构建
  AppBar _buildAppBar() {
    return AppBar(
      title: Text(_currentTask?.template ?? '拍照任务'),
      backgroundColor: Colors.blue,
      foregroundColor: Colors.white,
      actions: [
        // 识别结果按钮
        IconButton(
          icon: const Icon(Icons.fact_check),
          tooltip: '查看识别结果',
          onPressed: _showRecognitionResultList,
        ),
        // 更多选项
        PopupMenuButton<String>(
          color: const Color(0xFF2D3748), // 深色背景
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          onSelected: (value) {
            switch (value) {
              case 'save_and_share':
                _saveAndShare();
                break;
              case 'settings':
                _showTaskSettings();
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'save_and_share',
              child: Row(
                children: [
                  Icon(Icons.save_alt, color: Colors.white),
                  SizedBox(width: 8),
                  Text('保存并分享', style: TextStyle(color: Colors.white)),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings, color: Colors.white),
                  SizedBox(width: 8),
                  Text('任务设置', style: TextStyle(color: Colors.white)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 🔧 重命名：分享任务结果
  Future<void> _shareTaskResults() async {
    try {
      // 先生成PDF
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white)),
              ),
              SizedBox(width: 12),
              Text('正在准备分享内容...'),
            ],
          ),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );

      final pdfPath = await _generateTaskPDF(
          '任务报告_${DateTime.now().millisecondsSinceEpoch}');

      if (pdfPath != null) {
        await _sharePDF(pdfPath);
      } else {
        throw Exception('无法生成分享内容');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('分享失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 构建进度条
  Widget _buildProgressBar() {
    if (_currentTask == null) return const SizedBox.shrink();

    final requiredPhotos =
        _currentTask!.photos.where((p) => p.isRequired).toList();
    final completedPhotos =
        requiredPhotos.where((p) => p.imagePath != null).length;
    final totalPhotos = requiredPhotos.length;
    final progress = totalPhotos > 0 ? completedPhotos / totalPhotos : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '拍照进度',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
              Text(
                '$completedPhotos/$totalPhotos',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              progress == 1.0 ? Colors.green : Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建照片网格
  Widget _buildPhotoGrid() {
    if (_currentTask == null) return const SizedBox.shrink();

    return TaskPhotoGrid(
      task: _currentTask!,
      onTakePicture: _handleAddPhoto,
      showStatusBadge: true,
      onShowRecognitionDetail: _showRecognitionResultDialog,
      onShowAllResults: _showRecognitionResultList,
      onRetryRecognition: _retryRecognition,
      onDeleteCustomPhoto: _deleteCustomPhoto,
    );
  }

  /// 构建浮动操作按钮
  Widget? _buildFloatingActionButton() {
    if (_currentTask == null || _isAllPhotosCompleted()) return null;

    return FloatingActionButton(
      onPressed: () => _handleAddPhoto(
          'custom_photo_${DateTime.now().millisecondsSinceEpoch}'),
      backgroundColor: Colors.blue,
      child: const Icon(Icons.add_a_photo, color: Colors.white),
      tooltip: '添加照片',
    );
  }

  /// 弹窗显示单个识别结果
  void _showRecognitionResultDialog(PhotoItem photo) {
    if (photo.recognitionResult == null && !photo.recognitionFailed) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('该照片无需识别或正在识别中。')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) {
        final result = photo.recognitionResult;
        final ocrText = result?.ocrText ?? '';
        final qrCode = result?.qrCode ?? '';

        String contentText;
        if (photo.recognitionFailed) {
          contentText = '识别失败，请尝试重新拍照。';
        } else if (ocrText.isNotEmpty) {
          contentText = 'OCR识别结果: $ocrText';
        } else if (qrCode.isNotEmpty) {
          contentText = '二维码/条码: $qrCode';
        } else {
          contentText = '未识别到有效信息。';
        }

        return AlertDialog(
          title: Row(
            children: [
              Icon(
                photo.isVerified ? Icons.check_circle : Icons.error,
                color: photo.isVerified ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 8),
              Text(photo.id),
            ],
          ),
          content: Text(contentText),
          actions: <Widget>[
            TextButton(
              child: const Text('关闭'),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        );
      },
    );
  }

  // 删除自定义照片时，若全部删除，依然保留"添加更多"按钮
  void _deleteCustomPhoto(String photoId) {
    setState(() {
      _currentTask!.photos.removeWhere((p) => p.id == photoId);
      // 保证末尾有"添加更多"按钮
      _addCustomPhotoPlaceholder();
    });
  }

  // 在识别结果弹窗、识别结果汇总、单张识别详情等处，若photoItem.manualVerified为true，显示"人工确认通过"绿色标识
  Widget _buildManualVerifiedBadge() {
    return Container(
      margin: const EdgeInsets.only(left: 8),
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.green,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        '人工确认通过',
        style: TextStyle(
            color: Colors.white, fontSize: 11, fontWeight: FontWeight.bold),
      ),
    );
  }

  /// 📷 从相册选择图片
  Future<void> _pickImageFromGallery(String photoId) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);

      if (image != null) {
        await _processSelectedImage(photoId, image.path);
      }
    } catch (e) {
      AppLogger.error('从相册选择图片失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('从相册选择图片失败'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 🔄 处理选中的照片
  Future<void> _processSelectedImage(String photoId, String imagePath) async {
    if (!mounted) return;

    AppLogger.info('=== 处理选中照片开始 ===');
    AppLogger.info('photoId: $photoId');
    AppLogger.info('imagePath: $imagePath');
    AppLogger.info('_currentTask: ${_currentTask?.template}');
    AppLogger.info('_currentTask照片数量: ${_currentTask?.photos.length}');

    final photoItem = _currentTask?.photos.firstWhere((p) => p.id == photoId);
    if (photoItem == null) {
      AppLogger.error('未找到照片项: $photoId');
      return;
    }

    AppLogger.info('找到照片项: ${photoItem.label}');
    AppLogger.info('照片项ID: ${photoItem.id}');
    AppLogger.info('照片项configId: ${photoItem.configId}');
    AppLogger.info('照片项isCustom: ${photoItem.isCustom}');

    // 🔧 特殊处理："添加更多"按钮
    if (photoItem.label == '添加更多' && photoItem.configId == 'additional_custom') {
      AppLogger.info('🔧 [相册] 检测到"添加更多"按钮，开始创建新的自定义照片项');

      // 检查自定义照片数量限制
      final customPhotosCount = _currentTask!.photos
          .where((p) => p.isCustom && p.label != '添加更多')
          .length;

      if (customPhotosCount >= 10) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('⚠️ 自定义照片已达到上限（10张），无法继续添加'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
        return;
      }

      // 创建新的自定义照片项
      final newId = 'custom_${DateTime.now().millisecondsSinceEpoch}_${_currentTask!.photos.length}';
      final newPhoto = PhotoItem(
        id: newId,
        label: '自定义照片${customPhotosCount + 1}',
        isRequired: false,
        needRecognition: false,
        isCustom: true,
      );

      setState(() {
        // 🔧 修复：移除当前的"添加更多"按钮，添加新照片项，然后重新添加"添加更多"按钮
        _currentTask!.photos.removeWhere((p) => p.id == photoId); // 移除当前的"添加更多"按钮
        _currentTask!.photos.add(newPhoto); // 添加新的自定义照片项

        // 重新添加一个新的"添加更多"按钮
        _currentTask!.photos.add(PhotoItem(
          id: 'add_more_${DateTime.now().millisecondsSinceEpoch}',
          label: '添加更多',
          configId: 'additional_custom',
          isRequired: false,
          needRecognition: false,
          isCustom: true,
        ));
      });

      AppLogger.info('🔧 [相册] 新建自定义照片项: ${newPhoto.label}, ID: ${newPhoto.id}');

      // 递归调用处理新创建的照片项
      await _processSelectedImage(newId, imagePath);
      return;
    }

    setState(() {
      photoItem.imagePath = imagePath;
      // 🔧 修复：所有照片初始状态都设为processing，等识别完成后再更新最终状态
      photoItem.recognitionStatus = RecognitionStatus.processing;
      photoItem.recognitionStartTime = DateTime.now(); // 🔧 记录开始时间
      photoItem.recognitionErrorMessage = null; // 🔧 清除错误信息
    });

    AppLogger.info('照片状态已更新: ${photoItem.recognitionStatus}');

    // 保存照片到任务
    if (!mounted) return;
    final taskService = ref.read(taskServiceProvider);

    AppLogger.info('开始调用taskService.updatePhoto');
    await taskService.updatePhoto(photoId, imagePath);
    AppLogger.info('taskService.updatePhoto完成');

    // 检查保存后的状态
    AppLogger.info('保存后检查:');
    AppLogger.info('当前任务照片数量: ${taskService.currentTask?.photos.length}');
    for (int i = 0; i < taskService.currentTask!.photos.length; i++) {
      final photo = taskService.currentTask!.photos[i];
      AppLogger.info('照片${i + 1}: ${photo.label} - 有路径: ${photo.imagePath != null && photo.imagePath!.isNotEmpty} - 状态: ${photo.recognitionStatus}');
    }

    // 如果需要识别，启动智能处理
    if (photoItem.needRecognition) {
      AppLogger.info('启动智能处理');
      await _startIntelligentProcessing(photoId, imagePath);
    } else {
      // 🔧 修复：存证照片直接设为完成状态，并立即保存
      AppLogger.info('存证照片直接完成');
      setState(() {
        photoItem.recognitionStatus = RecognitionStatus.completed;
        photoItem.recognitionEndTime = DateTime.now();
      });
      
      // 🔧 关键修复：立即更新TaskService中的状态
      await taskService.updateRecognitionResult(photoId, RecognitionResult(
        status: RecognitionStatus.completed,
        isQrOcrConsistent: true,     // 存证照片默认一致
        matchesPreset: true,         // 存证照片默认匹配
        confidence: 1.0,
        ocrText: '',
        errorMessage: null,
        metadata: {'type': 'evidence_photo'},
      ));
    }
    
    AppLogger.info('=== 处理选中照片完成 ===');
    
    // 🔧 最终检查：验证数据是否正确保存
    AppLogger.info('=== 最终数据验证 ===');
    final finalTaskService = ref.read(taskServiceProvider);
    AppLogger.info('TaskService中的任务数量: ${finalTaskService.tasks.length}');
    
    if (finalTaskService.currentTask != null) {
      AppLogger.info('当前任务: ${finalTaskService.currentTask!.template}');
      AppLogger.info('当前任务照片数量: ${finalTaskService.currentTask!.photos.length}');
      
      int savedPhotos = 0;
      for (int i = 0; i < finalTaskService.currentTask!.photos.length; i++) {
        final photo = finalTaskService.currentTask!.photos[i];
        if (photo.imagePath != null && photo.imagePath!.isNotEmpty) {
          savedPhotos++;
          AppLogger.info('✅ 已保存照片${savedPhotos}: ${photo.label} - 路径: ${photo.imagePath} - 状态: ${photo.recognitionStatus}');
        } else {
          AppLogger.info('❌ 未保存照片: ${photo.label} - 状态: ${photo.recognitionStatus}');
        }
      }
      AppLogger.info('总计已保存照片: $savedPhotos 张');
    }
    
    // 检查任务列表中的数据
    final taskInList = finalTaskService.tasks.where((t) => t.id == finalTaskService.currentTask?.id).firstOrNull;
    if (taskInList != null) {
      AppLogger.info('任务列表中的任务照片数量: ${taskInList.photos.length}');
      int listSavedPhotos = 0;
      for (int i = 0; i < taskInList.photos.length; i++) {
        final photo = taskInList.photos[i];
        if (photo.imagePath != null && photo.imagePath!.isNotEmpty) {
          listSavedPhotos++;
          AppLogger.info('✅ 任务列表中已保存照片${listSavedPhotos}: ${photo.label} - 路径: ${photo.imagePath} - 状态: ${photo.recognitionStatus}');
        }
      }
      AppLogger.info('任务列表中总计已保存照片: $listSavedPhotos 张');
    }
  }

  /// 🚀 启动快速识别处理（拍照和本地上传统一逻辑）
  Future<void> _startFastRecognitionProcessing(
      String photoId, String imagePath) async {
    try {
      final photoItem = _currentTask?.photos.firstWhere((p) => p.id == photoId);
      if (photoItem == null) return;

      // 清除之前的错误信息
      if (mounted) {
        setState(() {
          photoItem.recognitionErrorMessage = null;
        });
      }

      // 显示快速处理提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 8),
                const Text('🚀 快速识别处理中...'),
              ],
            ),
            backgroundColor: const Color(0xFF2196F3),
            duration: const Duration(seconds: 1),
          ),
        );
      }

      // 直接调用异步上传服务（与本地上传相同的流程）
      await _startIntelligentProcessing(photoId, imagePath);
    } catch (e) {
      AppLogger.error('启动快速识别失败: $e');
      if (mounted) {
        _onRecognitionFailure(photoId, '启动识别失败: $e');
      }
    }
  }

  /// 🚀 启动智能处理（保留原有方法，供本地上传使用）
  Future<void> _startIntelligentProcessing(
      String photoId, String imagePath) async {
    AppLogger.info('【调试】=== _startIntelligentProcessing 开始 ===');
    AppLogger.info('【调试】photoId: $photoId, imagePath: $imagePath');
    AppLogger.info('【调试】当前任务模板: ${_currentTask?.template}');
    AppLogger.info('【调试】当前任务批次数量: ${_currentTask?.batches.length ?? 0}');

    try {
      final photoItem = _currentTask?.photos.firstWhere((p) => p.id == photoId);
      if (photoItem == null) return;

      // 🔧 修复：直接使用集成处理服务，确保混合任务识别流程正确执行
      String? presetProductCode;
      String? presetBatchNumber;

      if (_currentTask!.batches.isNotEmpty) {
        if (_currentTask!.batches.length == 1) {
          // 单批次任务：使用唯一批次信息
          presetProductCode = _currentTask!.batches.first.productCode;
          presetBatchNumber = _currentTask!.batches.first.batchNumber;
          AppLogger.info(
              '【调试】单批次任务: 产品牌号=$presetProductCode, 批号=$presetBatchNumber');
        } else {
          // 混合任务：传递null，让识别服务自行匹配所有批次
          presetProductCode = null;
          presetBatchNumber = null;
          AppLogger.info('【调试】混合任务: 传递null参数，使用智能匹配');
          AppLogger.info(
              '【调试】混合任务批次: ${_currentTask!.batches.map((b) => '${b.productCode}-${b.batchNumber}').join(', ')}');
        }
      } else {
        AppLogger.info('【调试】❌ 任务批次为空');
      }

      AppLogger.info('【调试】调用AsyncUploadService.processPhoto...');
      // 使用统一的异步上传服务
      await _uploadService.processPhoto(
        photoId,
        imagePath,
        presetProductCode: presetProductCode,
        presetBatchNumber: presetBatchNumber,
        allBatches: _currentTask?.batches,
        onRecognitionSuccess: _onRecognitionSuccess,
        onRecognitionFailure: _onRecognitionFailure,
      );
      AppLogger.info('【调试】AsyncUploadService.processPhoto 调用完成');
    } catch (e, stackTrace) {
      AppLogger.error('【调试】_startIntelligentProcessing 异常: $e');
      AppLogger.error('【调试】堆栈: $stackTrace');
      // 更新照片状态为失败
      setState(() {
        final photoItem =
            _currentTask?.photos.firstWhere((p) => p.id == photoId);
        if (photoItem != null) {
          photoItem.recognitionStatus = RecognitionStatus.failed;
        }
      });
    }
  }

  /// 🔐 检查并请求相机权限
  Future<bool> _checkCameraPermission() async {
    try {
      // 检查当前权限状态
      final status = await Permission.camera.status;

      if (status.isGranted) {
        return true;
      }

      if (status.isDenied) {
        // 请求权限
        final result = await Permission.camera.request();
        return result.isGranted;
      }

      if (status.isPermanentlyDenied) {
        // 权限被永久拒绝，引导用户到设置页面
        _showPermissionDialog();
        return false;
      }

      return false;
    } catch (e) {
      AppLogger.error('权限检查失败: $e');
      return false;
    }
  }

  /// 显示权限对话框
  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('需要相机权限'),
        content: const Text('此应用需要相机权限来拍照识别。请在设置中手动开启相机权限。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
            child: const Text('去设置'),
          ),
        ],
      ),
    );
  }

  /// 🔧 任务设置功能
  void _showTaskSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2D3748), // 深色背景
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Row(
          children: [
            Icon(Icons.settings, color: Colors.white),
            SizedBox(width: 8),
            Text('任务设置', style: TextStyle(color: Colors.white)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('任务配置选项:',
                style: TextStyle(
                    fontWeight: FontWeight.bold, color: Colors.white)),
            const SizedBox(height: 12),
            ListTile(
              leading: const Icon(Icons.photo_camera, color: Colors.white),
              title: const Text('拍照质量', style: TextStyle(color: Colors.white)),
              subtitle:
                  const Text('高质量(85%)', style: TextStyle(color: Colors.grey)),
              trailing: const Icon(Icons.check, color: Colors.green),
              onTap: () {
                Navigator.pop(context);
                _showPhotoQualitySettings();
              },
            ),
            ListTile(
              leading: const Icon(Icons.text_fields, color: Colors.white),
              title: const Text('识别精度', style: TextStyle(color: Colors.white)),
              subtitle:
                  const Text('高精度模式', style: TextStyle(color: Colors.grey)),
              trailing: const Icon(Icons.check, color: Colors.green),
              onTap: () {
                Navigator.pop(context);
                _showRecognitionSettings();
              },
            ),
            ListTile(
              leading: const Icon(Icons.save, color: Colors.white),
              title: const Text('自动保存', style: TextStyle(color: Colors.white)),
              subtitle: const Text('已启用', style: TextStyle(color: Colors.grey)),
              trailing: const Icon(Icons.check, color: Colors.green),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('自动保存功能已启用')),
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// 拍照质量设置
  void _showPhotoQualitySettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2D3748), // 深色背景
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Text('拍照质量设置', style: TextStyle(color: Colors.white)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<int>(
              title: const Text('高质量 (85%)',
                  style: TextStyle(color: Colors.white)),
              subtitle: const Text('推荐设置，平衡质量与文件大小',
                  style: TextStyle(color: Colors.grey)),
              value: 85,
              groupValue: 85,
              activeColor: Colors.blue,
              onChanged: (value) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('拍照质量已设置为高质量')),
                );
              },
            ),
            RadioListTile<int>(
              title: const Text('超高质量 (95%)',
                  style: TextStyle(color: Colors.white)),
              subtitle:
                  const Text('最佳质量，文件较大', style: TextStyle(color: Colors.grey)),
              value: 95,
              groupValue: 85,
              activeColor: Colors.blue,
              onChanged: (value) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('拍照质量已设置为超高质量')),
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// 识别精度设置
  void _showRecognitionSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2D3748), // 深色背景
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Text('识别精度设置', style: TextStyle(color: Colors.white)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('当前使用 ML Kit V2 引擎',
                style: TextStyle(color: Colors.white)),
            const SizedBox(height: 12),
            ListTile(
              leading: const Icon(Icons.speed, color: Colors.blue),
              title: const Text('高精度模式', style: TextStyle(color: Colors.white)),
              subtitle: const Text('当前模式 - 最佳识别效果',
                  style: TextStyle(color: Colors.grey)),
              trailing: const Icon(Icons.check, color: Colors.green),
            ),
            const SizedBox(height: 8),
            const Text(
              '• 12种专业算法优化\n• Google AI引擎\n• 本地化处理',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// 🔧 新增：保存并分享功能（合并版）
  Future<void> _saveAndShare() async {
    // 先让用户输入文件名
    final fileName = await _showFileNameDialog();
    if (fileName == null || fileName.isEmpty) return;

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white)),
              ),
              SizedBox(width: 12),
              Text('正在保存并准备分享...'),
            ],
          ),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(12))),
          elevation: 6,
        ),
      );

      // 生成PDF并返回完整路径
      final pdfPath = await _generateTaskPDF(fileName);

      if (pdfPath != null) {
        // 验证文件是否存在
        final file = File(pdfPath);
        if (!await file.exists()) {
          throw Exception('PDF文件保存失败');
        }

        // 直接分享，不显示成功对话框
        await _sharePDF(pdfPath);

        // 🔧 修复：更新任务状态为已完成
        if (!mounted) return;

        try {
          final taskService = ref.read(taskServiceProvider);

          // 🔧 修复：在完成任务前，确保工作量数据已经保存
          if (_currentTask != null) {
            // 加载当前的工作量分配
            final assignment =
                await WorkloadAssignmentService.loadCurrentAssignment();
            if (assignment != null) {
              // 将工作量分配信息写入任务metadata
              _currentTask!.recognitionMetadata ??= {};
              _currentTask!.recognitionMetadata!['workload'] =
                  assignment.toMap();
              AppLogger.info('📋 工作量数据已保存到任务');
            } else {
              AppLogger.warning('⚠️ 没有找到工作量分配数据');
            }
          }

          // 标记任务为已完成
          await taskService.completeTask();
          AppLogger.info('🎆 任务已标记为完成，PDF已分享');
        } catch (e) {
          AppLogger.error('⚠️ 更新任务状态失败: $e');
        }

        // 显示简化的成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('已保存至 Documents/装运卫士/$fileName.pdf 并分享'),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 4),
            action: SnackBarAction(
              label: '查看',
              textColor: Colors.white,
              onPressed: () {
                // 显示文件路径详情
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: Text('文件保存成功'),
                    content: Text('文件路径: $pdfPath'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text('确定'),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      } else {
        throw Exception('PDF生成失败：无法创建文件');
      }
    } catch (e) {
      // 隐藏加载提示
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      // 显示详细错误信息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.error, color: Colors.white),
                  const SizedBox(width: 8),
                  Text('保存并分享失败'),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                '错误详情: $e',
                style: TextStyle(fontSize: 12),
              ),
            ],
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 5),
          action: SnackBarAction(
            label: '重试',
            textColor: Colors.white,
            onPressed: () => _saveAndShare(),
          ),
        ),
      );
    }
  }

  /// 🔧 处理识别成功回调
  void _handleRecognitionSuccess(String photoId, RecognitionResult result) {
    try {
      final TaskService taskService = ref.read(taskServiceProvider);
      taskService.updateRecognitionResult(photoId, result);

      if (mounted) {
        setState(() {
          // UI会通过TaskService的notifyListeners自动更新
        });
      }
    } catch (e) {
      AppLogger.error('处理识别成功回调失败: $e');
    }
  }

  // _handleRecognitionFailure 方法已删除，统一使用 _onRecognitionFailure

  Widget _buildParticipantsSection() {
    if (_currentTask == null || _currentTask!.participants.isEmpty)
      return const SizedBox.shrink();
    final workers = _currentTask!.participants
        .map((id) => allWorkers.firstWhere((w) => w.id == id,
            orElse: () => WorkerInfo(
                id: id, name: id, role: '', warehouse: '', group: '')))
        .toList();
    final totalTonnage = _currentTask!.quantity * 1.5;
    final perTonnage = workers.isNotEmpty ? totalTonnage / workers.length : 0.0;
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.08),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.people, color: Colors.blue, size: 18),
              const SizedBox(width: 8),
              Text('参与人员（${workers.length}人）',
                  style: const TextStyle(fontSize: 15, color: Colors.white)),
              const Spacer(),
              if (workers.isNotEmpty)
                Text('人均 ${perTonnage.toStringAsFixed(1)} 吨',
                    style: const TextStyle(
                        fontSize: 13, color: Colors.greenAccent)),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: workers
                .map((w) => Chip(
                      label: Text(w.name,
                          style: const TextStyle(color: Colors.white)),
                      backgroundColor: Colors.blue.withOpacity(0.2),
                    ))
                .toList(),
          ),
        ],
      ),
    );
  }

  /// 判断OCR文本是否有效
  bool _isValidOcrText(String text) {
    if (text.isEmpty) return false;

    // 过滤掉无意义的内容
    final cleanText = text.trim();
    if (cleanText.length < 3) return false;

    // 检查是否包含有用信息（产品代码、批号等模式）
    final hasProductCode = RegExp(r'[A-Z]{2,}-\d{4}').hasMatch(cleanText);
    final hasBatchNumber = RegExp(r'\d{6}[A-Z]\d{5}').hasMatch(cleanText);
    final hasDate = RegExp(r'20\d{2}[.-]\d{2}[.-]\d{2}').hasMatch(cleanText);
    final hasTime = RegExp(r'\d{2}:\d{2}').hasMatch(cleanText);

    return hasProductCode || hasBatchNumber || hasDate || hasTime;
  }

  /// 过滤OCR文本，只保留有用信息
  String _filterOcrText(String text) {
    final lines = text.split('\n');
    final filteredLines = <String>[];

    for (final line in lines) {
      final cleanLine = line.trim();
      if (cleanLine.isEmpty) continue;

      // 保留包含有用信息的行
      if (_isUsefulLine(cleanLine)) {
        filteredLines.add(cleanLine);
      }
    }

    return filteredLines.join('\n');
  }

  /// 判断文本行是否有用
  bool _isUsefulLine(String line) {
    // 产品代码模式
    if (RegExp(r'[A-Z]{2,}-\d{4}').hasMatch(line)) return true;

    // 批号模式
    if (RegExp(r'\d{6}[A-Z]\d{5}').hasMatch(line)) return true;

    // 日期模式
    if (RegExp(r'20\d{2}[.-]\d{2}[.-]\d{2}').hasMatch(line)) return true;

    // 时间模式
    if (RegExp(r'\d{2}:\d{2}').hasMatch(line)) return true;

    // 公司名称等关键词
    if (line.contains('VIILONGS') || line.contains('zKEDAL')) return true;

    // 过滤掉无意义的短文本和特殊字符
    if (line.length < 3) return false;
    if (RegExp(r'^[^\w\u4e00-\u9fff]+$').hasMatch(line)) return false;

    return false;
  }

  /// 美观弹窗工具方法
  Future<bool?> _showModernConfirmDialog({
    required BuildContext context,
    required String title,
    required String content,
    required String confirmText,
    required String cancelText,
    IconData? icon,
    Color? iconColor,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.symmetric(horizontal: 24),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 36),
          decoration: BoxDecoration(
            gradient: ThemeColors.primaryGradient,
            borderRadius: BorderRadius.circular(28),
            boxShadow: [
              BoxShadow(
                color: ThemeColors.primary.withOpacity(0.18),
                blurRadius: 32,
                offset: const Offset(0, 12),
              ),
            ],
            border:
                Border.all(color: Colors.white.withOpacity(0.18), width: 1.2),
            backgroundBlendMode: BlendMode.luminosity,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (icon != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: ThemeColors.blueGradient,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: ThemeColors.primary.withOpacity(0.18),
                        blurRadius: 16,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(icon, color: Colors.white, size: 40),
                ),
                const SizedBox(height: 18),
              ],
              Text(title,
                  style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: 1.1)),
              const SizedBox(height: 14),
              Text(content,
                  style: const TextStyle(fontSize: 16, color: Colors.white70),
                  textAlign: TextAlign.center),
              const SizedBox(height: 32),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.white,
                        side: const BorderSide(color: Colors.white70),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16)),
                        textStyle: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      child: Text(cancelText),
                    ),
                  ),
                  const SizedBox(width: 18),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ThemeColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16)),
                        textStyle: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.bold),
                        elevation: 0,
                      ),
                      child: Text(confirmText),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 用于替换原有的AlertDialog弹窗调用，例如：
  // final shouldLeave = await _showModernConfirmDialog(
  //   context: context,
  //   title: '确认退出',
  //   content: '任务尚未完成，确定要退出吗？\n您可以稍后继续拍照。',
  //   confirmText: '退出任务',
  //   cancelText: '继续拍照',
  //   icon: Icons.warning_amber_rounded,
  // );

  /// 美观二按钮弹窗工具方法（替换原三按钮弹窗）
  Future<String?> _showModernDoubleDialog({
    required BuildContext context,
    required String title,
    required String content,
    required String firstText,
    required String secondText,
    IconData? icon,
    Color? iconColor,
  }) {
    return showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.symmetric(horizontal: 24),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 24,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (icon != null) ...[
                Icon(icon,
                    color: iconColor ?? const Color(0xFF3B82F6), size: 40),
                const SizedBox(height: 16),
              ],
              Text(title,
                  style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1E293B))),
              const SizedBox(height: 12),
              Text(content,
                  style:
                      const TextStyle(fontSize: 15, color: Color(0xFF64748B)),
                  textAlign: TextAlign.center),
              const SizedBox(height: 28),
              Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop('continue'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFF1F5F9),
                        foregroundColor: const Color(0xFF1E293B),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                        elevation: 0,
                      ),
                      child:
                          Text(firstText, style: const TextStyle(fontSize: 16)),
                    ),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop('save'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: const Color(0xFF3B82F6),
                        side: const BorderSide(color: Color(0xFF3B82F6)),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12)),
                      ),
                      child: Text(secondText,
                          style: const TextStyle(fontSize: 16)),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 替换原有三按钮弹窗为二按钮弹窗
  Future<String?> _showExitPhotoTaskDialog() async {
    return await _showModernDoubleDialog(
      context: context,
      title: '任务未完成',
      content: '您有未完成的拍照任务，请选择操作：',
      firstText: '继续拍照',
      secondText: '保存并返回',
      icon: Icons.warning_amber_rounded,
      iconColor: Colors.orange,
    );
  }
}
