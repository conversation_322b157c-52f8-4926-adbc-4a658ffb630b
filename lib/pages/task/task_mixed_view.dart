import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/core/providers/app_providers.dart';
import 'package:go_router/go_router.dart';
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/services/workload_assignment_service.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/product_database.dart';
import 'package:loadguard/models/worker_info_data.dart';
import 'package:loadguard/services/logging_service.dart';
import '../../utils/theme_colors.dart';
import '../../utils/simple_navigation_helper.dart';
import '../../widgets/product_selector_modal.dart';
import 'worker_selection_page.dart';

/// 🚛 混装任务视图
class TaskMixedView extends ConsumerStatefulWidget {
  final String? template;

  const TaskMixedView({
    Key? key,
    this.template,
  }) : super(key: key);

  @override
  ConsumerState<TaskMixedView> createState() => _TaskMixedViewState();
}

class _TaskMixedViewState extends ConsumerState<TaskMixedView> {
  final List<BatchInfo> _batches = [];
  bool _isLoading = false;

  // 新增：主页面输入控制器
  final TextEditingController _productCodeController = TextEditingController();
  final TextEditingController _quantityController = TextEditingController();
  String? _selectedProductCode;
  String? _selectedPopularCategory;

  // 需要新增批号输入控制器
  final TextEditingController _batchNumberController = TextEditingController();

  // 🔧 新增：人员选择相关状态
  final Set<String> _selectedWorkerIds = {};
  final Set<String> _selectedWarehouses = {'1号库'}; // 默认选择1号库

  @override
  void initState() {
    super.initState();
    // 默认设置选中产品，确保界面不为空
    _selectedProductCode = 'LLD-7042';
    _productCodeController.text = 'LLD-7042';
    // 默认设置批号
    final prefixes = _generateRecentBatchPrefixes();
    if (prefixes.isNotEmpty) {
      _batchNumberController.text = '${prefixes.first}F10422';
    }
    // 默认数量
    _quantityController.text = '20';
    _productCodeController.addListener(() {
      // print('[LOG] _productCodeController: ' + _productCodeController.text);
    });
    _batchNumberController.addListener(() {
      // print('[LOG] _batchNumberController: ' + _batchNumberController.text);
    });
    _quantityController.addListener(() {
      // print('[LOG] _quantityController: ' + _quantityController.text);
    });
  }

  @override
  void dispose() {
    _productCodeController.dispose();
    _batchNumberController.dispose();
    _quantityController.dispose();
    super.dispose();
  }

  void _updateBatchSuggestionForMixed(String? productCode) {
    if (productCode != null && productCode.isNotEmpty) {
      final prefixes = _generateRecentBatchPrefixes();
      if (prefixes.isNotEmpty) {
        final prefix = prefixes.first;
        if (productCode.contains('开工料') || productCode.contains('HDPE')) {
          _batchNumberController.text = '${prefix}H10079-4B';
        } else {
          _batchNumberController.text = '${prefix}F10422';
        }
      }
    }
  }

  void _addBatch() {
    final productCode = _productCodeController.text.trim();
    final batchNumber = _batchNumberController.text.trim();
    final quantityText = _quantityController.text.trim();

    if (productCode.isEmpty || batchNumber.isEmpty || quantityText.isEmpty) {
      HapticFeedback.lightImpact(); // 错误震动反馈
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请填写所有信息')),
      );
      return;
    }

    int? quantity = int.tryParse(quantityText);
    if (quantity == null || quantity <= 0) {
      HapticFeedback.lightImpact(); // 错误震动反馈
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('数量必须为正整数')),
      );
      return;
    }

    // 成功添加震动反馈
    HapticFeedback.mediumImpact();

    setState(() {
      _batches.add(BatchInfo(
        productCode: productCode,
        batchNumber: batchNumber,
        plannedQuantity: quantity,
      ));
      _productCodeController.clear();
      _batchNumberController.clear();
      _quantityController.clear();
      _selectedProductCode = null;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('✅ 成功添加批次：$productCode'),
        backgroundColor: ThemeColors.success,
      ),
    );
  }

  /// 🚀 创建混装任务
  void _createMixedTask() async {
    if (_batches.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请至少添加一个批次')),
      );
      return;
    }

    try {
      setState(() => _isLoading = true);

      // 🔧 创建工作量分配记录
      if (_selectedWorkerIds.isNotEmpty) {
        try {
          final selectedWorkers = _selectedWorkerIds
              .map((id) {
                return allWorkers.firstWhere(
                  (w) => w.id == id,
                  orElse: () => const WorkerInfo(
                      id: '',
                      name: 'Unknown',
                      role: '',
                      warehouse: '',
                      group: ''),
                );
              })
              .where((w) => w.id.isNotEmpty)
              .toList();

          final totalQuantity =
              _batches.fold(0, (sum, batch) => sum + batch.plannedQuantity);
          final totalWeight = totalQuantity * 1.5; // 1托 = 1.5吨

          // 🔧 使用新的命名参数API保存工作量分配
          await WorkloadAssignmentService.saveAssignment(
            palletCount: totalQuantity,
            selectedForklifts:
                selectedWorkers.where((w) => w.role != '仓管人员').toList(),
            selectedWarehouses:
                selectedWorkers.where((w) => w.role == '仓管人员').toList(),
          );
        } catch (e) {
          // print('⚠️ 保存工作量分配失败: $e');
        }
      }

      if (!mounted) return;

      final taskService = ref.read(taskServiceProvider);
      final newTask = await taskService.createMixedTask(
        template: widget.template ?? '混装',
        batches: _batches,
        participants: _selectedWorkerIds.toList(),
      );

      // 🔧 修复：确保任务完全保存后再跳转
      await taskService.saveTask(newTask);

      // 🔧 修复：等待一小段时间确保数据同步
      await Future.delayed(const Duration(milliseconds: 100));

      if (mounted) {
        context.go('/task-detail/${newTask.id}?type=photos');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('创建任务失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _getDynamicTitle() {
    final t = widget.template ?? '混装';
    if (t.contains('混装')) {
      return '混装任务';
    }
    return '$t任务';
  }

  /// 生成最近8天的批号前缀（yyMMdd）
  List<String> _generateRecentBatchPrefixes({int days = 8}) {
    final now = DateTime.now();
    return List.generate(days, (i) {
      final date = now.subtract(Duration(days: i));
      final y = date.year % 100;
      final m = date.month.toString().padLeft(2, '0');
      final d = date.day.toString().padLeft(2, '0');
      return '$y$m$d';
    });
  }

  Widget _buildProfessionalHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.verified, color: ThemeColors.primaryHover, size: 28),
              const SizedBox(width: 12),
              const Text(
                'ML Kit V2专业版',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '高准确率识别 | 预设牌号快速选择 | 智能批号验证',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProductCodeSelector() {
    // 确保产品选择不为空
    _selectedProductCode ??= 'LLD-7042';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '选择预设牌号',
          style: TextStyles.subtitle.copyWith(fontSize: 16),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withOpacity(0.3)),
          ),
          child: InkWell(
            onTap: () {
              _showProductSelector(context);
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  Icon(Icons.category, color: Colors.white.withOpacity(0.8)),
                  const SizedBox(width: 16),
                  if (_selectedProductCode != null) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        color: _getProductCategory(_selectedProductCode!),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        _getProductCategoryName(_selectedProductCode!),
                        style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        _selectedProductCode!,
                        style: const TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                            fontWeight: FontWeight.w600),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    Icon(Icons.arrow_drop_down,
                        color: Colors.white.withOpacity(0.8)),
                  ],
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 获取产品分类对应的颜色
  Color _getProductCategory(String productCode) {
    final product = ProductDatabase.getAllProducts().firstWhere(
      (p) => p.code == productCode,
      orElse: () => ProductInfo(
        code: productCode,
        name: '',
        category: 'LLDPE',
        commonBatchPrefixes: [],
        priority: 0,
      ),
    );
    return ProductDatabase.getCategoryColor(product.category);
  }

  // 获取产品分类名称
  String _getProductCategoryName(String productCode) {
    final product = ProductDatabase.getAllProducts().firstWhere(
      (p) => p.code == productCode,
      orElse: () => ProductInfo(
        code: productCode,
        name: '',
        category: 'LLDPE',
        commonBatchPrefixes: [],
        priority: 0,
      ),
    );
    return product.category;
  }

  // 显示产品选择器弹窗 - 使用优化版本
  void _showProductSelector(BuildContext context) {
    SimpleNavigationHelper.showManagedBottomSheet(
      context: context,
      isDismissible: true,
      builder: (context) {
        return ProductSelectorModal(
          selectedProductCode: _selectedProductCode,
          onProductSelected: (productCode) {
            setState(() {
              _selectedProductCode = productCode;
              _productCodeController.text = productCode;
            });
            _updateBatchSuggestionForMixed(productCode);
            // 添加选择反馈
            HapticFeedback.selectionClick();
          },
        );
      },
    );
  }

  Widget _buildBatchNumberInput() {
    final prefixes = _generateRecentBatchPrefixes();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '批次号',
          style: TextStyles.subtitle.copyWith(fontSize: 16),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _batchNumberController,
          style: const TextStyle(color: Colors.white, fontSize: 16),
          decoration: InputDecoration(
            hintText: '输入批号 (如：250627F10422)',
            hintStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
            filled: true,
            fillColor: Colors.white.withOpacity(0.15),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.white, width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
          onChanged: (val) {
            // print('[LOG] onChanged batchNumber: ' + val);
          },
        ),
        if (prefixes.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            '常用批号前缀：',
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          // 改为两行显示，不使用水平滚动
          Wrap(
            spacing: 8, // 水平间距
            runSpacing: 4, // 垂直间距
            children: prefixes.map((prefix) {
              return GestureDetector(
                onTap: () {
                  // print('[LOG] prefix tap: ' + prefix);
                  _batchNumberController.text = '${prefix}F10422';
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    prefix,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 12,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildQuantityInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '数量 (托)',
          style: TextStyle(
            color: Colors.white.withOpacity(0.9),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _quantityController,
          keyboardType: TextInputType.number,
          style: const TextStyle(color: Colors.white, fontSize: 16),
          decoration: InputDecoration(
            hintText: '数量（例如：20）',
            hintStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
            filled: true,
            fillColor: Colors.white.withOpacity(0.15),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.white.withOpacity(0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.white, width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
          onChanged: (value) {
            // print('[LOG] onChanged quantity: ' + value);
            // 🔧 测试：移除setState调用，避免重建干扰输入
            // setState(() {
            //   print('[LOG] setState in onChanged quantity, value: '+value);
            // });
          },
        ),
      ],
    );
  }

  /// 📊 **常用牌号快速选择**
  Widget _buildQuickSelectGrid() {
    final categories = ['LLDPE', 'HDPE', 'mPE', 'PP', 'SAN', 'PS'];
    final selectedCategory = _selectedPopularCategory ?? categories[0];
    final products = ProductDatabase.getPopularProductsByCategory(
        selectedCategory,
        limit: 8);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '常用牌号快速选择',
          style: TextStyle(
            color: Colors.white.withOpacity(0.9),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),

        // 类别标签过滤
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: categories.map((cat) {
              final selected = selectedCategory == cat;
              final color = ProductDatabase.getCategoryColor(cat);
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      _selectedPopularCategory = cat;
                    });
                  },
                  style: OutlinedButton.styleFrom(
                    backgroundColor: selected
                        ? color.withOpacity(0.3)
                        : Colors.white.withOpacity(0.08),
                    foregroundColor: Colors.white,
                    side: BorderSide(
                        color:
                            selected ? color : Colors.white.withOpacity(0.3)),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20)),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  ),
                  child: Text(cat,
                      style:
                          TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                ),
              );
            }).toList(),
          ),
        ),

        const SizedBox(height: 12),

        // 牌号网格
        LayoutBuilder(
          builder: (context, constraints) {
            final double itemWidth =
                (constraints.maxWidth - (3 * 8)) / 4; // 4 columns with spacing
            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                mainAxisSpacing: 8,
                crossAxisSpacing: 8,
                childAspectRatio:
                    math.max(1.8, itemWidth / 40), // Ensure minimum height
              ),
              itemCount: 8,
              itemBuilder: (context, index) {
                if (index < products.length) {
                  final product = products[index];
                  final isSelected = _selectedProductCode == product.code;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedProductCode = product.code;
                        _productCodeController.text = product.code;
                      });
                      _updateBatchSuggestionForMixed(product.code);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 4, vertical: 8),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Colors.white.withOpacity(0.3)
                            : Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: isSelected
                              ? Colors.white
                              : Colors.white.withOpacity(0.3),
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (index < 3) ...[
                            const Text('🔥', style: TextStyle(fontSize: 12)),
                            const SizedBox(height: 2),
                          ],
                          Flexible(
                            child: Text(
                              product.code,
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 11,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                } else {
                  // 空白占位
                  return Container(
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.white.withOpacity(0.1)),
                    ),
                  );
                }
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildAddBatchButton() {
    return Container(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _addBatch,
        style: ElevatedButton.styleFrom(
          backgroundColor: ThemeColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_circle_outline, color: Colors.white),
            const SizedBox(width: 8),
            Text(
              '添加批次',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBatchList() {
    if (_batches.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(40),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withOpacity(0.2)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: Colors.white.withOpacity(0.7),
            ),
            const SizedBox(height: 16),
            Text(
              '暂无批次',
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '请在上方输入批次信息后点击"添加批次"',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '已添加的批次 (${_batches.length})',
          style: TextStyle(
            color: Colors.white.withOpacity(0.9),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...List.generate(_batches.length, (index) {
          final batch = _batches[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        batch.productCode,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '数量: ${batch.plannedQuantity} 托',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 14,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () {
                    setState(() {
                      _batches.removeAt(index);
                    });
                  },
                  icon: Icon(
                    Icons.delete_outline,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildStartPhotoButton() {
    return Container(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _batches.isEmpty ? null : _createMixedTask,
        style: ElevatedButton.styleFrom(
          backgroundColor: _batches.isEmpty ? Colors.grey : ThemeColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 18),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.camera_alt, color: Colors.white),
                  const SizedBox(width: 8),
                  Text(
                    '开始拍照 专业版识别',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    return SimpleNavigationHelper.buildStandardPage(
      onBackPressed: () {
        HapticFeedback.lightImpact();
        context.go('/template-selection');
      },
      enableSwipeBack: true,
      child: Scaffold(
        appBar: AppBar(
          title: Text(_getDynamicTitle()),
          backgroundColor: ThemeColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          automaticallyImplyLeading: true,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              HapticFeedback.lightImpact();
              context.go('/template-selection');
            },
          ),
        ),
        body: Container(
          height: screenHeight,
          decoration: const BoxDecoration(
            gradient: ThemeColors.primaryGradient,
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildProfessionalHeader(),
                      const SizedBox(height: 32),

                      _buildProductCodeSelector(),
                      const SizedBox(height: 20),

                      _buildBatchNumberInput(),
                      const SizedBox(height: 20),

                      _buildQuantityInput(),
                      const SizedBox(height: 24),

                      _buildQuickSelectGrid(),
                      const SizedBox(height: 32),

                      _buildAddBatchButton(),
                      const SizedBox(height: 32),

                      // 🔧 新增：参与人员选择
                      _buildWorkerSelection(),
                      const SizedBox(height: 32),

                      _buildBatchList(),
                      const SizedBox(height: 32),

                      _buildStartPhotoButton(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<bool> _onWillPop() async {
    // 🆕 自动保存草稿，无弹窗直接返回
    final hasInputChanges = _productCodeController.text.isNotEmpty ||
        _batchNumberController.text.isNotEmpty ||
        _quantityController.text.isNotEmpty;
    final hasAddedBatches = _batches.isNotEmpty;

    if ((hasInputChanges || hasAddedBatches) && !_isLoading) {
      // 自动保存当前输入状态作为草稿
      await _autoSaveDraft();
    }

    // 直接返回，无弹窗确认
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    } else {
      context.go('/template-selection');
    }
    return false;
  }

  /// 🔄 自动保存草稿状态
  Future<void> _autoSaveDraft() async {
    try {
      // 这里可以实现将当前输入保存到本地存储
      // 例如使用SharedPreferences保存草稿
      // LoggingService.info('🗂️ 混装任务草稿已自动保存', tag: 'MixedTaskDraft');
      // print('🗂️ 混装任务草稿已自动保存');
    } catch (e) {
      // LoggingService.error('❌ 草稿保存失败', error: e, tag: 'MixedTaskDraft');
      // print('❌ 草稿保存失败: $e');
    }
  }

  /// 🔧 新增：参与人员选择组件
  Widget _buildWorkerSelection() {
    final totalQuantity =
        _batches.fold(0, (sum, batch) => sum + batch.plannedQuantity);
    final totalTonnage = totalQuantity * 1.5;
    final perPersonTonnage = _selectedWorkerIds.isNotEmpty
        ? totalTonnage / _selectedWorkerIds.length
        : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '参与人员',
          style: TextStyle(
            color: Colors.white.withOpacity(0.9),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),

        // 人员选择输入框
        _buildWorkerSelectionField(),
        const SizedBox(height: 12),

        // 人均分配信息 - 只在有选中人员且有数量时显示
        if (totalQuantity > 0 && _selectedWorkerIds.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '人均分配',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                      Text(
                        '${perPersonTonnage.toStringAsFixed(1)} 吨',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: ThemeColors.primaryHover,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_selectedWorkerIds.length}人参与',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// 🔧 新增：人员选择输入框
  Widget _buildWorkerSelectionField() {
    final selectedWorkers = _selectedWorkerIds.map((id) {
      return allWorkers.firstWhere(
        (w) => w.id == id,
        orElse: () => const WorkerInfo(
            id: '', name: 'Unknown', role: '', warehouse: '', group: ''),
      );
    }).toList();

    String displayText;
    if (_selectedWarehouses.isEmpty) {
      displayText = '请先选择库区';
    } else if (selectedWorkers.isEmpty) {
      displayText = '${_selectedWarehouses.join("、")} - 点击选择人员';
    } else {
      final warehouseText = _selectedWarehouses.join("、");
      final workerText = selectedWorkers.map((w) => w.name).join("、");
      displayText =
          '$warehouseText - 已选${selectedWorkers.length}人: $workerText';
    }

    return GestureDetector(
      onTap: _openWorkerSelectionPage,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.15),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            const Icon(Icons.people, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                displayText,
                style: TextStyle(
                  color:
                      selectedWorkers.isEmpty ? Colors.white70 : Colors.white,
                  fontSize: 16,
                  fontWeight: selectedWorkers.isEmpty
                      ? FontWeight.normal
                      : FontWeight.w500,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (selectedWorkers.isNotEmpty) ...[
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: ThemeColors.primaryHover,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${selectedWorkers.length}人',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                const Icon(Icons.arrow_forward_ios,
                    color: Colors.white70, size: 16),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 🔧 新增：打开全屏人员选择页面
  void _openWorkerSelectionPage() async {
    final totalQuantity =
        _batches.fold(0, (sum, batch) => sum + batch.plannedQuantity);

    final result = await Navigator.of(context).push<Map<String, dynamic>>(
      MaterialPageRoute(
        builder: (context) => WorkerSelectionPage(
          selectedWarehouses: _selectedWarehouses,
          selectedWorkerIds: _selectedWorkerIds,
          quantity: totalQuantity,
        ),
        fullscreenDialog: true,
      ),
    );

    if (result != null && mounted) {
      setState(() {
        _selectedWarehouses.clear();
        _selectedWarehouses.addAll(result['warehouses'] ?? <String>{});

        _selectedWorkerIds.clear();
        _selectedWorkerIds.addAll(result['workerIds'] ?? <String>{});
      });
    }
  }
}
