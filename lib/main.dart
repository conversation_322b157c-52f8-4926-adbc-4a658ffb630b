import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/services/performance_optimizer.dart';
import 'package:loadguard/services/input_optimizer.dart';

import 'package:loadguard/services/logging_service.dart';
import 'package:loadguard/pages/app_launcher_page.dart';
import 'package:loadguard/pages/home_page.dart';
import 'package:loadguard/pages/template_selection_page.dart';
import 'package:loadguard/pages/enhanced_task_page.dart';
import 'package:loadguard/pages/result_page.dart';
import 'package:loadguard/utils/theme_colors.dart';
import 'package:loadguard/utils/material3_theme.dart';
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/core/providers/app_providers.dart';
import 'package:loadguard/utils/enhanced_navigation_helper.dart';
import 'package:loadguard/utils/router_config_manager.dart';
import 'package:loadguard/utils/route_history_manager.dart';
import 'package:loadguard/utils/route_performance_monitor.dart';
import 'package:loadguard/pages/about_page.dart';
import 'package:loadguard/pages/performance_stats_page.dart';
import 'package:loadguard/pages/enterprise_control_panel.dart';
import 'package:loadguard/pages/strict_activation_page.dart';
import 'package:loadguard/pages/workload_management_page.dart';
import 'package:loadguard/pages/workload_statistics_page.dart';
import 'package:loadguard/utils/strict_route_guard.dart';
import 'package:loadguard/pages/admin_management_page.dart';
import 'package:loadguard/pages/enhanced_security_management_page.dart';
import 'package:loadguard/pages/task/worker_selection_page.dart';

import 'package:loadguard/core/providers/app_providers.dart';
import 'package:loadguard/services/hive_storage_service.dart';
import 'dart:ui' as ui;
import 'package:loadguard/services/app_security_service.dart';

void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 🚀 启用Impeller渲染引擎（如果支持）
  _enableImpellerIfSupported();

  // 设置系统UI
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // 设置屏幕方向
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  try {
    LoggingService.info('🚀 应用启动开始', tag: 'Main');

    // 🔧 修复：安全初始化优化服务
    await _initializeOptimizationServicesSafely();

    // 🗄️ 初始化Hive存储
    await HiveStorageService.initialize();

    // 🔧 修复：初始化严格的路由守卫
    await StrictRouteGuard.initialize();
    
    // 启动定期安全检查
    StrictRouteGuard.startPeriodicSecurityCheck();

    // 初始化路由
    final router = _createRouter();

    LoggingService.info('✅ 应用初始化完成', tag: 'Main');

    // 启动应用 - 完全使用Riverpod
    final container = ProviderContainer();
    ProviderBridge.initialize(container);

    // 🔧 修复：预热TaskService，确保它在应用启动时就被初始化
    try {
      final taskService = container.read(taskServiceProvider);
      LoggingService.info('✅ TaskService预热完成，任务数量: ${taskService.tasks.length}', tag: 'Main');
    } catch (e) {
      LoggingService.error('❌ TaskService预热失败: $e', tag: 'Main');
    }

    runApp(
      UncontrolledProviderScope(
        container: container,
        child: LoadGuardApp(router: router),
      ),
    );
  } catch (e, stackTrace) {
    LoggingService.error('❌ 应用启动失败',
        error: e, stackTrace: stackTrace, tag: 'Main');

    // 即使初始化失败也要启动应用，显示错误页面
    final router = _createRouter();
    final container = ProviderContainer();
    ProviderBridge.initialize(container);

    runApp(
      UncontrolledProviderScope(
        container: container,
        child: LoadGuardApp(router: router),
      ),
    );
  }
}

/// 🚀 启用Impeller渲染引擎（如果支持）
void _enableImpellerIfSupported() {
  try {
    // 检查是否支持Impeller
    if (ui.PlatformDispatcher.instance.views.isNotEmpty) {
      final view = ui.PlatformDispatcher.instance.views.first;
      // 🆕 16.0.0优化：使用正确的API检查渲染引擎
      try {
        // 这里可以添加渲染引擎检测逻辑
        LoggingService.info('📱 渲染引擎检测完成', tag: 'Main');
      } catch (e) {
        LoggingService.info('📱 使用默认渲染引擎', tag: 'Main');
      }
    }
  } catch (e) {
    LoggingService.warning('⚠️ 无法检测渲染引擎状态', tag: 'Main');
  }
}

/// 🔧 安全初始化优化服务
Future<void> _initializeOptimizationServicesSafely() async {
  try {
    // 初始化性能优化服务
    await PerformanceOptimizer().initialize();
    LoggingService.info('✅ 性能优化服务初始化完成', tag: 'Main');

    // 初始化输入优化服务
    await InputOptimizer().initialize();
    LoggingService.info('✅ 输入优化服务初始化完成', tag: 'Main');

    // 许可证数据保护已集成到统一存储服务中
    LoggingService.info('✅ 许可证统一存储服务就绪', tag: 'Main');
  } catch (e, stackTrace) {
    LoggingService.error('❌ 优化服务初始化失败',
        error: e, stackTrace: stackTrace, tag: 'Main');
    // 不抛出异常，让应用继续启动
  }
}



/// 🎯 创建优化的GoRouter配置 - 适配16.0.0版本
GoRouter _createRouter() {
  // 🆕 16.0.0优化：使用路由配置管理器创建路由
  final routes = [
    // 🏠 启动页面
    GoRoute(
      path: '/',
      name: 'launcher',
      builder: RouterConfigManager.createLoadingPageBuilder(
          (context, state) => const AppLauncherPage()),
    ),

    // 🏠 主页 - 使用严格的路由守卫
    GoRoute(
      path: '/home',
      name: 'home',
      builder: StrictRouteGuard.createProtectedPageBuilder(
        (context, state) => const HomePage(),
      ),
    ),

    // 🔐 激活页面 - 统一的激活页面
    GoRoute(
      path: '/activation',
      name: 'activation',
      builder: (context, state) => const StrictActivationPage(),
    ),

    // 📋 模板选择页面
    GoRoute(
      path: '/template-selection',
      name: 'template-selection',
      builder: RouterConfigManager.createAuthPageBuilder(
        (context, state) => const TemplateSelectionPage(),
        RouterConfigManager.requiresAuth('template-selection'),
      ),
    ),

    // 🆕 16.0.0优化：新任务创建页面
    GoRoute(
      path: '/enhanced-task/new',
      name: 'new-task',
      builder: RouterConfigManager.createAuthPageBuilder(
        (context, state) {
          final type = state.uri.queryParameters['type'] ?? 'single';
          final template = state.uri.queryParameters['template'] ?? '';
          return EnhancedTaskPage(
            taskId: 'new',
            type: type,
            template: template,
          );
        },
        RouterConfigManager.requiresAuth('new-task'),
      ),
    ),

    // 📄 任务详情页面
    GoRoute(
      path: '/task-detail/:taskId',
      name: 'task-detail',
      builder: RouterConfigManager.createAuthPageBuilder(
        (context, state) {
          print('🔍 [Router] 任务详情页面路由被调用');
          print('🔍 [Router] state.pathParameters: ${state.pathParameters}');
          print('🔍 [Router] state.uri.queryParameters: ${state.uri.queryParameters}');
          
          // 🆕 16.0.0优化：参数验证
          if (!RouterConfigManager.validateRouteParameters(state, ['taskId'])) {
            print('❌ [Router] 参数验证失败');
            return _buildParameterErrorPage(context, state);
          }

          final taskId = state.pathParameters['taskId'] ?? '';
          final type = state.uri.queryParameters['type'];
          print('🔍 [Router] taskId: $taskId, type: $type');
          print('🔍 [Router] 创建 EnhancedTaskPage');
          return EnhancedTaskPage(taskId: taskId, type: type);
        },
        RouterConfigManager.requiresAuth('task-detail'),
      ),
    ),

    // 📊 结果页面
    GoRoute(
      path: '/result/:taskId',
      name: 'result',
      builder: RouterConfigManager.createAuthPageBuilder(
        (context, state) {
          // 🆕 16.0.0优化：参数验证
          if (!RouterConfigManager.validateRouteParameters(state, ['taskId'])) {
            return _buildParameterErrorPage(context, state);
          }

          final taskId = state.pathParameters['taskId'] ?? '';
          return ResultPage(taskId: taskId);
        },
        RouterConfigManager.requiresAuth('result'),
      ),
    ),

    // 🔒 安全管理页面（使用增强版本）
    GoRoute(
      path: '/security-management',
      name: 'security-management',
      builder: RouterConfigManager.createAuthPageBuilder(
        (context, state) => const EnhancedSecurityManagementPage(),
        RouterConfigManager.requiresAuth('security-management'),
      ),
    ),

    // ℹ️ 关于页面
    GoRoute(
      path: '/about',
      name: 'about',
      builder: RouterConfigManager.createAuthPageBuilder(
        (context, state) => const AboutPage(),
        RouterConfigManager.requiresAuth('about'),
      ),
    ),

    // 📈 性能统计页面
    GoRoute(
      path: '/performance-stats',
      name: 'performance-stats',
      builder: RouterConfigManager.createAuthPageBuilder(
        (context, state) => const PerformanceStatsPage(),
        RouterConfigManager.requiresAuth('performance-stats'),
      ),
    ),

    // 🏢 企业控制面板
    GoRoute(
      path: '/enterprise-control',
      name: 'enterprise-control',
      builder: RouterConfigManager.createAuthPageBuilder(
        (context, state) => const EnterpriseControlPanel(),
        RouterConfigManager.requiresAuth('enterprise-control'),
      ),
    ),

    // 📋 工作负载管理页面
    GoRoute(
      path: '/workload-management',
      name: 'workload-management',
      builder: RouterConfigManager.createAuthPageBuilder(
        (context, state) => const WorkloadManagementPage(),
        RouterConfigManager.requiresAuth('workload-management'),
      ),
    ),

    // 📊 工作负载统计页面
    GoRoute(
      path: '/workload-statistics',
      name: 'workload-statistics',
      builder: RouterConfigManager.createAuthPageBuilder(
        (context, state) => const WorkloadStatisticsPage(),
        RouterConfigManager.requiresAuth('workload-statistics'),
      ),
    ),

    // 👨‍💼 管理员管理页面
    GoRoute(
      path: '/admin-management',
      name: 'admin-management',
      builder: RouterConfigManager.createAuthPageBuilder(
        (context, state) => const AdminManagementPage(),
        RouterConfigManager.requiresAuth('admin-management'),
      ),
    ),

    // 👥 工人选择页面
    GoRoute(
      path: '/worker-selection',
      name: 'worker-selection',
      builder: RouterConfigManager.createAuthPageBuilder(
        (context, state) {
          final warehouses =
              state.uri.queryParameters['warehouses']?.split(',').toSet() ??
                  <String>{};
          final workerIds =
              state.uri.queryParameters['workerIds']?.split(',').toSet() ??
                  <String>{};
          final quantity =
              int.tryParse(state.uri.queryParameters['quantity'] ?? '0') ?? 0;
          return WorkerSelectionPage(
            selectedWarehouses: warehouses,
            selectedWorkerIds: workerIds,
            quantity: quantity,
          );
        },
        RouterConfigManager.requiresAuth('worker-selection'),
      ),
    ),
  ];

  // 🆕 16.0.0优化：使用路由配置管理器创建优化的路由器
  return RouterConfigManager.createOptimizedRouter(
    routes: routes,
    initialLocation: '/',
    errorBuilder: _buildErrorPage,
    redirect: _globalRedirect,
    debugLogDiagnostics: true,
  );
}

/// 🆕 16.0.0优化：全局重定向逻辑 - 使用严格的路由守卫
String? _globalRedirect(BuildContext context, GoRouterState state) {
  // 记录路由历史
  final routeName = _getRouteNameFromPath(state.uri.path);
  if (routeName != null) {
    RouteHistoryManager.addRoute(routeName);
    RoutePerformanceMonitor.startMonitoring(routeName);
  }

  // 🔧 修复：使用严格的路由守卫进行重定向
  return StrictRouteGuard.globalRedirect(context, state);
}

/// 🆕 16.0.0优化：从路径获取路由名称
String? _getRouteNameFromPath(String path) {
  final pathMap = {
    '/': 'launcher',
    '/home': 'home',
    '/activation': 'activation',
    '/template-selection': 'template-selection',
    '/enhanced-task/new': 'new-task',
    '/security-management': 'security-management',
    '/about': 'about',
    '/performance-stats': 'performance-stats',
    '/enterprise-control': 'enterprise-control',
    '/workload-management': 'workload-management',
    '/workload-statistics': 'workload-statistics',
    '/admin-management': 'admin-management',
    '/worker-selection': 'worker-selection',
  };

  // 处理带参数的路由
  if (path.startsWith('/task-detail/')) return 'task-detail';
  if (path.startsWith('/result/')) return 'result';

  return pathMap[path];
}

/// 🆕 16.0.0优化：参数错误页面构建器
Widget _buildParameterErrorPage(BuildContext context, GoRouterState state) {
  return Scaffold(
    appBar: AppBar(
      title: const Text('参数错误'),
      backgroundColor: ThemeColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
    ),
    body: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.orange,
          ),
          const SizedBox(height: 16),
          const Text(
            '参数错误',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '路径: ${state.uri.path}',
            style: const TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 8),
          const Text(
            '缺少必要的参数',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.goNamed('home'),
            child: const Text('返回首页'),
          ),
        ],
      ),
    ),
  );
}

/// 🆕 16.0.0优化：改进的错误页面构建
Widget _buildErrorPage(BuildContext context, GoRouterState state) {
  return Scaffold(
    appBar: AppBar(
      title: const Text('页面错误'),
      backgroundColor: ThemeColors.primary,
      foregroundColor: Colors.white,
      // 🆕 16.0.0新特性：改进的AppBar
      elevation: 0,
      centerTitle: true,
    ),
    body: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            '页面加载失败',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '路径: ${state.uri.path}',
            style: const TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 24),
          // 🆕 16.0.0优化：使用命名路由
          ElevatedButton(
            onPressed: () => context.goNamed('home'),
            child: const Text('返回首页'),
          ),
        ],
      ),
    ),
  );
}

/// 🎨 主应用组件 - 适配Material 3和go_router 16.0.0
class LoadGuardApp extends StatelessWidget {
  final GoRouter router;

  const LoadGuardApp({
    super.key,
    required this.router,
  });

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: '装运卫士',
      debugShowCheckedModeBanner: false,
      routerConfig: router,

      // 🎨 使用Material 3主题系统
      theme: Material3Theme.lightTheme,
      darkTheme: Material3Theme.darkTheme,

      // 🌙 自动跟随系统主题
      themeMode: ThemeMode.system,

      // 🆕 16.0.0新特性：改进的构建器
      builder: (context, child) {
        // 添加全局错误处理
        return _buildErrorBoundary(child!);
      },
    );
  }

  /// 构建错误边界
  Widget _buildErrorBoundary(Widget child) {
    return ErrorBoundary(
      child: child,
    );
  }
}

/// 🆕 16.0.0优化：改进的错误边界组件
class ErrorBoundary extends StatefulWidget {
  final Widget child;

  const ErrorBoundary({
    super.key,
    required this.child,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Error? _error;

  @override
  void initState() {
    super.initState();
    // 🆕 16.0.0新特性：改进的错误处理
    FlutterError.onError = (FlutterErrorDetails details) {
      setState(() {
        _error = details.exception as Error?;
      });
    };
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('应用错误'),
          backgroundColor: ThemeColors.primary,
          foregroundColor: Colors.white,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                '应用发生错误',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '错误信息: ${_error.toString()}',
                style: const TextStyle(color: Colors.grey),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _error = null;
                  });
                },
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      );
    }

    return widget.child;
  }
}
