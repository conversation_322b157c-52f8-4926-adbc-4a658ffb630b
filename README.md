# 装运卫士 (LoadGuard)

专业级货物装运管理应用

## 🚀 快速开始

### 环境要求

- **Flutter SDK**: 3.24.0 或更高版本
- **Dart SDK**: 3.5.0 或更高版本
- **Android Studio**: 最新稳定版本
- **Java JDK**: 17 或更高版本

### 安装步骤

1. **安装Flutter**
   ```bash
   # 下载Flutter SDK
   # 访问 https://flutter.dev/docs/get-started/install
   
   # 验证安装
   flutter doctor
   ```

2. **克隆项目**
   ```bash
   git clone <repository-url>
   cd loadguard
   ```

3. **安装依赖**
   ```bash
   flutter pub get
   ```

4. **运行项目**
   ```bash
   # 调试模式
   flutter run
   
   # 或指定设备
   flutter run -d <device-id>
   ```

## 🛠️ 开发环境配置

### Android开发

1. **安装Android Studio**
2. **配置Android SDK**
   - 最低API级别: 21 (Android 5.0)
   - 目标API级别: 最新稳定版本
   - 构建工具: 最新版本

3. **配置模拟器**
   ```bash
   # 列出可用设备
   flutter devices
   
   # 创建模拟器
   flutter emulators --create
   ```

### 网络配置（中国大陆用户）

项目已配置国内镜像源，无需额外设置：
- 阿里云Maven镜像
- 腾讯云Maven镜像
- 华为云Maven镜像

## 📦 项目结构

```
lib/
├── core/                 # 核心功能
│   ├── providers/       # 状态管理
│   └── lifecycle_mixin.dart
├── models/              # 数据模型
├── pages/               # 页面组件
├── services/            # 业务服务
├── utils/               # 工具类
├── widgets/             # 通用组件
└── main.dart           # 应用入口

android/                 # Android平台配置
ios/                     # iOS平台配置
web/                     # Web平台配置
```

## 🔧 常见问题解决

### 编译错误

1. **清理缓存**
   ```bash
   flutter clean
   flutter pub get
   ```

2. **检查Flutter版本**
   ```bash
   flutter --version
   flutter doctor
   ```

3. **更新依赖**
   ```bash
   flutter pub upgrade
   ```

### 网络问题

如果遇到依赖下载问题：

1. **检查网络连接**
2. **使用VPN**（如果在网络受限环境）
3. **手动配置代理**

### Android构建问题

1. **检查Java版本**
   ```bash
   java --version
   ```

2. **清理Gradle缓存**
   ```bash
   cd android
   ./gradlew clean
   ```

3. **重新构建**
   ```bash
   flutter build apk --debug
   ```

## 🧪 测试

```bash
# 运行单元测试
flutter test

# 运行集成测试
flutter test integration_test/

# 代码分析
flutter analyze
```

## 📱 构建发布版本

```bash
# Android APK
flutter build apk --release

# Android App Bundle
flutter build appbundle --release

# iOS (需要macOS)
flutter build ios --release
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 技术支持

如果遇到问题，请：

1. 检查本README的常见问题部分
2. 查看项目Issues
3. 创建新的Issue并提供详细信息：
   - Flutter版本 (`flutter --version`)
   - 错误信息
   - 重现步骤
   - 设备信息
