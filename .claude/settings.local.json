{"permissions": {"allow": ["Bash(ls:*)", "<PERSON><PERSON>(curl:*)", "Ba<PERSON>(flutter:*)", "Bash(/mnt/d/flutter/bin/flutter pub get)", "Bash(find:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(/usr/bin/flutter pub get)", "Bash(/mnt/d/flutter/bin/flutter doctor)", "<PERSON><PERSON>(dos2unix:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(dart:*)", "Bash(export:*)", "Bash(/mnt/d/flutter/bin/dart --version)", "<PERSON><PERSON>(./gradlew:*)", "Bash(/mnt/d/flutter/bin/dart pub get)", "<PERSON><PERSON>(chmod:*)", "Bash(grep:*)", "Bash(rg:*)", "WebFetch(domain:developers.google.com)", "WebFetch(domain:github.com)", "Bash(/mnt/d/flutter/bin/cache/dart-sdk/bin/dart.exe --version)", "Bash(apt list:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "WebFetch(domain:mvnrepository.com)", "Bash(dpkg:*)", "Bash(git add:*)", "Bash(timeout 60 flutter analyze --no-fatal-infos --no-fatal-warnings)", "<PERSON><PERSON>(timeout:*)", "Bash(for:*)", "Bash(do if grep -q \"WillPopScope\\|PopScope\" \"$file\")", "Bash(! grep -q \"navigation_helper\\|gesture_navigation_manager\" \"$file\")", "Bash(then echo \"NO UNIFIED NAV: $file\")", "Bash(fi)", "Bash(done)", "Bash(do echo \"Checking $file\")", "Bash(if grep -q \"WillPopScope\\|PopScope\" \"$file\")", "<PERSON><PERSON>(then echo \"  HAS PopScope/WillPopScope\")", "Bash(if grep -q \"navigation_helper\\|gesture_navigation_manager\" \"$file\")", "<PERSON><PERSON>(then echo \"  HAS unified nav\")", "Bash(else echo \"  NO unified nav\")", "Bash(echo)", "<PERSON><PERSON>(iconv:*)", "Bash(then echo \"WIDGET NEEDS UPDATE: $file\")", "Bash(do if ! grep -q \"navigation_helper\" \"$file\")", "Bash(! grep -q \"gesture_navigation_manager\" \"$file\")", "Bash(then echo \"MAY NEED UPDATE (Scaffold only): $file\")", "<PERSON><PERSON>(pkill:*)", "Bash(kill:*)", "Bash(sudo fuser:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(echo:*)", "Bash(PUB_HOSTED_URL=https://pub.flutter-io.cn FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn dart pub get)", "Bash(PUB_HOSTED_URL=https://mirrors.tuna.tsinghua.edu.cn/dart-pub FLUTTER_STORAGE_BASE_URL=https://mirrors.tuna.tsinghua.edu.cn/flutter dart pub get)", "Bash(PUB_HOSTED_URL=https://mirrors.aliyun.com/dart-pub FLUTTER_STORAGE_BASE_URL=https://mirrors.aliyun.com/flutter timeout 60s dart pub get)", "Bash(ping:*)", "Bash(PUB_HOSTED_URL=https://pub.dev dart pub get --verbose)", "<PERSON><PERSON>(adb:*)", "Bash(ANDROID_HOME=\"/mnt/c/Users/<USER>/AppData/Local/Android/Sdk\")", "Bash(\"$ANDROID_HOME/platform-tools/adb.exe\" devices)", "Bash(PUB_HOSTED_URL=https://pub.flutter-io.cn FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn flutter run -d emulator-5554)", "WebFetch(domain:developers.google.cn)", "WebFetch(domain:pub.dev)", "Bash(git commit:*)", "Bash(do)", "<PERSON><PERSON>(cat:*)", "Bash(/mnt/d/flutter/bin/flutter --version)", "Bash(cd \"D:\\zhuangyunweishi\")", "Bash(flutter build apk --release)", "Bash(git rm:*)", "Bash(git reset:*)", "Bash(where java)", "<PERSON><PERSON>(mkdir:*)", "Bash(gradlew.bat assembleDebug:*)", "<PERSON><PERSON>(dir)", "Bash(cp:*)", "Bash(git checkout:*)"], "deny": []}}