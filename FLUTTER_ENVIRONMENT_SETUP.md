# Flutter环境配置指南

## 当前环境状态

### ✅ 已确认的配置
- **Flutter SDK**: `D:\flutter` (版本 3.24.0)
- **Android SDK**: `C:\Users\<USER>\AppData\Local\Android\Sdk`
- **项目配置**: `android/local.properties` 已正确配置

### 📋 需要的环境变量

#### 系统环境变量 (用户级别)
```
FLUTTER_HOME = D:\flutter
ANDROID_HOME = C:\Users\<USER>\AppData\Local\Android\Sdk
ANDROID_SDK_ROOT = C:\Users\<USER>\AppData\Local\Android\Sdk
```

#### PATH环境变量添加
```
D:\flutter\bin
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools
C:\Users\<USER>\AppData\Local\Android\Sdk\tools
C:\Users\<USER>\AppData\Local\Android\Sdk\cmdline-tools\latest\bin
```

## 🔧 手动配置步骤

### 方法1: 通过系统设置 (推荐)
1. 右键"此电脑" → "属性" → "高级系统设置"
2. 点击"环境变量"
3. 在"用户变量"中添加:
   - `FLUTTER_HOME` = `D:\flutter`
   - `ANDROID_HOME` = `C:\Users\<USER>\AppData\Local\Android\Sdk`
   - `ANDROID_SDK_ROOT` = `C:\Users\<USER>\AppData\Local\Android\Sdk`
4. 编辑用户变量中的"Path"，添加:
   - `D:\flutter\bin`
   - `C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools`
   - `C:\Users\<USER>\AppData\Local\Android\Sdk\tools`

### 方法2: 通过PowerShell (临时)
```powershell
# 设置当前会话环境变量
$env:FLUTTER_HOME="D:\flutter"
$env:ANDROID_HOME="C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:ANDROID_SDK_ROOT="C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH="D:\flutter\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;$env:PATH"
```

### 方法3: 通过PowerShell (永久)
```powershell
# 设置用户环境变量 (永久)
[Environment]::SetEnvironmentVariable("FLUTTER_HOME", "D:\flutter", "User")
[Environment]::SetEnvironmentVariable("ANDROID_HOME", "C:\Users\<USER>\AppData\Local\Android\Sdk", "User")
[Environment]::SetEnvironmentVariable("ANDROID_SDK_ROOT", "C:\Users\<USER>\AppData\Local\Android\Sdk", "User")

# 更新PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
$newPath = "D:\flutter\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;$currentPath"
[Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
```

## 🧪 验证环境

配置完成后，重启终端并运行以下命令验证:

```bash
# 检查Flutter版本
flutter --version

# 检查环境状态
flutter doctor

# 检查项目依赖
flutter pub get

# 检查可用设备
flutter devices
```

## 📱 项目特定配置

### pubspec.yaml 要求
- **Flutter版本**: >=3.24.0
- **Dart版本**: >=3.5.0 <4.0.0

### 关键依赖
- `flutter_riverpod: ^2.5.1` (状态管理)
- `go_router: ^16.0.0` (路由)
- `google_mlkit_text_recognition: ^0.15.0` (ML Kit)

## ⚠️ 注意事项

1. **Flutter版本**: 当前版本(3.24.0)已经363天未更新，建议运行 `flutter upgrade`
2. **Android SDK**: 确保安装了必要的Android SDK组件
3. **网络问题**: 如果遇到网络连接问题，可能需要配置代理或镜像源

## 🚀 下一步

1. 配置环境变量
2. 重启终端/IDE
3. 运行 `flutter doctor` 检查状态
4. 运行 `flutter pub get` 安装依赖
5. 运行 `flutter run` 启动应用
