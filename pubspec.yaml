name: loadguard
description: "装运卫士 - 专业级货物装运管理应用"
publish_to: 'none'

version: 2.0.0+200

environment:
  sdk: '>=3.5.0 <4.0.0'
  flutter: '>=3.24.0'

dependencies:
  flutter:
    sdk: flutter

  # 🎯 状态管理 - 稳定版本
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # 🎨 UI组件
  cupertino_icons: ^1.0.8

  # 🧭 路由系统 - 稳定版本
  go_router: ^16.0.0

  # 💾 数据存储 - 添加Hive支持
  shared_preferences: ^2.2.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.3
  path_provider_android: ^2.2.1

  # 🔐 权限和设备信息
  permission_handler: ^12.0.1
  device_info_plus: ^11.5.0
  package_info_plus: ^8.0.0

  # 📷 图像处理
  image_picker: ^1.1.2
  image: ^4.2.0

  # 🌐 网络请求
  http: ^1.2.1
  dio: ^5.5.0
  connectivity_plus: ^6.0.3

  # 🔒 加密和安全
  crypto: ^3.0.3
  encrypt: ^5.0.3

  # 🤖 ML Kit - 严格保持0.15.0版本
  google_mlkit_text_recognition: ^0.15.0

  # 📄 PDF和文档
  pdf: ^3.11.0
  printing: ^5.14.2

  # 📊 图表和数据可视化
  fl_chart: ^1.0.0
  qr_flutter: ^4.1.0

  # 🌍 国际化
  intl: ^0.20.2

  # 🔧 工具库
  json_annotation: ^4.8.1
  async: ^2.11.0
  collection: ^1.17.2
  share_plus: ^11.0.0

  # 🎯 数据类生成
  freezed_annotation: ^2.4.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # 🔧 代码生成工具 - 稳定版本
  build_runner: ^2.4.13
  json_serializable: ^6.8.0
  riverpod_generator: ^2.4.3
  freezed: ^2.5.7

  # 📝 代码检查 - 稳定版本
  flutter_lints: ^4.0.0

  # 🧪 测试工具
  mockito: ^5.4.4

flutter:
  uses-material-design: true
  
  # 字体配置
  fonts:
    - family: NotoSansSC
      fonts:
        - asset: assets/fonts/NotoSansSC-Regular.ttf
        - asset: assets/fonts/NotoSansSC-Bold.ttf
          weight: 700
  
  # 资源文件
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/config/
    - assets/models/
