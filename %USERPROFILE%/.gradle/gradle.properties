# Gradleå¨å±éç½® - ä¼åç½ç»è¿æ¥åæå»ºæ§è½

# ç½ç»è¿æ¥ä¼å
systemProp.http.keepAlive=true
systemProp.http.maxConnections=10
systemProp.http.maxConnectionsPerRoute=5

# è¿æ¥è¶æ¶è®¾ç½®ï¼æ¯«ç§ï¼
systemProp.http.connectionTimeout=60000
systemProp.http.socketTimeout=60000

# ä»£çè®¾ç½®ï¼å¦æéè¦ï¼
# systemProp.http.proxyHost=127.0.0.1
# systemProp.http.proxyPort=7890
# systemProp.https.proxyHost=127.0.0.1
# systemProp.https.proxyPort=7890

# SSL/TLSä¼å
systemProp.https.protocols=TLSv1.2,TLSv1.3
systemProp.jsse.enableSNIExtension=true

# æå»ºæ§è½ä¼å
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true

# JVMåå­ä¼å
org.gradle.jvmargs=-Xmx2048m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# Androidæå»ºä¼å
android.useAndroidX=true
android.enableJetifier=true
android.enableR8.fullMode=true

# Kotlinç¼è¯ä¼å
kotlin.incremental=true
kotlin.incremental.android=true
kotlin.code.style=official

# Flutterç¸å³
flutter.compileSdkVersion=34
flutter.targetSdkVersion=34
flutter.ndkVersion=25.1.8937393