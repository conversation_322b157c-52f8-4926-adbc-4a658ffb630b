// 全局Gradle初始化脚本 - 配置国内镜像源
allprojects {
    repositories {
        // 阿里云镜像源（优先）
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        
        // 华为云镜像源
        maven { url 'https://repo.huaweicloud.com/repository/maven/' }
        
        // 腾讯云镜像源
        maven { url 'https://mirrors.tencent.com/nexus/repository/maven-public/' }
        
        // 官方源作为备用
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

// 专门为buildscript配置镜像源
allprojects {
    buildscript {
        repositories {
            // 阿里云镜像源（优先）
            maven { url 'https://maven.aliyun.com/repository/public' }
            maven { url 'https://maven.aliyun.com/repository/google' }
            maven { url 'https://maven.aliyun.com/repository/central' }
            maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
            
            // 华为云镜像源
            maven { url 'https://repo.huaweicloud.com/repository/maven/' }
            
            // 腾讯云镜像源
            maven { url 'https://mirrors.tencent.com/nexus/repository/maven-public/' }
            
            // 官方源作为备用
            google()
            mavenCentral()
            gradlePluginPortal()
        }
    }
}

// 设置Gradle守护进程参数优化网络连接
gradle.allprojects {
    repositories.all { ArtifactRepository repo ->
        if (repo instanceof MavenArtifactRepository) {
            def url = repo.url.toString()
            if (url.startsWith('https://repo1.maven.org/maven2') || url.startsWith('https://repo.maven.apache.org/maven2')) {
                // 将Maven中央仓库替换为阿里云镜像
                project.logger.lifecycle "Repository ${repo.url} replaced by Aliyun Maven mirror."
                remove repo
                project.repositories {
                    maven { url 'https://maven.aliyun.com/repository/central' }
                }
            }
        }
    }
}