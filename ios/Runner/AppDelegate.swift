import UIKit
import Flutter
import MLKitTextRecognition
import <PERSON><PERSON><PERSON><PERSON>V<PERSON>

@UIApplicationMain
class AppDelegate: FlutterAppDelegate {
    private var mlKitTextRecognizer: TextRecognizer? = nil
    private let channel = "com.example.loadguard/text_recognition"
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
        let methodChannel = FlutterMethodChannel(
            name: channel,
            binaryMessenger: controller.binaryMessenger
        )
        
        methodChannel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
            switch call.method {
            case "initializeTextRecognizer":
                self?.initializeTextRecognizer(result: result)
                
            case "recognizeText":
                guard let args = call.arguments as? [String: Any],
                      let imagePath = args["imagePath"] as? String else {
                    result(FlutterError(code: "INVALID_ARGUMENT",
                                      message: "Image path is required",
                                      details: nil))
                    return
                }
                
                self?.recognizeText(imagePath: imagePath, result: result)
                
            case "healthCheck":
                // 健康检查方法 - 验证原生服务可用性
                let isReady = self?.mlKitTextRecognizer != nil
                result(isReady)
                
            case "disposeTextRecognizer":
                self?.disposeTextRecognizer()
                result(nil)
                
            default:
                result(FlutterMethodNotImplemented)
            }
        }
        
        GeneratedPluginRegistrant.register(with: self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    private func initializeTextRecognizer(result: @escaping FlutterResult) {
        do {
            // 🔧 修复：使用0.15.0兼容的API
            print("🚀 初始化 Google ML Kit Text Recognizer (V2 0.15.0)")
            
            // 使用Google ML Kit的文本识别器
            mlKitTextRecognizer = TextRecognizer.textRecognizer()
            
            print("✅ Google ML Kit TextRecognizer 初始化成功")
            result(true)
        } catch {
            print("❌ TextRecognizer 初始化失败: \(error.localizedDescription)")
            result(FlutterError(code: "INITIALIZATION_ERROR",
                              message: error.localizedDescription,
                              details: nil))
        }
    }
    
    private func recognizeText(imagePath: String, result: @escaping FlutterResult) {
        guard let textRecognizer = mlKitTextRecognizer else {
            result(FlutterError(code: "NOT_INITIALIZED",
                              message: "Text recognizer not initialized",
                              details: nil))
            return
        }
        
        let startTime = Date()
        
        do {
            // 创建输入图像
            let image = UIImage(contentsOfFile: imagePath)
            guard let image = image else {
                result(FlutterError(code: "INVALID_IMAGE",
                                  message: "Cannot load image from path",
                                  details: nil))
                return
            }
            
            let visionImage = VisionImage(image: image)
            
            // 执行识别
            textRecognizer.process(visionImage) { visionText, error in
                if let error = error {
                    result(FlutterError(code: "RECOGNITION_ERROR",
                                      message: error.localizedDescription,
                                      details: nil))
                    return
                }
                
                guard let visionText = visionText else {
                    result(FlutterError(code: "RECOGNITION_ERROR",
                                      message: "No text recognized",
                                      details: nil))
                    return
                }
                
                // 计算处理时间
                let processingTime = Date().timeIntervalSince(startTime) * 1000
                print("✅ 文本识别完成，耗时: \(String(format: "%.2f", processingTime)) ms")
                
                var resultMap: [String: Any] = [:]
                resultMap["text"] = visionText.text
                
                let blocks = visionText.blocks.map { block -> [String: Any] in
                    let boundingBox = block.frame
                    return [
                        "text": block.text,
                        "confidence": 0.9, // ML Kit不提供置信度，使用默认值
                        "boundingBox": [
                            "left": boundingBox.origin.x,
                            "top": boundingBox.origin.y,
                            "right": boundingBox.origin.x + boundingBox.size.width,
                            "bottom": boundingBox.origin.y + boundingBox.size.height
                        ]
                    ]
                }
                resultMap["blocks"] = blocks
                resultMap["processingTime"] = processingTime
                resultMap["engineUsed"] = "ML Kit Text Recognition v2"
                
                result(resultMap)
            }
        } catch {
            result(FlutterError(code: "PROCESSING_ERROR",
                              message: error.localizedDescription,
                              details: nil))
        }
    }
    
    private func disposeTextRecognizer() {
        mlKitTextRecognizer = nil
        print("🗑️ ML Kit TextRecognizer 资源已释放")
    }
}
