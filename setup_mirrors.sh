#!/bin/bash

# 装运卫士 - Flutter镜像源配置脚本

echo "🚀 配置Flutter国内镜像源..."

# 设置环境变量
export PUB_HOSTED_URL=https://pub.flutter-io.cn
export FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn

echo "📝 添加镜像源到环境配置..."
echo "" >> ~/.bashrc
echo "# Flutter 国内镜像源配置" >> ~/.bashrc
echo "export PUB_HOSTED_URL=https://pub.flutter-io.cn" >> ~/.bashrc
echo "export FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn" >> ~/.bashrc

echo "🧹 清理旧缓存..."
rm -rf .dart_tool/
rm -rf build/
rm -f pubspec.lock

echo "📦 使用镜像源获取依赖..."
source ~/.bashrc
dart pub get

echo "✅ 镜像源配置完成！"
echo "📋 当前镜像配置："
echo "   PUB_HOSTED_URL: $PUB_HOSTED_URL"
echo "   FLUTTER_STORAGE_BASE_URL: $FLUTTER_STORAGE_BASE_URL"